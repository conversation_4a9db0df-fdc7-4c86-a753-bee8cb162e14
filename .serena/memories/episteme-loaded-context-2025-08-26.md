# Episteme Project Context - Loaded August 26, 2025

## Project Overview
**Episteme** (formerly CCL - Contextual Code Library) is a cloud-native, AI-powered code analysis and intelligence platform providing:
- Advanced multi-language code analysis with AST parsing (19+ languages)
- Pattern detection and mining capabilities with ML/AI
- Query intelligence for natural language code search
- Pattern marketplace for sharing and monetization
- Real-time collaboration features
- Comprehensive web interface

## Current Platform Status (August 2025)

### Service Readiness Matrix
| Service | Status | Port | Production URL | Notes |
|---------|--------|------|----------------|-------|
| **Analysis Engine** | ✅ **PRODUCTION** | 8001 | https://analysis-engine-572735000332.us-central1.run.app | 17K-65K LOC/s throughput |
| **Query Intelligence** | ✅ **PRODUCTION** | 8002 | Live service | 95% PRD alignment |
| **Pattern Mining** | 🟡 **CRITICAL BLOCKERS** | 8003 | Dev only | Import failures, 2-4 weeks to fix |
| **Marketplace** | ❌ **NON-OPERATIONAL** | N/A | Not running | Compilation errors |
| **Collaboration** | 📋 **PLANNING** | 8005 | Identity issues | Service confusion |
| **Web Frontend** | 📋 **NOT IMPLEMENTED** | N/A | Not implemented | Planning phase |

### Technology Stack by Service
- **Rust** (analysis-engine): Axum, Tokio, Tree-sitter, 150+ files compiled
- **Python** (query-intelligence, pattern-mining): FastAPI, Gemini AI, PyTorch, Poetry
- **Go** (marketplace): Gin framework, needs compilation fixes
- **TypeScript** (collaboration, web): Socket.io, Next.js, React 18

## Environment Configuration

### Development Environment
```bash
# Local service URLs
ANALYSIS_SERVICE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004
COLLABORATION_SERVICE_URL=http://localhost:8005
WEB_SERVICE_URL=http://localhost:3001
```

### Infrastructure
- **Cloud Platform**: Google Cloud Platform exclusively
- **Databases**: Cloud Spanner (primary), BigQuery (analytics), Firestore (real-time), Redis (caching)
- **Container Platform**: Cloud Run with Docker
- **Build System**: Cloud Build replacing GitHub Actions
- **Monitoring**: Prometheus + Grafana + Cloud Monitoring

## Key Project Files

### Essential Reading Order
1. **CLAUDE.md** - Complete project context and AI instructions
2. **PLANNING.md** - Architecture overview and development standards  
3. **TASK.md** - Current tasks and sprint progress
4. **README.md** - Developer onboarding guide
5. **docs/platform/status.md** - Canonical service status

### Directory Structure
```
episteme/
├── services/              # 9 microservices
│   ├── analysis-engine/   # ✅ Production-ready Rust service
│   ├── query-intelligence/# ✅ Production Python AI service
│   ├── pattern-mining/    # 🚨 Critical import failures
│   ├── marketplace/       # ❌ Compilation errors  
│   ├── collaboration/     # 📋 Planning phase
│   └── web/              # 📋 Not implemented
├── PRPs/                 # Product Requirements Prompts
├── docs/                 # Platform documentation
├── scripts/              # Automation and security scripts
├── infrastructure/       # Cloud infrastructure configs
├── contracts/            # API contracts and schemas
└── tests/               # Integration and performance tests
```

## Development Commands

### Make Commands
```bash
make setup          # Set up development environment
make start          # Start all services
make test           # Run all tests
make lint           # Run all linters
make build          # Build all services
make health         # Check service health
```

### Service-Specific
```bash
make build-svc SVC=analysis-engine
make up SVC=pattern-mining
make logs SVC=query-intelligence
```

## Performance Achievements
- **Analysis Engine**: 17,346-65,694 LOC/second (5.2x-19.7x minimum)
- **Scale**: 9.1M LOC processed successfully
- **Languages**: 19+ languages with 90.7% success rate
- **Response Time**: <25ms API endpoints

## Security & Compliance
- **SOC2**: 100% (31/31 controls)
- **GDPR**: 92/100 score
- **Security Vulnerabilities**: 0 critical, 0 high
- **JWT Authentication**: Production-ready with key rotation

## Critical Issues & Priorities

### P0 - Critical (Immediate)
1. **Pattern-Mining**: Fix Google Cloud BigQuery import failures
2. **Marketplace**: Resolve compilation errors to restore service
3. **Collaboration**: Address service identity confusion

### P1 - High (2 weeks)  
4. **Infrastructure**: Add docker-compose.yml to all services
5. **Documentation**: Complete missing service docs
6. **Dependencies**: Standardize package management

## Claude Code Integration
- **Claude Flow**: Auto topology, parallel execution enabled
- **SPARC Development**: Complete TDD workflow system
- **Agent Coordination**: 54 specialized agents available
- **Performance**: Max 10 agents, hierarchical topology

## Environment Variables
- Uses `.env.example` as template (208 variables)
- Development: `.env.development` 
- Production: Google Cloud Secret Manager
- Security: Never commit real secrets to version control

## Active Development Status
- **Phase**: Production hardening and service completion
- **Sprint Focus**: Resolving critical service blockers
- **Team Structure**: AI-assisted development with multi-agent coordination
- **Quality Gates**: 8-step validation cycle with evidence requirements

This context provides the foundation for understanding Episteme's current state, architecture, and immediate development priorities.