FROM python:3.11-slim

WORKDIR /app

# Install only the absolute essentials
RUN pip install --no-cache-dir fastapi==0.115.6 uvicorn[standard]==0.34.0

# Create a simple health-only app
COPY <<'EOF' main.py
from fastapi import FastAPI
app = FastAPI(title="Pattern Mining Service - Health Check")

@app.get("/")
def root():
    return {"status": "healthy", "service": "pattern-mining"}

@app.get("/health")
def health():
    return {"status": "healthy", "timestamp": "2025-08-26T19:00:00Z"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
EOF

# Set environment variables
ENV PORT=8080
ENV ENVIRONMENT=production
ENV PYTHONUNBUFFERED=1

# Expose port
EXPOSE 8080

# Run the application
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]