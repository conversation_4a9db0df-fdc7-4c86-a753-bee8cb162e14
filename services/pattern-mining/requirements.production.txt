# Pattern Detection Service - Production Requirements
# Optimized for Linux AMD64 architecture (Google Cloud Run)
# Python 3.11 required for optimal compatibility
# 
# CRITICAL COMPATIBILITY NOTES:
# - numpy==1.26.4: Compatible with scikit-learn 1.5.2 on AMD64
# - pandas==2.2.3: Stable version with good AMD64 support
# - scikit-learn==1.5.2: Optimized for AMD64 architecture
# - jsonschema==4.24.0: Stable version without conflicts
# - redis==5.2.0: Latest stable with vector search support
# - asyncpg==0.30.0: PostgreSQL async driver for production
# - sqlalchemy==2.0.36: Latest stable ORM
#
# All versions tested for compatibility on Linux AMD64 (Google Cloud Run)

# Web Framework - Core FastAPI Stack
fastapi==0.115.0
uvicorn[standard]==0.32.0
httpx==0.28.0
websockets==13.0
aiohttp==3.10.0

# Data Validation & Serialization
pydantic==2.9.0
pydantic-settings==2.5.0
jsonschema==4.24.0
orjson==3.10.0  # Fast JSON for production
msgpack==1.1.0  # Binary serialization

# Database - Production Ready
sqlalchemy==2.0.36
asyncpg==0.30.0
alembic==1.14.0

# Google Cloud Services - Production stable versions
google-cloud-bigquery==3.25.0
google-cloud-bigquery-storage==2.25.0
google-cloud-aiplatform==1.68.0
google-generativeai==0.8.2
google-cloud-storage==2.18.0
google-cloud-pubsub==2.22.0
google-cloud-spanner==3.48.0
google-cloud-monitoring==2.21.0
google-cloud-secret-manager==2.20.0
google-auth==2.34.0
google-auth-oauthlib==1.2.1
google-auth-httplib2==0.2.0

# Data Science Core - AMD64 Compatible versions
numpy==1.26.4  # CRITICAL: Compatible with scikit-learn 1.5.2
pandas==2.2.3  # CRITICAL: Stable version with good AMD64 support
joblib==1.4.2   # Parallel processing and caching
scipy>=1.13.0   # Scientific computing (compatible with numpy 1.26.4)

# Machine Learning - AMD64 Optimized
scikit-learn==1.5.2  # CRITICAL: Optimized for AMD64 architecture

# AST Analysis & Code Parsing
libcst==1.8.2
tree-sitter==0.23.0  # Compatible version for Python 3.11
ast-grep-py==0.26.0
astor==0.8.1

# Graph Analysis
networkx==3.4

# Pattern Discovery - Essential clustering
hdbscan==0.8.40
umap-learn==0.5.7

# Security Analysis Tools
semgrep==1.90.0
bandit==1.8.0
safety==3.3.0

# Security & Authentication
pyjwt==2.9.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.9
python-jose[cryptography]==3.3.0
cryptography==43.0.0
bleach==6.1.0
html5lib==1.1

# Async Utilities
aiocache==0.12.3
aiofiles==24.1.0
asyncio-throttle==1.0.2
aioredis==2.0.1

# Monitoring & Observability - Compatible versions for production
opentelemetry-api==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-instrumentation-fastapi==0.47b0
opentelemetry-instrumentation-requests==0.47b0
opentelemetry-instrumentation-sqlalchemy==0.47b0
opentelemetry-instrumentation-redis==0.47b0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-exporter-gcp-trace==1.6.0
prometheus-fastapi-instrumentator==7.0.0
prometheus-client==0.21.0
structlog==24.4.0

# Caching & Storage
redis==5.2.0  # CRITICAL: Latest with vector search support
hiredis==2.4.0  # Redis performance optimizations
motor==3.6.0    # Async MongoDB driver
minio==7.2.0    # Object storage client

# Utilities
pyyaml==6.0.2
python-dotenv==1.0.1
rich==13.5.2
typer==0.13.0
click==8.1.7
tqdm==4.67.0
psutil==5.9.8  # System monitoring

# Production Web Server Options
gunicorn==21.2.0  # Production WSGI server
gevent==24.2.1    # Async support for gunicorn

# Additional production dependencies for robust deployment
wheel>=0.42.0      # For reliable package building
setuptools>=68.0.0 # Build tools