apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: pattern-mining-prod
  labels:
    service: pattern-mining
    environment: production
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-throttling: "false"
    run.googleapis.com/startup-cpu-boost: "true"
    autoscaling.knative.dev/minScale: "3"
    autoscaling.knative.dev/maxScale: "100"
    run.googleapis.com/timeout: "300"
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "3"
        autoscaling.knative.dev/maxScale: "100"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/startup-cpu-boost: "true"
        run.googleapis.com/timeout: "300"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - name: pattern-mining
        image: gcr.io/vibe-match-463114/pattern-mining:production
        ports:
        - name: http1
          containerPort: 8003
        env:
        # Application Configuration
        - name: ENVIRONMENT
          value: "production"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8003"
        - name: APP_NAME
          value: "pattern-mining"
        - name: APP_VERSION
          value: "1.0.0"
        - name: LOG_LEVEL
          value: "INFO"
        - name: LOG_FORMAT
          value: "json"
        - name: DEBUG
          value: "false"
        - name: WORKERS
          value: "1"
        
        # Security Configuration
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: secret-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: jwt-secret
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: session-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: encryption-key
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: api-key
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: admin-api-key
        - name: JWT_ISSUER
          value: "ccl-platform"
        - name: JWT_AUDIENCE
          value: "ccl-platform"
        
        # Database Configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: database-url
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: postgres-password
        - name: DATABASE_POOL_SIZE
          value: "20"
        - name: DATABASE_MAX_OVERFLOW
          value: "40"
        - name: DATABASE_ECHO
          value: "false"
        
        # Redis Configuration
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: redis-url
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: redis-password
        - name: REDIS_SSL
          value: "true"
        - name: REDIS_MAX_CONNECTIONS
          value: "100"
        - name: REDIS_CONNECTION_TIMEOUT
          value: "10.0"
        - name: REDIS_SOCKET_TIMEOUT
          value: "10.0"
        - name: REDIS_HEALTH_CHECK_INTERVAL
          value: "30.0"
        - name: REDIS_MAX_RETRIES
          value: "5"
        - name: REDIS_RETRY_BACKOFF_FACTOR
          value: "2.0"
        
        # GCP Configuration
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_LOCATION
          value: "us-central1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        
        # Gemini API Configuration
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: gemini-api-key
        - name: GEMINI_DEFAULT_MODEL
          value: "gemini-2.5-pro"
        - name: GEMINI_TEMPERATURE
          value: "0.1"
        - name: GEMINI_TOP_P
          value: "0.95"
        - name: GEMINI_TOP_K
          value: "40"
        - name: GEMINI_MAX_OUTPUT_TOKENS
          value: "8192"
        - name: GEMINI_REQUESTS_PER_MINUTE
          value: "300"
        - name: GEMINI_TOKENS_PER_MINUTE
          value: "4000000"
        - name: GEMINI_CONCURRENT_REQUESTS
          value: "20"
        
        # BigQuery Configuration
        - name: BIGQUERY_DATASET_ID
          value: "pattern_mining_prod"
        - name: BIGQUERY_LOCATION
          value: "US"
        - name: BIGQUERY_TABLE_PREFIX
          value: "pattern_mining"
        - name: BIGQUERY_MAX_CONNECTIONS
          value: "20"
        - name: BIGQUERY_CONNECTION_TIMEOUT
          value: "60"
        - name: BIGQUERY_READ_TIMEOUT
          value: "600"
        - name: BIGQUERY_USE_CACHE
          value: "true"
        
        # Performance Configuration
        - name: ENABLE_CACHING
          value: "true"
        - name: CACHE_TTL_SECONDS
          value: "3600"
        - name: MAX_REQUEST_SIZE
          value: "52428800"  # 50MB
        - name: RESPONSE_TIMEOUT
          value: "25"
        - name: WORKER_TIMEOUT
          value: "120"
        - name: MAX_PATTERNS_PER_REQUEST
          value: "500"
        - name: PATTERN_CONFIDENCE_THRESHOLD
          value: "0.7"
        - name: ENABLE_STREAMING
          value: "true"
        
        # Rate Limiting
        - name: RATE_LIMIT_PER_MINUTE
          value: "1000"
        - name: RATE_LIMIT_BURST
          value: "100"
        
        # Monitoring Configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: ENABLE_TRACING
          value: "true"
        - name: METRICS_PORT
          value: "9090"
        - name: JAEGER_ENDPOINT
          value: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/traces"
        
        # ML Configuration
        - name: ML_MODEL_STORAGE_PATH
          value: "/tmp/models"
        - name: ML_BATCH_SIZE
          value: "64"
        - name: ML_MAX_SEQUENCE_LENGTH
          value: "1024"
        - name: EMBEDDING_MODEL
          value: "sentence-transformers/all-MiniLM-L6-v2"
        - name: EMBEDDING_DIMENSION
          value: "384"
        
        # Feature Flags
        - name: ENABLE_GPU
          value: "false"
        
        # WebSocket Configuration
        - name: WEBSOCKET_MAX_CONNECTIONS
          value: "5000"
        - name: WEBSOCKET_HEARTBEAT_INTERVAL
          value: "30"
        
        # Analytics Configuration
        - name: ANALYTICS_RETENTION_DAYS
          value: "180"
        - name: ENABLE_REAL_TIME_ANALYTICS
          value: "true"
        
        # Batch Processing
        - name: MAX_BATCH_SIZE
          value: "500"
        - name: BATCH_TIMEOUT_SECONDS
          value: "3600"
        
        # CORS Configuration
        - name: CORS_ORIGINS
          value: "https://episteme-frontend.app,https://api.episteme.app"
        
        resources:
          requests:
            cpu: "2000m"
            memory: "4Gi"
          limits:
            cpu: "4000m"
            memory: "8Gi"
        
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        
        # Startup probe
        startupProbe:
          httpGet:
            path: /health/startup
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
          successThreshold: 1
        
        # Liveness probe
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 8003
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        # Readiness probe
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 8003
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
      
      volumes:
      - name: google-cloud-key
        secret:
          secretName: pattern-mining-gcp-key