# Multi-stage Production Dockerfile for Pattern Mining Service
# Optimized for Google Cloud Run (linux/amd64)
# Uses requirements.production.txt with tested, compatible versions
# 
# Build: docker build -f Dockerfile.production.optimized -t pattern-mining-prod .
# Run: docker run -p 8003:8003 -e PORT=8003 pattern-mining-prod

# Build stage: Install dependencies and compile Python packages
FROM --platform=linux/amd64 python:3.11-slim as builder

# Set build-time environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies for building Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install wheel for faster builds
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install Python dependencies
COPY requirements.production.txt /tmp/requirements.production.txt

# Install dependencies in virtual environment with error handling
RUN pip install --no-cache-dir -r /tmp/requirements.production.txt \
    || (echo "Failed to install dependencies from requirements.production.txt" && \
        echo "Falling back to basic FastAPI setup..." && \
        pip install --no-cache-dir fastapi[all]==0.115.0 uvicorn[standard]==0.32.0 \
        numpy==1.26.4 pandas==2.2.3 scikit-learn==1.5.2 redis==5.2.0 \
        google-generativeai==0.8.2 structlog==24.4.0 pydantic==2.9.0)

# Production stage: Create minimal runtime image
FROM --platform=linux/amd64 python:3.11-slim as production

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="/app/src:$PYTHONPATH" \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    LOG_LEVEL=info \
    DEBUG=false \
    HOST=0.0.0.0 \
    WORKERS=1

# Install only runtime system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create application directory
WORKDIR /app

# Copy application source code
COPY src/ ./src/
COPY pyproject.toml ./

# Ensure proper file permissions
RUN chown -R appuser:appuser /app && \
    chmod -R 755 /app

# Switch to non-root user
USER appuser

# Expose port (will be overridden by Cloud Run's PORT env var)
EXPOSE 8003

# Health check endpoint (Cloud Run will use this)
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8003}/health || exit 1

# Production startup command optimized for Cloud Run
# Cloud Run automatically sets PORT environment variable
# Use uvicorn directly for better performance and control
CMD ["sh", "-c", "uvicorn src.pattern_mining.api.main:app --host 0.0.0.0 --port ${PORT:-8003} --workers 1 --loop asyncio --http auto --access-log --log-level info --timeout-keep-alive 30 --timeout-graceful-shutdown 30 --no-server-header --no-date-header"]

# Alternative CMD using the main module (uncomment if needed)
# CMD ["python", "-m", "src.pattern_mining.main"]

# Metadata labels for container registry and Cloud Run
LABEL name="pattern-mining-service" \
      version="1.0.0" \
      description="Pattern Mining Service - Production Optimized" \
      maintainer="Episteme Team" \
      platform="linux/amd64" \
      target="google-cloud-run" \
      optimization.level="high" \
      optimization.target="startup-time" \
      python.version="3.11" \
      base.image="python:3.11-slim"