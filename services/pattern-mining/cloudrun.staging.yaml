apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: pattern-mining-staging
  labels:
    service: pattern-mining
    environment: staging
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "20"
    run.googleapis.com/timeout: "300"
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "20"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/timeout: "300"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 50
      timeoutSeconds: 300
      containers:
      - name: pattern-mining
        image: gcr.io/vibe-match-463114/pattern-mining:staging
        ports:
        - name: http1
          containerPort: 8003
        env:
        # Application Configuration
        - name: ENVIRONMENT
          value: "staging"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8003"
        - name: APP_NAME
          value: "pattern-mining"
        - name: APP_VERSION
          value: "1.0.0"
        - name: LOG_LEVEL
          value: "DEBUG"
        - name: LOG_FORMAT
          value: "json"
        - name: DEBUG
          value: "true"
        - name: WORKERS
          value: "1"
        
        # Security Configuration (using staging secrets)
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: secret-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: jwt-secret
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: session-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: encryption-key
        
        # Database Configuration (staging database)
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: database-url
        - name: DATABASE_POOL_SIZE
          value: "10"
        - name: DATABASE_MAX_OVERFLOW
          value: "20"
        - name: DATABASE_ECHO
          value: "true"
        
        # Redis Configuration (staging Redis)
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: redis-url
        - name: REDIS_SSL
          value: "false"
        - name: REDIS_MAX_CONNECTIONS
          value: "50"
        - name: REDIS_CONNECTION_TIMEOUT
          value: "5.0"
        - name: REDIS_SOCKET_TIMEOUT
          value: "5.0"
        
        # GCP Configuration
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_LOCATION
          value: "us-central1"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        
        # Gemini API Configuration
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-staging-secrets
              key: gemini-api-key
        - name: GEMINI_DEFAULT_MODEL
          value: "gemini-2.5-flash"
        - name: GEMINI_TEMPERATURE
          value: "0.1"
        - name: GEMINI_REQUESTS_PER_MINUTE
          value: "100"
        - name: GEMINI_TOKENS_PER_MINUTE
          value: "1000000"
        - name: GEMINI_CONCURRENT_REQUESTS
          value: "10"
        
        # BigQuery Configuration
        - name: BIGQUERY_DATASET_ID
          value: "pattern_mining_staging"
        - name: BIGQUERY_LOCATION
          value: "US"
        - name: BIGQUERY_MAX_CONNECTIONS
          value: "10"
        - name: BIGQUERY_USE_CACHE
          value: "true"
        
        # Performance Configuration (reduced for staging)
        - name: ENABLE_CACHING
          value: "true"
        - name: CACHE_TTL_SECONDS
          value: "1800"
        - name: MAX_REQUEST_SIZE
          value: "10485760"  # 10MB
        - name: RESPONSE_TIMEOUT
          value: "25"
        - name: MAX_PATTERNS_PER_REQUEST
          value: "100"
        - name: PATTERN_CONFIDENCE_THRESHOLD
          value: "0.6"
        
        # Monitoring Configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: ENABLE_TRACING
          value: "true"
        - name: METRICS_PORT
          value: "9090"
        
        # CORS Configuration
        - name: CORS_ORIGINS
          value: "https://staging.episteme.app,https://localhost:3000,https://localhost:8080"
        
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
        
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        
        # Health probes (shorter intervals for staging)
        startupProbe:
          httpGet:
            path: /health/startup
            port: 8003
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 6
        
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 8003
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      
      volumes:
      - name: google-cloud-key
        secret:
          secretName: pattern-mining-staging-gcp-key