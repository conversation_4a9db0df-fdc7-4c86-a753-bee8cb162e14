FROM python:3.11-slim

WORKDIR /app

# Copy minimal requirements first for better caching
COPY requirements.minimal.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application
COPY . .

# Set environment variables
ENV PORT=8080
ENV ENVIRONMENT=production
ENV PYTHONUNBUFFERED=1

# Expose port
EXPOSE 8080

# Run the application
CMD ["python", "-m", "uvicorn", "src.pattern_mining.main:app", "--host", "0.0.0.0", "--port", "8080"]