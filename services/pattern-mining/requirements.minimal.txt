# Minimal requirements for Pattern Mining Service
fastapi==0.115.6
uvicorn[standard]==0.34.0
pydantic==2.10.5
pydantic-settings==2.7.1
httpx==0.28.1
python-dotenv==1.0.1
prometheus-client==0.21.1
structlog==24.4.0
jsonschema==4.24.0
numpy==1.26.4
pandas==2.2.3
scikit-learn==1.5.2
redis==5.2.0
asyncpg==0.30.0
sqlalchemy==2.0.36

# Essential Google Cloud packages
google-cloud-bigquery==3.25.0
google-auth==2.34.0
aiohttp==3.10.0
google-generativeai==0.8.2
google-cloud-aiplatform==1.68.0
psutil==5.9.8