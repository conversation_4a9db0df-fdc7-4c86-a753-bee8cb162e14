"""
Python AST Transformer (production-ready)

Transforms raw Python source code into:
- Hierarchical AST (internal representation)
- Flattened AST nodes (`PatternASTNode`) using existing `ASTTransformer`
- Extracted symbols (`PatternSymbol`) with ranges and basic signatures
- File metrics (LOC, complexity estimate, counts)
- Import list and basic code chunks for embeddings

This module is dependency-free (stdlib only) and optimized for robustness over
micro-optimizations. It is designed to be called by `TransformerClient` for the
Python implementation path.
"""

import ast
import hashlib
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from .ast_transformer import ASTTransformer, AstNode
from ..contracts.models import PatternASTNode, PatternRange, PatternSymbol

logger = logging.getLogger(__name__)


# --------------------------- Public API ---------------------------

def transform_python_code(code: str, file_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Transform Python source code into a structured representation:

    Returns a dict with keys:
    - file_path, language, content_hash, size_bytes
    - ast_hierarchical: hierarchical AST (our internal node dict format)
    - ast_flattened: list[PatternASTNode] serialized as dicts
    - symbols: list[PatternSymbol] serialized as dicts
    - metrics: file-level metrics
    - imports: list of imports ({module, name, alias, level})
    - chunks: simple code chunks (functions/classes) for embeddings
    - performance: basic counters
    """
    language = "python"
    file_path = file_path or "<memory>"

    try:
        py_ast = ast.parse(code, filename=file_path, mode="exec")
    except SyntaxError as e:
        logger.error(f"Syntax error parsing {file_path}: {e}")
        raise

    # Build hierarchical AST (internal dict form)
    hierarchical_root = _build_hierarchical_ast(py_ast, code)

    # Flatten via existing transformer
    transformer = ASTTransformer()
    flattened_nodes: List[PatternASTNode] = transformer.transform(
        _dict_to_astnode(hierarchical_root)
    )

    # Extract symbols and imports
    symbols = _extract_symbols(py_ast, code)
    imports = _extract_imports(py_ast)

    # Metrics
    metrics = _compute_file_metrics(code, py_ast, symbols)

    # Chunks (functions/classes)
    chunks = _extract_chunks(py_ast, code)

    content_hash = hashlib.sha256(code.encode("utf-8")).hexdigest()

    result: Dict[str, Any] = {
        "file_path": file_path,
        "language": language,
        "content_hash": content_hash,
        "size_bytes": len(code.encode("utf-8")),
        "ast_hierarchical": hierarchical_root,
        "ast_flattened": [_pydantic_to_dict(n) for n in flattened_nodes],
        "symbols": [_pydantic_to_dict(s) for s in symbols],
        "metrics": metrics,
        "imports": imports,
        "chunks": chunks,
        "performance": transformer.get_performance_metrics(),
    }

    return result


# --------------------------- Helpers ---------------------------

def _get_pos(node: ast.AST) -> Tuple[Optional[int], Optional[int], Optional[int], Optional[int]]:
    """Return (start_line, end_line, start_column, end_column) if available."""
    start_line = getattr(node, "lineno", None)
    end_line = getattr(node, "end_lineno", None)
    start_col = getattr(node, "col_offset", None)
    end_col = getattr(node, "end_col_offset", None)
    return start_line, end_line, start_col, end_col


def _build_hierarchical_ast(node: ast.AST, code: str) -> Dict[str, Any]:
    """Convert Python ast.AST into our hierarchical dict format recursively."""
    node_type = type(node).__name__
    name = getattr(node, "name", None)

    start_line, end_line, start_col, end_col = _get_pos(node)

    # Extract a few select properties per node type for context
    properties: Dict[str, Any] = {}
    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
        properties["args_count"] = len(node.args.args) if node.args and node.args.args else 0
        properties["is_async"] = isinstance(node, ast.AsyncFunctionDef)
        properties["decorators"] = [
            _safe_unparse(d) for d in getattr(node, "decorator_list", [])
        ]
    elif isinstance(node, ast.ClassDef):
        properties["bases"] = [_safe_unparse(b) for b in node.bases]
        properties["decorators"] = [
            _safe_unparse(d) for d in getattr(node, "decorator_list", [])
        ]
    elif isinstance(node, ast.Import):
        properties["names"] = [alias.name for alias in node.names]
    elif isinstance(node, ast.ImportFrom):
        properties["module"] = node.module
        properties["level"] = node.level
        properties["names"] = [alias.name for alias in node.names]

    children: List[Dict[str, Any]] = [
        _build_hierarchical_ast(child, code) for child in ast.iter_child_nodes(node)
    ]

    node_dict: Dict[str, Any] = {
        "type": node_type,
        "name": name,
        "start_line": int(start_line) if start_line is not None else 1,
        "end_line": int(end_line) if end_line is not None else (int(start_line) if start_line else 1),
        "start_column": int(start_col) if start_col is not None else 0,
        "end_column": int(end_col) if end_col is not None else 0,
        "children": children,
        "properties": properties or {},
        "text": None,
        "annotations": None,
    }

    return node_dict


def _dict_to_astnode(d: Dict[str, Any]) -> AstNode:
    """Convert a hierarchical dict node into `AstNode` dataclass recursively (root only)."""
    node = AstNode(
        node_type=d.get("type", "unknown"),
        name=d.get("name"),
        start_line=d.get("start_line", 1),
        end_line=d.get("end_line", 1),
        start_column=d.get("start_column"),
        end_column=d.get("end_column"),
        properties=d.get("properties"),
        text=d.get("text"),
        annotations=d.get("annotations"),
    )

    node.children = [_dict_to_astnode(c) for c in d.get("children", [])]
    return node


def _extract_symbols(py_ast: ast.AST, code: str) -> List[PatternSymbol]:
    """Extract functions, methods, and classes as symbols with ranges and signatures."""
    symbols: List[PatternSymbol] = []

    class_name_stack: List[str] = []

    class Visitor(ast.NodeVisitor):
        def visit_ClassDef(self, n: ast.ClassDef):
            start, end, sc, ec = _get_pos(n)
            symbols.append(
                PatternSymbol(
                    name=n.name,
                    type=PatternSymbol.SymbolType.CLASS,
                    range=PatternRange(
                        start_line=int(start or 1),
                        end_line=int(end or (start or 1)),
                        start_column=int(sc or 0),
                        end_column=int(ec or 0),
                    ),
                )
            )
            class_name_stack.append(n.name)
            self.generic_visit(n)
            class_name_stack.pop()

        def visit_FunctionDef(self, n: ast.FunctionDef):
            _add_function_symbol(n)

        def visit_AsyncFunctionDef(self, n: ast.AsyncFunctionDef):
            _add_function_symbol(n)

    def _add_function_symbol(n: ast.AST):
        start, end, sc, ec = _get_pos(n)
        is_method = len(class_name_stack) > 0
        symbol_type = (
            PatternSymbol.SymbolType.METHOD if is_method else PatternSymbol.SymbolType.FUNCTION
        )
        symbols.append(
            PatternSymbol(
                name=getattr(n, "name", "<lambda>"),
                type=symbol_type,
                range=PatternRange(
                    start_line=int(start or 1),
                    end_line=int(end or (start or 1)),
                    start_column=int(sc or 0),
                    end_column=int(ec or 0),
                ),
                signature=_safe_signature(n),
                parameters=_safe_params(n),
            )
        )

    Visitor().visit(py_ast)
    return symbols


def _extract_imports(py_ast: ast.AST) -> List[Dict[str, Any]]:
    imports: List[Dict[str, Any]] = []

    class Visitor(ast.NodeVisitor):
        def visit_Import(self, n: ast.Import):
            for alias in n.names:
                imports.append({
                    "module": alias.name,
                    "name": alias.name,
                    "alias": alias.asname,
                    "level": 0,
                })

        def visit_ImportFrom(self, n: ast.ImportFrom):
            for alias in n.names:
                imports.append({
                    "module": n.module,
                    "name": alias.name,
                    "alias": alias.asname,
                    "level": n.level or 0,
                })

    Visitor().visit(py_ast)
    return imports


def _extract_chunks(py_ast: ast.AST, code: str) -> List[Dict[str, Any]]:
    """Extract simple chunks for functions/classes for potential embeddings."""
    chunks: List[Dict[str, Any]] = []

    class Visitor(ast.NodeVisitor):
        def visit_ClassDef(self, n: ast.ClassDef):
            _add_chunk(n, "class")
            self.generic_visit(n)

        def visit_FunctionDef(self, n: ast.FunctionDef):
            _add_chunk(n, "function")

        def visit_AsyncFunctionDef(self, n: ast.AsyncFunctionDef):
            _add_chunk(n, "function")

    def _add_chunk(n: ast.AST, kind: str):
        start, end, sc, ec = _get_pos(n)
        chunks.append({
            "type": kind,
            "name": getattr(n, "name", None),
            "range": {
                "start": {"line": int((start or 1) - 1), "column": int(sc or 0), "byte": 0},
                "end": {"line": int((end or (start or 1)) - 1), "column": int(ec or 0), "byte": 0},
            },
        })

    Visitor().visit(py_ast)
    return chunks


def _compute_file_metrics(
    code: str, py_ast: ast.AST, symbols: List[PatternSymbol]
) -> Dict[str, Any]:
    lines = code.splitlines()
    loc = sum(1 for line in lines if line.strip() and not line.strip().startswith("#"))
    total_lines = len(lines)
    function_count = sum(1 for s in symbols if s.type in (PatternSymbol.SymbolType.FUNCTION, PatternSymbol.SymbolType.METHOD))
    class_count = sum(1 for s in symbols if s.type == PatternSymbol.SymbolType.CLASS)
    complexity = _estimate_cyclomatic_complexity(py_ast)
    comment_lines = sum(1 for line in lines if line.strip().startswith("#"))
    comment_ratio = (comment_lines / loc) if loc > 0 else 0.0

    return {
        "lines_of_code": loc,
        "total_lines": total_lines,
        "complexity": complexity,
        "maintainability_index": max(0.0, 100.0 - min(100.0, complexity * 1.5)),
        "function_count": function_count,
        "class_count": class_count,
        "comment_ratio": round(comment_ratio, 3),
    }


def _estimate_cyclomatic_complexity(py_ast: ast.AST) -> int:
    """Rough cyclomatic complexity estimate based on decision points."""
    decision_nodes = (
        ast.If,
        ast.For,
        ast.AsyncFor,
        ast.While,
        ast.Try,
        ast.With,
        ast.BoolOp,
        ast.IfExp,
        ast.ExceptHandler,
        ast.Comprehension if hasattr(ast, "Comprehension") else tuple(),
    )

    class Counter(ast.NodeVisitor):
        def __init__(self):
            self.count = 1  # Base complexity

        def generic_visit(self, node):
            if isinstance(node, decision_nodes):
                self.count += 1
            super().generic_visit(node)

    counter = Counter()
    counter.visit(py_ast)
    return counter.count


def _safe_unparse(node: ast.AST) -> str:
    try:
        # ast.unparse exists in Python 3.9+
        return ast.unparse(node)  # type: ignore[attr-defined]
    except Exception:
        return "<expr>"


def _safe_signature(n: ast.AST) -> Optional[str]:
    try:
        if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef)):
            args = []
            for a in n.args.args:
                name = a.arg
                annotation = _safe_unparse(a.annotation) if getattr(a, "annotation", None) else None
                args.append(f"{name}: {annotation}" if annotation else name)
            return f"({', '.join(args)})"
        return None
    except Exception:
        return None


def _safe_params(n: ast.AST) -> Optional[List[Dict[str, str]]]:
    try:
        if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef)):
            params: List[Dict[str, str]] = []
            for a in n.args.args:
                annotation = _safe_unparse(a.annotation) if getattr(a, "annotation", None) else None
                entry: Dict[str, str] = {"name": a.arg}
                if annotation:
                    entry["type"] = annotation
                params.append(entry)
            return params
        return None
    except Exception:
        return None


def _pydantic_to_dict(model_obj: Any) -> Dict[str, Any]:
    """Serialize Pydantic model to dict safely (avoid .model_dump for compatibility)."""
    try:
        # Pydantic v1 style
        if hasattr(model_obj, "dict"):
            return model_obj.dict()
        # Fallback for simple dataclasses or objects with __dict__
        if hasattr(model_obj, "__dict__"):
            return dict(model_obj.__dict__)
    except Exception:
        pass
    return {}


