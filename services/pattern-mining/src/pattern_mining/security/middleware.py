"""
Security Middleware for Pattern Mining Service

Provides comprehensive security middleware including:
- Request validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Security headers
- Authentication and authorization
"""

import json
import time
import re
import hashlib
import secrets
from typing import Callable, Dict, Any, Optional, List, Set
from urllib.parse import parse_qs, urlparse
from datetime import datetime, timedelta
import html
import bleach
from fastapi import Request, Response, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import redis.asyncio as redis
import structlog
from .authentication import JWTAuthenticator, AuthenticationError
from .authorization import RBACAuthorizer, AccessRequest, Resource, Action
from .rate_limiting import RateLimitManager, RateLimitType
from ..config.settings import Settings

logger = structlog.get_logger()
security = HTTPBearer()


class SecurityError(Exception):
    """Base security error."""
    pass


class ValidationError(SecurityError):
    """Validation error."""
    pass


class SecurityMiddleware(BaseHTTPMiddleware):
    """Main security middleware orchestrator."""
    
    def __init__(
        self,
        app,
        settings: Settings,
        redis_client: redis.Redis,
        jwt_authenticator: JWTAuthenticator = None,
        rbac_authorizer: RBACAuthorizer = None,
        rate_limit_manager: RateLimitManager = None
    ):
        super().__init__(app)
        self.settings = settings
        self.redis_client = redis_client
        self.jwt_authenticator = jwt_authenticator
        self.rbac_authorizer = rbac_authorizer
        self.rate_limit_manager = rate_limit_manager
        
        # Initialize sub-middleware with fallback handling
        try:
            self.auth_middleware = AuthenticationMiddleware(jwt_authenticator) if jwt_authenticator else None
            self.authz_middleware = AuthorizationMiddleware(rbac_authorizer) if rbac_authorizer else None
            self.validation_middleware = ValidationMiddleware(settings)
            self.headers_middleware = SecurityHeadersMiddleware(settings)
            self.csrf_middleware = CSRFProtectionMiddleware(redis_client) if redis_client else None
            
            # Track which components are available
            self.has_auth = jwt_authenticator is not None
            self.has_authz = rbac_authorizer is not None
            self.has_rate_limiting = rate_limit_manager is not None
            self.has_csrf = redis_client is not None
            
            if not any([self.has_auth, self.has_authz, self.has_rate_limiting]):
                logger.warning("Security middleware running with minimal security features")
                
        except Exception as e:
            logger.error("Failed to initialize security middleware components", error=str(e))
            # Set all to None for graceful degradation
            self.auth_middleware = None
            self.authz_middleware = None
            self.csrf_middleware = None
            self.has_auth = False
            self.has_authz = False
            self.has_rate_limiting = False
            self.has_csrf = False
        
        # Security configuration
        self.blocked_user_agents = {
            "sqlmap", "nikto", "nessus", "openvas", "burp", "zap"
        }
        
        self.suspicious_patterns = [
            r"<script.*?>.*?</script>",  # XSS attempts
            r"javascript:",  # JavaScript injection
            r"vbscript:",  # VBScript injection
            r"onload=",  # Event handler injection
            r"onerror=",  # Error handler injection
            r"union\s+select",  # SQL injection
            r"or\s+1\s*=\s*1",  # SQL injection
            r"drop\s+table",  # SQL injection
            r"insert\s+into",  # SQL injection
            r"delete\s+from",  # SQL injection
            r"../",  # Directory traversal
            r"\.\.\\",  # Directory traversal (Windows)
            r"eval\(",  # Code evaluation
            r"exec\(",  # Code execution
        ]
        
        # Compile patterns for performance
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Main security middleware dispatch with graceful degradation."""
        start_time = time.time()
        user = None
        
        try:
            # Skip security checks for health endpoints
            if request.url.path.startswith("/health"):
                return await call_next(request)
            
            # 1. Basic security checks (always enabled)
            await self._check_basic_security(request)
            
            # 2. Rate limiting (if available)
            if self.has_rate_limiting:
                await self._check_rate_limits(request)
            
            # 3. Request validation (always enabled)
            if self.validation_middleware:
                await self.validation_middleware.validate_request(request)
            
            # 4. Authentication (if available)
            if self.has_auth and self.auth_middleware:
                try:
                    user = await self.auth_middleware.authenticate_request(request)
                except Exception as auth_error:
                    logger.warning("Authentication failed", error=str(auth_error))
            
            # 5. Authorization (if available and user authenticated)
            if self.has_authz and self.authz_middleware and user:
                try:
                    await self.authz_middleware.authorize_request(request, user)
                except Exception as authz_error:
                    logger.warning("Authorization failed", error=str(authz_error))
            
            # 6. CSRF protection (if available)
            if self.has_csrf and self.csrf_middleware:
                try:
                    await self.csrf_middleware.validate_csrf_token(request)
                except Exception as csrf_error:
                    logger.warning("CSRF validation failed", error=str(csrf_error))
            
            # Process request
            response = await call_next(request)
            
            # 7. Add security headers (always enabled)
            if self.headers_middleware:
                response = await self.headers_middleware.add_security_headers(response)
            
            # 8. Audit logging
            await self._audit_log(request, response, user, time.time() - start_time)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Security middleware error",
                error=str(e),
                path=request.url.path,
                method=request.method,
                client_ip=self._get_client_ip(request)
            )
            raise HTTPException(status_code=500, detail="Internal security error")
    
    async def _check_basic_security(self, request: Request) -> None:
        """Basic security checks."""
        # Check user agent
        user_agent = request.headers.get("user-agent", "").lower()
        for blocked_agent in self.blocked_user_agents:
            if blocked_agent in user_agent:
                logger.warning(
                    "Blocked user agent detected",
                    user_agent=user_agent,
                    client_ip=self._get_client_ip(request)
                )
                raise HTTPException(status_code=403, detail="Forbidden")
        
        # Check for suspicious patterns in URL
        url_path = request.url.path.lower()
        query_params = str(request.query_params).lower()
        
        for pattern in self.compiled_patterns:
            if pattern.search(url_path) or pattern.search(query_params):
                logger.warning(
                    "Suspicious pattern detected",
                    pattern=pattern.pattern,
                    path=url_path,
                    query_params=query_params,
                    client_ip=self._get_client_ip(request)
                )
                raise HTTPException(status_code=400, detail="Malicious request detected")
        
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.settings.max_request_size:
            logger.warning(
                "Request size too large",
                content_length=content_length,
                max_size=self.settings.max_request_size,
                client_ip=self._get_client_ip(request)
            )
            raise HTTPException(status_code=413, detail="Request too large")
    
    async def _check_rate_limits(self, request: Request) -> None:
        """Check rate limits."""
        client_ip = self._get_client_ip(request)
        
        # Check IP-based rate limiting
        ip_result = await self.rate_limit_manager.check_rate_limit(
            key=client_ip,
            limit_type=RateLimitType.IP,
            endpoint=request.url.path
        )
        
        if not ip_result.allowed:
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded",
                headers={
                    "X-RateLimit-Limit": str(ip_result.limit),
                    "X-RateLimit-Remaining": str(ip_result.remaining),
                    "X-RateLimit-Reset": str(ip_result.reset_time),
                    "Retry-After": str(ip_result.retry_after or ip_result.reset_time)
                }
            )
        
        # Check burst protection
        burst_result = await self.rate_limit_manager.check_burst_limit(
            key=client_ip,
            limit_type=RateLimitType.IP,
            endpoint=request.url.path
        )
        
        if not burst_result.allowed:
            raise HTTPException(
                status_code=429,
                detail="Burst limit exceeded",
                headers={
                    "X-Burst-Limit": str(burst_result.limit),
                    "Retry-After": str(burst_result.retry_after or burst_result.reset_time)
                }
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def _audit_log(
        self,
        request: Request,
        response: Response,
        user: Optional[Dict[str, Any]],
        duration: float
    ) -> None:
        """Audit logging."""
        audit_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "user_id": user.get("sub") if user else None,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "duration": duration,
            "request_size": request.headers.get("content-length"),
            "response_size": response.headers.get("content-length")
        }
        
        # Store in Redis with TTL
        audit_key = f"audit:{datetime.utcnow().strftime('%Y-%m-%d')}:{secrets.token_hex(8)}"
        await self.redis_client.setex(
            audit_key,
            86400 * 30,  # 30 days
            json.dumps(audit_data)
        )
        
        logger.info(
            "Request processed",
            **audit_data
        )


class AuthenticationMiddleware:
    """Authentication middleware."""
    
    def __init__(self, jwt_authenticator: JWTAuthenticator):
        self.jwt_authenticator = jwt_authenticator
    
    async def authenticate_request(self, request: Request) -> Optional[Dict[str, Any]]:
        """Authenticate request."""
        # Skip authentication for public endpoints
        public_endpoints = {
            "/api/v1/health",
            "/api/v1/docs",
            "/api/v1/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/register"
        }
        
        if request.url.path in public_endpoints:
            return None
        
        # Get authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header:
            # Check for API key
            api_key = request.headers.get("x-api-key")
            if api_key:
                return await self._authenticate_api_key(api_key)
            
            raise HTTPException(
                status_code=401,
                detail="Missing authorization header",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Validate bearer token
        if not auth_header.startswith("Bearer "):
            raise HTTPException(
                status_code=401,
                detail="Invalid authorization header format",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        token = auth_header.split(" ", 1)[1]
        
        try:
            payload = await self.jwt_authenticator.verify_token(token)
            return payload
        except AuthenticationError as e:
            raise HTTPException(
                status_code=401,
                detail=str(e),
                headers={"WWW-Authenticate": "Bearer"}
            )
    
    async def _authenticate_api_key(self, api_key: str) -> Dict[str, Any]:
        """Authenticate API key."""
        # This would typically validate against a database
        # For now, return a placeholder
        return {
            "sub": "api_key_user",
            "type": "api_key",
            "permissions": ["api:read", "api:write"]
        }


class AuthorizationMiddleware:
    """Authorization middleware."""
    
    def __init__(self, rbac_authorizer: RBACAuthorizer):
        self.rbac_authorizer = rbac_authorizer
    
    async def authorize_request(self, request: Request, user: Dict[str, Any]) -> None:
        """Authorize request."""
        # Map HTTP methods to actions
        method_action_map = {
            "GET": Action.READ,
            "POST": Action.CREATE,
            "PUT": Action.UPDATE,
            "PATCH": Action.UPDATE,
            "DELETE": Action.DELETE
        }
        
        # Determine resource type and ID from path
        resource_type, resource_id = self._parse_resource_from_path(request.url.path)
        
        # Create access request
        access_request = AccessRequest(
            user_id=user["sub"],
            resource_type=resource_type,
            resource_id=resource_id,
            action=method_action_map.get(request.method, Action.READ),
            context={
                "ip_address": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent"),
                "endpoint": request.url.path
            }
        )
        
        # Check authorization
        if not await self.rbac_authorizer.authorize(access_request):
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions"
            )
    
    def _parse_resource_from_path(self, path: str) -> tuple[Resource, str]:
        """Parse resource type and ID from path."""
        # Simple path parsing - in production, use proper routing
        parts = path.strip("/").split("/")
        
        if len(parts) >= 3:
            if "patterns" in parts:
                return Resource.PATTERN, parts[-1] if parts[-1] != "patterns" else "all"
            elif "models" in parts:
                return Resource.MODEL, parts[-1] if parts[-1] != "models" else "all"
            elif "analysis" in parts:
                return Resource.ANALYSIS, parts[-1] if parts[-1] != "analysis" else "all"
            elif "repositories" in parts:
                return Resource.REPOSITORY, parts[-1] if parts[-1] != "repositories" else "all"
            elif "users" in parts:
                return Resource.USER, parts[-1] if parts[-1] != "users" else "all"
        
        return Resource.API, "general"
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


class ValidationMiddleware:
    """Request validation middleware."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        
        # SQL injection patterns
        self.sql_patterns = [
            r"union\s+select",
            r"or\s+1\s*=\s*1",
            r"drop\s+table",
            r"insert\s+into",
            r"delete\s+from",
            r"update\s+set",
            r"create\s+table",
            r"alter\s+table",
            r"exec\s*\(",
            r"sp_executesql",
            r"xp_cmdshell",
            r"--",
            r"/\*.*\*/",
        ]
        
        # XSS patterns
        self.xss_patterns = [
            r"<script.*?>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"onload=",
            r"onerror=",
            r"onclick=",
            r"onmouseover=",
            r"<iframe",
            r"<object",
            r"<embed",
            r"<form",
            r"<input",
            r"<meta",
            r"<link",
        ]
        
        # Compile patterns
        self.compiled_sql_patterns = [re.compile(p, re.IGNORECASE) for p in self.sql_patterns]
        self.compiled_xss_patterns = [re.compile(p, re.IGNORECASE) for p in self.xss_patterns]
    
    async def validate_request(self, request: Request) -> None:
        """Validate request for security issues."""
        # Validate URL parameters
        for key, value in request.query_params.items():
            await self._validate_parameter(key, value, "query")
        
        # Validate headers
        for key, value in request.headers.items():
            await self._validate_header(key, value)
        
        # Validate body (if present)
        if request.method in ["POST", "PUT", "PATCH"]:
            await self._validate_body(request)
    
    async def _validate_parameter(self, key: str, value: str, param_type: str) -> None:
        """Validate parameter value."""
        # Check for SQL injection
        for pattern in self.compiled_sql_patterns:
            if pattern.search(value):
                logger.warning(
                    "SQL injection attempt detected",
                    parameter=key,
                    value=value[:100],  # Log first 100 chars
                    type=param_type,
                    pattern=pattern.pattern
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Malicious content detected in parameter: {key}"
                )
        
        # Check for XSS
        for pattern in self.compiled_xss_patterns:
            if pattern.search(value):
                logger.warning(
                    "XSS attempt detected",
                    parameter=key,
                    value=value[:100],
                    type=param_type,
                    pattern=pattern.pattern
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Malicious content detected in parameter: {key}"
                )
        
        # Check parameter length
        if len(value) > 10000:  # Configurable limit
            logger.warning(
                "Parameter too long",
                parameter=key,
                length=len(value),
                type=param_type
            )
            raise HTTPException(
                status_code=400,
                detail=f"Parameter too long: {key}"
            )
    
    async def _validate_header(self, key: str, value: str) -> None:
        """Validate header value."""
        # Skip standard headers
        skip_headers = {
            "user-agent", "accept", "accept-encoding", "accept-language",
            "connection", "host", "content-length", "content-type",
            "authorization", "x-api-key", "x-forwarded-for", "x-real-ip"
        }
        
        if key.lower() in skip_headers:
            return
        
        # Check for injection attempts in custom headers
        for pattern in self.compiled_sql_patterns + self.compiled_xss_patterns:
            if pattern.search(value):
                logger.warning(
                    "Malicious header detected",
                    header=key,
                    value=value[:100],
                    pattern=pattern.pattern
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Malicious content detected in header: {key}"
                )
    
    async def _validate_body(self, request: Request) -> None:
        """Validate request body."""
        try:
            body = await request.body()
            if not body:
                return
            
            # Check body size
            if len(body) > self.settings.max_request_size:
                raise HTTPException(
                    status_code=413,
                    detail="Request body too large"
                )
            
            # Convert to string for pattern matching
            body_str = body.decode("utf-8", errors="ignore")
            
            # Check for SQL injection
            for pattern in self.compiled_sql_patterns:
                if pattern.search(body_str):
                    logger.warning(
                        "SQL injection attempt in body",
                        pattern=pattern.pattern,
                        body_preview=body_str[:200]
                    )
                    raise HTTPException(
                        status_code=400,
                        detail="Malicious content detected in request body"
                    )
            
            # Check for XSS
            for pattern in self.compiled_xss_patterns:
                if pattern.search(body_str):
                    logger.warning(
                        "XSS attempt in body",
                        pattern=pattern.pattern,
                        body_preview=body_str[:200]
                    )
                    raise HTTPException(
                        status_code=400,
                        detail="Malicious content detected in request body"
                    )
            
        except UnicodeDecodeError:
            # Handle binary data
            pass
        except Exception as e:
            logger.error("Body validation error", error=str(e))
            raise HTTPException(
                status_code=400,
                detail="Request body validation failed"
            )


class SecurityHeadersMiddleware:
    """Security headers middleware."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    async def add_security_headers(self, response: Response) -> Response:
        """Add security headers to response."""
        # Content Security Policy
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        headers = {
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            
            # Prevent content type sniffing
            "X-Content-Type-Options": "nosniff",
            
            # XSS protection
            "X-XSS-Protection": "1; mode=block",
            
            # Content Security Policy
            "Content-Security-Policy": csp_policy,
            
            # Strict Transport Security (HTTPS only)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            
            # Referrer Policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions Policy
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            
            # Remove server information
            "Server": "Pattern Mining Service",
            
            # Cache control for sensitive data
            "Cache-Control": "no-store, no-cache, must-revalidate, private",
            "Pragma": "no-cache",
            "Expires": "0",
        }
        
        # Add CORS headers if configured
        if self.settings.cors_origins:
            headers["Access-Control-Allow-Origin"] = ", ".join(self.settings.cors_origins)
            headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-API-Key"
            headers["Access-Control-Max-Age"] = "86400"
        
        # Add headers to response
        for key, value in headers.items():
            response.headers[key] = value
        
        return response


class CSRFProtectionMiddleware:
    """CSRF protection middleware."""
    
    def __init__(self, redis_client: redis.Redis, token_ttl: int = 3600):
        self.redis_client = redis_client
        self.token_ttl = token_ttl
    
    async def generate_csrf_token(self, session_id: str) -> str:
        """Generate CSRF token for session."""
        token = secrets.token_urlsafe(32)
        token_key = f"csrf_token:{session_id}"
        
        await self.redis_client.setex(token_key, self.token_ttl, token)
        
        return token
    
    async def validate_csrf_token(self, request: Request) -> None:
        """Validate CSRF token."""
        # Skip CSRF validation for safe methods
        if request.method in ["GET", "HEAD", "OPTIONS"]:
            return
        
        # Skip for API requests with proper authentication
        if request.headers.get("authorization") or request.headers.get("x-api-key"):
            return
        
        # Get session ID (this would typically come from session middleware)
        session_id = request.headers.get("x-session-id")
        if not session_id:
            return  # Skip if no session
        
        # Get CSRF token from header or form data
        csrf_token = request.headers.get("x-csrf-token")
        
        if not csrf_token:
            # Try to get from form data
            if request.headers.get("content-type") == "application/x-www-form-urlencoded":
                body = await request.body()
                form_data = parse_qs(body.decode())
                csrf_token = form_data.get("csrf_token", [None])[0]
        
        if not csrf_token:
            raise HTTPException(
                status_code=403,
                detail="CSRF token missing"
            )
        
        # Validate token
        token_key = f"csrf_token:{session_id}"
        stored_token = await self.redis_client.get(token_key)
        
        if not stored_token or stored_token != csrf_token:
            logger.warning(
                "CSRF token validation failed",
                session_id=session_id,
                provided_token=csrf_token[:10] + "..." if csrf_token else None,
                stored_token=stored_token[:10] + "..." if stored_token else None
            )
            raise HTTPException(
                status_code=403,
                detail="CSRF token invalid"
            )
    
    async def revoke_csrf_token(self, session_id: str) -> None:
        """Revoke CSRF token for session."""
        token_key = f"csrf_token:{session_id}"
        await self.redis_client.delete(token_key)