# Pattern Mining Security Module

This security module provides comprehensive security features for the Pattern Mining service, including authentication, authorization, rate limiting, encryption, and audit logging.

## ✅ Security Issues Fixed

### Critical Issues Resolved

1. **Hardcoded Authentication (config_management.py:59)**
   - ✅ Fixed: Replaced hardcoded `"dev-user-001"` with proper JWT authentication
   - ✅ Added development mode fallback for backward compatibility
   - ✅ Implemented proper error handling and logging

2. **Incomplete Authentication System (authentication.py)**
   - ✅ Fixed: Completed JWT authentication implementation
   - ✅ Added proper token creation, verification, and revocation
   - ✅ Implemented session management and multi-factor authentication support

3. **Missing Authorization Logic (authorization.py)**
   - ✅ Fixed: Implemented complete RBAC system
   - ✅ Added role-based and resource-based permission checks
   - ✅ Created default roles and permission management

4. **Incomplete Encryption (encryption.py)**
   - ✅ Fixed: Completed encryption manager implementation
   - ✅ Added multiple encryption algorithms (AES-256-GCM, Fernet, etc.)
   - ✅ Implemented key management and rotation

5. **Missing Rate Limiting Logic (rate_limiting.py)**
   - ✅ Fixed: Implemented token bucket and sliding window algorithms
   - ✅ Added burst protection and adaptive rate limiting
   - ✅ Created per-user, per-IP, and per-endpoint rate limits

## 🏗️ Architecture Overview

```
security/
├── authentication.py      # JWT, OAuth2, MFA, Session management
├── authorization.py       # RBAC, permissions, access control
├── encryption.py          # Data encryption, key management, secrets
├── rate_limiting.py       # Rate limiting, burst protection
├── auth.py               # Auth utilities and helpers
├── security_helpers.py   # Unified security manager
├── security_test.py      # Comprehensive test suite
└── __init__.py           # Module exports
```

## 🔐 Components

### 1. Authentication (`authentication.py`)

**Features:**
- JWT token creation, verification, and revocation
- OAuth2 integration for third-party authentication
- Multi-factor authentication (TOTP)
- Session management with Redis backend
- Service account authentication

**Usage:**
```python
from pattern_mining.security import JWTAuthenticator, User, UserRole

authenticator = JWTAuthenticator(
    secret_key="your-secret-key",
    redis_client=redis_client
)

# Create user
user = User(
    id="user123",
    email="<EMAIL>",
    username="testuser",
    role=UserRole.USER,
    permissions=["read", "write"]
)

# Generate tokens
access_token = await authenticator.create_access_token(user)
refresh_token = await authenticator.create_refresh_token(user)

# Verify token
payload = await authenticator.verify_token(access_token)
```

### 2. Authorization (`authorization.py`)

**Features:**
- Role-Based Access Control (RBAC)
- Resource-specific permissions
- Default role system (admin, user, readonly, etc.)
- Context-based authorization (IP, time restrictions)

**Usage:**
```python
from pattern_mining.security import RBACAuthorizer, AccessRequest, Resource, Action

authorizer = RBACAuthorizer(role_manager, permission_manager, redis_client)

# Check access
request = AccessRequest(
    user_id="user123",
    resource_type=Resource.PATTERN,
    resource_id="pattern-456",
    action=Action.READ
)

is_authorized = await authorizer.authorize(request)
```

### 3. Rate Limiting (`rate_limiting.py`)

**Features:**
- Token bucket algorithm
- Sliding window rate limiting
- Fixed window rate limiting
- Burst protection with penalties
- Per-user, per-IP, per-endpoint limits

**Usage:**
```python
from pattern_mining.security import RateLimitManager, RateLimitType

rate_limiter = RateLimitManager(redis_client, settings)

# Check rate limit
result = await rate_limiter.check_rate_limit(
    key="user:123",
    limit_type=RateLimitType.USER,
    endpoint="/api/patterns"
)

if not result.allowed:
    # Rate limit exceeded
    retry_after = result.retry_after
```

### 4. Encryption (`encryption.py`)

**Features:**
- Multiple encryption algorithms (AES-256-GCM, Fernet, ChaCha20-Poly1305)
- Master key derivation and management
- Key rotation and lifecycle management
- Integration with Google Cloud Secret Manager
- API key management with hashing

**Usage:**
```python
from pattern_mining.security import EncryptionManager, KeyType

encryption_manager = EncryptionManager(settings, redis_client)

# Encrypt data
encrypted = await encryption_manager.encrypt_data("sensitive data")

# Decrypt data
decrypted = await encryption_manager.decrypt_data(encrypted)

# Key management
key = encryption_manager.generate_key(KeyType.DATA, EncryptionAlgorithm.AES_256_GCM)
await encryption_manager.store_key(key)
```

### 5. Security Helpers (`security_helpers.py`)

**Centralized Security Manager:**
```python
from pattern_mining.security import SecurityManager, get_security_manager

# Get global security manager
security = await get_security_manager()

# Authenticate user
user = await security.authenticate_user(jwt_token)

# Check rate limits
allowed = await security.check_rate_limit("user123", "/api/endpoint")

# Encrypt/decrypt data
encrypted = await security.encrypt_sensitive_data("secret data")
decrypted = await security.decrypt_sensitive_data(encrypted)

# Health check
health = await security.health_check()
```

## 🔧 Configuration

### Environment Variables

```bash
# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Rate Limiting
API_RATE_LIMIT=1000
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Development Mode
IS_DEVELOPMENT=false  # Set to true for development mode
```

### Settings Integration

The security module integrates with your application settings:

```python
from pattern_mining.config.settings import get_settings

settings = get_settings()

# Security configuration
security_config = {
    "secret_key": settings.secret_key,
    "redis_url": settings.redis_url,
    "is_development": getattr(settings, 'is_development', False),
    "jwt_secret": getattr(settings, 'jwt_secret', settings.secret_key),
}
```

## 🛡️ Security Best Practices Implemented

### 1. Authentication Security
- ✅ Strong JWT secret key generation
- ✅ Token expiration and refresh logic
- ✅ Token revocation support
- ✅ Session management with timeouts
- ✅ Multi-factor authentication support

### 2. Authorization Security  
- ✅ Principle of least privilege
- ✅ Role-based access control
- ✅ Resource ownership checks
- ✅ Context-based restrictions (IP, time)

### 3. Encryption Security
- ✅ Industry-standard encryption algorithms
- ✅ Proper key derivation (PBKDF2)
- ✅ Key rotation capabilities
- ✅ Secure key storage in Redis
- ✅ Master key protection

### 4. Rate Limiting Security
- ✅ Multiple rate limiting algorithms
- ✅ Burst attack protection
- ✅ Distributed rate limiting
- ✅ Adaptive rate limiting based on load
- ✅ Per-user and per-endpoint limits

### 5. General Security
- ✅ Comprehensive audit logging
- ✅ Proper error handling without information leakage
- ✅ Input validation and sanitization
- ✅ Development/production mode separation
- ✅ Health monitoring and alerting

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd /path/to/pattern-mining/src/pattern_mining/security
python security_test.py
```

The test suite covers:
- JWT authentication flows
- RBAC authorization
- Encryption/decryption operations
- Rate limiting functionality
- Integration between components
- Error handling scenarios

## 🚀 Deployment

### Development Mode
- Uses simplified token validation
- Allows development tokens like `dev-`, `admin-`, `test-`
- Provides development utilities in `auth.py`

### Production Mode
- Full JWT validation with Redis
- Proper session management
- Rate limiting enforcement
- Audit logging to Redis
- Security monitoring

### Backward Compatibility
- Development mode maintains existing behavior
- Graceful fallbacks for missing dependencies
- Progressive security enhancement

## 📚 API Reference

### Quick Start

1. **Initialize Security Manager**
   ```python
   from pattern_mining.security import get_security_manager
   
   security = await get_security_manager()
   await security.initialize_default_security()
   ```

2. **Authenticate Requests**
   ```python
   from pattern_mining.security import authenticate_request
   
   user = await authenticate_request(jwt_token)
   ```

3. **Check Permissions**
   ```python
   from pattern_mining.security import check_user_permissions
   
   has_permission = await check_user_permissions(user, ["read", "write"])
   ```

4. **Rate Limiting**
   ```python
   security = await get_security_manager()
   allowed = await security.check_rate_limit(user.id, "/api/endpoint")
   ```

## 🔍 Monitoring and Alerts

The security module provides built-in monitoring:

1. **Security Events Audit Trail**
   - All authentication attempts
   - Authorization decisions
   - Rate limit violations
   - Encryption operations

2. **Health Checks**
   - Component status monitoring
   - Redis connection health
   - Performance metrics

3. **Security Metrics**
   - Failed authentication rate
   - Authorization denial rate
   - Rate limit hit rate
   - Token revocation frequency

## 🛠️ Troubleshooting

### Common Issues

1. **Authentication Fails**
   - Check JWT secret key configuration
   - Verify Redis connectivity
   - Ensure token hasn't expired

2. **Authorization Denies Access**
   - Verify user roles are assigned
   - Check resource ownership
   - Review permission definitions

3. **Rate Limiting Too Restrictive**
   - Adjust rate limit configuration
   - Check for burst limit penalties
   - Review endpoint-specific limits

4. **Encryption Errors**
   - Verify master key derivation
   - Check key initialization
   - Ensure Redis key storage

### Debug Mode

Enable debug logging:
```python
import structlog
structlog.configure(level="DEBUG")
```

### Health Check Endpoint

Check security component health:
```python
security = await get_security_manager()
health = await security.health_check()
logger.info(f"Health status: {health}")
```

---

## 📞 Support

For security-related issues:
1. Check this documentation
2. Run the test suite to verify functionality
3. Review audit logs for security events
4. Enable debug logging for detailed information

**⚠️ Security Note**: Never log sensitive information like passwords, tokens, or encrypted data in plain text.