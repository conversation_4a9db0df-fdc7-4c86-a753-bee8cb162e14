"""
Authorization System for Pattern Mining Service

Provides comprehensive authorization including:
- Role-based access control (RBAC)
- Resource-based permissions
- API endpoint authorization
- Data access controls
- Privilege escalation prevention
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import redis.asyncio as redis
import structlog
from fastapi import HTTPE<PERSON>ception, Request
import jwt
from jwt.exceptions import InvalidTokenError, ExpiredSignatureError
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .authentication import JWTManager

logger = structlog.get_logger()


@dataclass
class AuditLog:
    """Audit log entry."""
    user_id: str
    action: str
    resource_type: str
    resource_id: str
    result: bool
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    context: Dict[str, Any] = field(default_factory=dict)


class AuditLogger:
    """Audit logging service."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def log_access_attempt(
        self,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: str,
        result: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        context: Dict[str, Any] = None
    ) -> None:
        """Log an access attempt."""
        audit_entry = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            result=result,
            ip_address=ip_address,
            user_agent=user_agent,
            context=context or {}
        )
        
        # Store in Redis with expiration (30 days)
        audit_key = f"audit:{datetime.utcnow().strftime('%Y-%m-%d')}:{user_id}:{int(datetime.utcnow().timestamp())}"
        audit_data = {
            "user_id": audit_entry.user_id,
            "action": audit_entry.action,
            "resource_type": audit_entry.resource_type,
            "resource_id": audit_entry.resource_id,
            "result": str(audit_entry.result),
            "ip_address": audit_entry.ip_address or "",
            "user_agent": audit_entry.user_agent or "",
            "timestamp": audit_entry.timestamp.isoformat(),
            "context": json.dumps(audit_entry.context)
        }
        
        await self.redis_client.hset(audit_key, mapping=audit_data)
        await self.redis_client.expire(audit_key, 30 * 24 * 60 * 60)  # 30 days
        
        # Log to structured logger
        logger.info(
            "Access attempt logged",
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            result=result,
            ip_address=ip_address,
            timestamp=audit_entry.timestamp
        )
    
    async def get_user_audit_logs(
        self,
        user_id: str,
        days: int = 7,
        limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs for a user."""
        logs = []
        
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d')
            pattern = f"audit:{date}:{user_id}:*"
            keys = await self.redis_client.keys(pattern)
            
            for key in sorted(keys, reverse=True)[:limit]:
                log_data = await self.redis_client.hgetall(key)
                if log_data:
                    logs.append(AuditLog(
                        user_id=log_data["user_id"],
                        action=log_data["action"],
                        resource_type=log_data["resource_type"],
                        resource_id=log_data["resource_id"],
                        result=log_data["result"] == "True",
                        ip_address=log_data["ip_address"] if log_data["ip_address"] else None,
                        user_agent=log_data["user_agent"] if log_data["user_agent"] else None,
                        timestamp=datetime.fromisoformat(log_data["timestamp"]),
                        context=json.loads(log_data["context"]) if log_data["context"] else {}
                    ))
                
                if len(logs) >= limit:
                    break
            
            if len(logs) >= limit:
                break
        
        return logs[:limit]


class JWTAuthorizationHelper:
    """JWT token validation helper for authorization."""
    
    def __init__(self, jwt_manager: 'JWTManager'):
        self.jwt_manager = jwt_manager
    
    async def extract_user_from_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Extract user information from JWT token."""
        try:
            payload = await self.jwt_manager.verify_token(token)
            return {
                "user_id": payload.get("sub"),
                "roles": payload.get("roles", []),
                "permissions": payload.get("permissions", []),
                "exp": payload.get("exp"),
                "iat": payload.get("iat")
            }
        except (InvalidTokenError, ExpiredSignatureError) as e:
            logger.warning("Invalid JWT token", error=str(e))
            return None
        except Exception as e:
            logger.error("Error extracting user from token", error=str(e))
            return None
    
    async def validate_token_permissions(
        self,
        token: str,
        required_permission: str
    ) -> bool:
        """Validate if token has required permission."""
        user_data = await self.extract_user_from_token(token)
        if not user_data:
            return False
        
        permissions = user_data.get("permissions", [])
        return required_permission in permissions
    
    def extract_user_context_from_request(self, request: Request) -> Dict[str, Any]:
        """Extract user context from HTTP request."""
        return {
            "ip_address": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "method": request.method,
            "path": str(request.url.path),
            "query_params": dict(request.query_params)
        }


class AuthorizationError(Exception):
    """Base authorization error."""
    def __init__(self, message: str, error_code: str = "AUTH_ERROR", details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.utcnow()
        
        # Log the authorization error
        logger.error(
            "Authorization error occurred",
            error_code=error_code,
            message=message,
            details=self.details,
            timestamp=self.timestamp
        )


class PermissionError(AuthorizationError):
    """Permission-related errors."""
    def __init__(self, user_id: str, resource: str, action: str, message: str = None):
        self.user_id = user_id
        self.resource = resource
        self.action = action
        
        if not message:
            message = f"Permission denied: User {user_id} lacks {action} permission on {resource}"
        
        super().__init__(
            message=message,
            error_code="PERMISSION_DENIED",
            details={
                "user_id": user_id,
                "resource": resource,
                "action": action
            }
        )


class RoleError(AuthorizationError):
    """Role-related errors."""
    def __init__(self, user_id: str, role: str, message: str = None):
        self.user_id = user_id
        self.role = role
        
        if not message:
            message = f"Role error: Invalid or missing role {role} for user {user_id}"
        
        super().__init__(
            message=message,
            error_code="ROLE_ERROR",
            details={
                "user_id": user_id,
                "role": role
            }
        )


class Action(str, Enum):
    """Available actions."""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"


class Resource(str, Enum):
    """Available resources."""
    PATTERN = "pattern"
    MODEL = "model"
    ANALYSIS = "analysis"
    REPOSITORY = "repository"
    USER = "user"
    SYSTEM = "system"
    API = "api"


class Permission(str, Enum):
    """System permissions."""
    # Pattern permissions
    PATTERN_CREATE = "pattern:create"
    PATTERN_READ = "pattern:read"
    PATTERN_UPDATE = "pattern:update"
    PATTERN_DELETE = "pattern:delete"
    
    # Model permissions
    MODEL_CREATE = "model:create"
    MODEL_READ = "model:read"
    MODEL_UPDATE = "model:update"
    MODEL_DELETE = "model:delete"
    MODEL_TRAIN = "model:train"
    MODEL_DEPLOY = "model:deploy"
    
    # Analysis permissions
    ANALYSIS_CREATE = "analysis:create"
    ANALYSIS_READ = "analysis:read"
    ANALYSIS_UPDATE = "analysis:update"
    ANALYSIS_DELETE = "analysis:delete"
    ANALYSIS_EXECUTE = "analysis:execute"
    
    # Repository permissions
    REPOSITORY_CREATE = "repository:create"
    REPOSITORY_READ = "repository:read"
    REPOSITORY_UPDATE = "repository:update"
    REPOSITORY_DELETE = "repository:delete"
    REPOSITORY_ADMIN = "repository:admin"
    
    # User permissions
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_ADMIN = "user:admin"
    
    # System permissions
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_CONFIG = "system:config"
    
    # API permissions
    API_READ = "api:read"
    API_WRITE = "api:write"
    API_ADMIN = "api:admin"


class Role(str, Enum):
    """System roles."""
    ADMIN = "admin"
    MODERATOR = "moderator"
    USER = "user"
    READONLY = "readonly"
    SERVICE = "service"
    ANALYST = "analyst"
    DEVELOPER = "developer"


@dataclass
class RoleDefinition:
    """Role definition with permissions."""
    name: str
    permissions: Set[Permission]
    description: str
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ResourcePermission:
    """Resource-specific permission."""
    resource_type: Resource
    resource_id: str
    action: Action
    granted_by: str
    granted_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None


@dataclass
class AccessRequest:
    """Access request for authorization."""
    user_id: str
    resource_type: Resource
    resource_id: str
    action: Action
    context: Dict[str, Any] = field(default_factory=dict)


class AuthorizationPolicy(ABC):
    """Abstract authorization policy."""
    
    @abstractmethod
    async def check_access(self, request: AccessRequest) -> bool:
        """Check if access should be granted."""
        raise NotImplementedError("Subclasses must implement check_access method")


class RoleBasedPolicy(AuthorizationPolicy):
    """Role-based authorization policy."""
    
    def __init__(self, role_manager: 'RoleManager'):
        self.role_manager = role_manager
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on user roles."""
        user_roles = await self.role_manager.get_user_roles(request.user_id)
        required_permission = f"{request.resource_type.value}:{request.action.value}"
        
        for role in user_roles:
            role_permissions = await self.role_manager.get_role_permissions(role)
            if required_permission in role_permissions:
                return True
        
        return False


class ResourceBasedPolicy(AuthorizationPolicy):
    """Resource-based authorization policy."""
    
    def __init__(self, authorizer: 'RBACAuthorizer'):
        self.authorizer = authorizer
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on resource ownership."""
        return await self.authorizer.check_resource_access(
            request.user_id,
            request.resource_type,
            request.resource_id,
            request.action
        )


class ContextBasedPolicy(AuthorizationPolicy):
    """Context-based authorization policy."""
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on context (IP, time, etc.)."""
        # Check IP whitelist
        if "ip_address" in request.context:
            allowed_ips = request.context.get("allowed_ips", [])
            if allowed_ips and request.context["ip_address"] not in allowed_ips:
                return False
        
        # Check time-based access
        if "time_restrictions" in request.context:
            current_time = datetime.utcnow().time()
            restrictions = request.context["time_restrictions"]
            
            if "start_time" in restrictions and current_time < restrictions["start_time"]:
                return False
            if "end_time" in restrictions and current_time > restrictions["end_time"]:
                return False
        
        return True


class RoleManager:
    """Role management service."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self._default_roles = self._create_default_roles()
    
    def _create_default_roles(self) -> Dict[Role, RoleDefinition]:
        """Create default system roles."""
        return {
            Role.ADMIN: RoleDefinition(
                name="admin",
                permissions={
                    Permission.SYSTEM_ADMIN,
                    Permission.USER_ADMIN,
                    Permission.REPOSITORY_ADMIN,
                    Permission.MODEL_CREATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.MODEL_DELETE,
                    Permission.MODEL_TRAIN,
                    Permission.MODEL_DEPLOY,
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.PATTERN_DELETE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_UPDATE,
                    Permission.ANALYSIS_DELETE,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.API_ADMIN,
                },
                description="Full system administrator",
                is_system_role=True
            ),
            Role.MODERATOR: RoleDefinition(
                name="moderator",
                permissions={
                    Permission.USER_READ,
                    Permission.USER_UPDATE,
                    Permission.REPOSITORY_READ,
                    Permission.REPOSITORY_UPDATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.API_WRITE,
                },
                description="Content moderator",
                is_system_role=True
            ),
            Role.USER: RoleDefinition(
                name="user",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Regular user",
                is_system_role=True
            ),
            Role.READONLY: RoleDefinition(
                name="readonly",
                permissions={
                    Permission.PATTERN_READ,
                    Permission.ANALYSIS_READ,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                },
                description="Read-only access",
                is_system_role=True
            ),
            Role.SERVICE: RoleDefinition(
                name="service",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.MODEL_TRAIN,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Service account",
                is_system_role=True
            ),
            Role.ANALYST: RoleDefinition(
                name="analyst",
                permissions={
                    Permission.PATTERN_READ,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_UPDATE,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Data analyst",
                is_system_role=True
            ),
            Role.DEVELOPER: RoleDefinition(
                name="developer",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.MODEL_CREATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.MODEL_TRAIN,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.REPOSITORY_READ,
                    Permission.REPOSITORY_UPDATE,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Developer",
                is_system_role=True
            )
        }
    
    async def create_role(self, role: RoleDefinition) -> None:
        """Create a new role."""
        try:
            role_key = f"role:{role.name}"
            role_data = {
                "name": role.name,
                "permissions": json.dumps([p.value for p in role.permissions]),
                "description": role.description,
                "is_system_role": str(role.is_system_role),
                "created_at": role.created_at.isoformat(),
                "updated_at": role.updated_at.isoformat()
            }
            
            await self.redis_client.hset(role_key, mapping=role_data)
            
            logger.info(
                "Role created",
                role_name=role.name,
                permission_count=len(role.permissions)
            )
        except Exception as e:
            logger.error(
                "Failed to create role",
                role_name=role.name,
                error=str(e)
            )
            raise
    
    async def get_role(self, role_name: str) -> Optional[RoleDefinition]:
        """Get role by name."""
        try:
            role_key = f"role:{role_name}"
            role_data = await self.redis_client.hgetall(role_key)
            
            if not role_data:
                return None
            
            return RoleDefinition(
                name=role_data["name"],
                permissions={Permission(p) for p in json.loads(role_data["permissions"])},
                description=role_data["description"],
                is_system_role=role_data["is_system_role"] == "True",
                created_at=datetime.fromisoformat(role_data["created_at"]),
                updated_at=datetime.fromisoformat(role_data["updated_at"])
            )
        except Exception as e:
            logger.error(
                "Failed to get role",
                role_name=role_name,
                error=str(e)
            )
            return None
    
    async def assign_role_to_user(self, user_id: str, role_name: str) -> None:
        """Assign role to user."""
        user_roles_key = f"user_roles:{user_id}"
        await self.redis_client.sadd(user_roles_key, role_name)
        
        logger.info(
            "Role assigned to user",
            user_id=user_id,
            role_name=role_name
        )
    
    async def remove_role_from_user(self, user_id: str, role_name: str) -> None:
        """Remove role from user."""
        user_roles_key = f"user_roles:{user_id}"
        await self.redis_client.srem(user_roles_key, role_name)
        
        logger.info(
            "Role removed from user",
            user_id=user_id,
            role_name=role_name
        )
    
    async def get_user_roles(self, user_id: str) -> List[str]:
        """Get user roles."""
        user_roles_key = f"user_roles:{user_id}"
        roles = await self.redis_client.smembers(user_roles_key)
        return list(roles)
    
    async def get_role_permissions(self, role_name: str) -> Set[str]:
        """Get permissions for a role."""
        role = await self.get_role(role_name)
        if not role:
            return set()
        
        return {p.value for p in role.permissions}
    
    async def initialize_default_roles(self) -> None:
        """Initialize default system roles."""
        for role_def in self._default_roles.values():
            await self.create_role(role_def)
        
        logger.info("Default roles initialized")


class PermissionManager:
    """Permission management service."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def grant_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action,
        granted_by: str,
        expires_at: Optional[datetime] = None
    ) -> None:
        """Grant specific permission to user."""
        permission = ResourcePermission(
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            granted_by=granted_by,
            expires_at=expires_at
        )
        
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        permission_data = {
            "resource_type": resource_type.value,
            "resource_id": resource_id,
            "action": action.value,
            "granted_by": granted_by,
            "granted_at": permission.granted_at.isoformat(),
            "expires_at": permission.expires_at.isoformat() if permission.expires_at else None
        }
        
        await self.redis_client.hset(permission_key, mapping=permission_data)
        
        # Set expiration if specified
        if expires_at:
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            await self.redis_client.expire(permission_key, ttl)
        
        logger.info(
            "Permission granted",
            user_id=user_id,
            resource_type=resource_type.value,
            resource_id=resource_id,
            action=action.value,
            granted_by=granted_by
        )
    
    async def revoke_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> None:
        """Revoke specific permission from user."""
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        await self.redis_client.delete(permission_key)
        
        logger.info(
            "Permission revoked",
            user_id=user_id,
            resource_type=resource_type.value,
            resource_id=resource_id,
            action=action.value
        )
    
    async def check_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> bool:
        """Check if user has specific permission."""
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        permission_data = await self.redis_client.hgetall(permission_key)
        
        if not permission_data:
            return False
        
        # Check if permission has expired
        if permission_data.get("expires_at"):
            expires_at = datetime.fromisoformat(permission_data["expires_at"])
            if expires_at < datetime.utcnow():
                await self.redis_client.delete(permission_key)
                return False
        
        return True
    
    async def get_user_permissions(self, user_id: str) -> List[ResourcePermission]:
        """Get all permissions for a user."""
        pattern = f"permission:{user_id}:*"
        keys = await self.redis_client.keys(pattern)
        permissions = []
        
        for key in keys:
            permission_data = await self.redis_client.hgetall(key)
            if permission_data:
                # Check if expired
                if permission_data.get("expires_at"):
                    expires_at = datetime.fromisoformat(permission_data["expires_at"])
                    if expires_at < datetime.utcnow():
                        await self.redis_client.delete(key)
                        continue
                
                permissions.append(ResourcePermission(
                    resource_type=Resource(permission_data["resource_type"]),
                    resource_id=permission_data["resource_id"],
                    action=Action(permission_data["action"]),
                    granted_by=permission_data["granted_by"],
                    granted_at=datetime.fromisoformat(permission_data["granted_at"]),
                    expires_at=datetime.fromisoformat(permission_data["expires_at"]) if permission_data.get("expires_at") else None
                ))
        
        return permissions


class RBACAuthorizer:
    """Role-Based Access Control authorizer."""
    
    def __init__(
        self,
        role_manager: RoleManager,
        permission_manager: PermissionManager,
        redis_client: redis.Redis,
        jwt_helper: Optional[JWTAuthorizationHelper] = None,
        audit_logger: Optional[AuditLogger] = None
    ):
        self.role_manager = role_manager
        self.permission_manager = permission_manager
        self.redis_client = redis_client
        self.jwt_helper = jwt_helper
        self.audit_logger = audit_logger or AuditLogger(redis_client)
        self.policies = [
            RoleBasedPolicy(role_manager),
            ResourceBasedPolicy(self),
            ContextBasedPolicy()
        ]
    
    async def authorize(self, request: AccessRequest) -> bool:
        """Authorize access request."""
        start_time = datetime.utcnow()
        
        # Extract context information
        ip_address = request.context.get("ip_address")
        user_agent = request.context.get("user_agent")
        
        # Check cache first
        cache_key = f"auth_cache:{request.user_id}:{request.resource_type.value}:{request.resource_id}:{request.action.value}"
        cached_result = await self.redis_client.get(cache_key)
        
        if cached_result is not None:
            result = cached_result == "true"
            await self.audit_logger.log_access_attempt(
                user_id=request.user_id,
                action=request.action.value,
                resource_type=request.resource_type.value,
                resource_id=request.resource_id,
                result=result,
                ip_address=ip_address,
                user_agent=user_agent,
                context={"source": "cache", **request.context}
            )
            return result
        
        # Validate JWT token if provided
        jwt_token = request.context.get("jwt_token")
        if jwt_token and self.jwt_helper:
            # Validate token and extract user info
            user_data = await self.jwt_helper.extract_user_from_token(jwt_token)
            if not user_data:
                await self.audit_logger.log_access_attempt(
                    user_id=request.user_id,
                    action=request.action.value,
                    resource_type=request.resource_type.value,
                    resource_id=request.resource_id,
                    result=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    context={"reason": "invalid_jwt_token", **request.context}
                )
                return False
            
            # Verify user_id matches token
            if user_data.get("user_id") != request.user_id:
                await self.audit_logger.log_access_attempt(
                    user_id=request.user_id,
                    action=request.action.value,
                    resource_type=request.resource_type.value,
                    resource_id=request.resource_id,
                    result=False,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    context={"reason": "user_id_mismatch", "token_user_id": user_data.get("user_id"), **request.context}
                )
                return False
        
        # Check all policies
        policy_results = []
        for policy in self.policies:
            try:
                policy_result = await policy.check_access(request)
                policy_results.append((policy.__class__.__name__, policy_result))
                
                if policy_result:
                    # Cache positive result for 5 minutes
                    await self.redis_client.setex(cache_key, 300, "true")
                    
                    # Log successful authorization
                    processing_time = (datetime.utcnow() - start_time).total_seconds()
                    await self.audit_logger.log_access_attempt(
                        user_id=request.user_id,
                        action=request.action.value,
                        resource_type=request.resource_type.value,
                        resource_id=request.resource_id,
                        result=True,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        context={
                            "granting_policy": policy.__class__.__name__,
                            "processing_time_seconds": processing_time,
                            "policy_results": policy_results,
                            **request.context
                        }
                    )
                    
                    logger.info(
                        "Access granted",
                        user_id=request.user_id,
                        resource_type=request.resource_type.value,
                        resource_id=request.resource_id,
                        action=request.action.value,
                        granting_policy=policy.__class__.__name__,
                        processing_time=processing_time
                    )
                    
                    return True
                    
            except Exception as e:
                logger.error(
                    "Policy check failed",
                    policy=policy.__class__.__name__,
                    error=str(e),
                    user_id=request.user_id
                )
                policy_results.append((policy.__class__.__name__, False))
        
        # Cache negative result for 1 minute
        await self.redis_client.setex(cache_key, 60, "false")
        
        # Log failed authorization
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        await self.audit_logger.log_access_attempt(
            user_id=request.user_id,
            action=request.action.value,
            resource_type=request.resource_type.value,
            resource_id=request.resource_id,
            result=False,
            ip_address=ip_address,
            user_agent=user_agent,
            context={
                "reason": "all_policies_denied",
                "processing_time_seconds": processing_time,
                "policy_results": policy_results,
                **request.context
            }
        )
        
        logger.warning(
            "Access denied",
            user_id=request.user_id,
            resource_type=request.resource_type.value,
            resource_id=request.resource_id,
            action=request.action.value,
            processing_time=processing_time,
            policy_results=policy_results
        )
        
        return False
    
    async def check_resource_access(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> bool:
        """Check resource-specific access."""
        # Check specific permission
        if await self.permission_manager.check_permission(user_id, resource_type, resource_id, action):
            return True
        
        # Check ownership (for user-owned resources)
        if resource_type == Resource.PATTERN:
            owner_key = f"pattern_owner:{resource_id}"
            owner_id = await self.redis_client.get(owner_key)
            if owner_id == user_id:
                return True
        
        return False


class ResourceAuthorizer:
    """Resource-based authorization."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def set_resource_owner(self, resource_type: Resource, resource_id: str, user_id: str) -> None:
        """Set resource owner."""
        owner_key = f"{resource_type.value}_owner:{resource_id}"
        await self.redis_client.set(owner_key, user_id)
        
        logger.info(
            "Resource owner set",
            resource_type=resource_type.value,
            resource_id=resource_id,
            user_id=user_id
        )
    
    async def get_resource_owner(self, resource_type: Resource, resource_id: str) -> Optional[str]:
        """Get resource owner."""
        owner_key = f"{resource_type.value}_owner:{resource_id}"
        return await self.redis_client.get(owner_key)
    
    async def check_resource_ownership(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str
    ) -> bool:
        """Check if user owns resource."""
        owner = await self.get_resource_owner(resource_type, resource_id)
        return owner == user_id


class PrivilegeEscalationPrevention:
    """Prevents privilege escalation attacks."""
    
    def __init__(self, redis_client: redis.Redis, audit_logger: AuditLogger):
        self.redis_client = redis_client
        self.audit_logger = audit_logger
    
    async def check_privilege_escalation(
        self,
        user_id: str,
        current_roles: List[str],
        requested_action: str,
        context: Dict[str, Any]
    ) -> bool:
        """Check for potential privilege escalation."""
        
        # Track role elevation attempts
        elevation_key = f"role_elevation:{user_id}:{datetime.utcnow().strftime('%Y-%m-%d')}"
        current_count = await self.redis_client.incr(elevation_key)
        await self.redis_client.expire(elevation_key, 24 * 60 * 60)  # 24 hours
        
        # Alert on multiple elevation attempts
        if current_count > 5:
            await self.audit_logger.log_access_attempt(
                user_id=user_id,
                action="PRIVILEGE_ESCALATION_ATTEMPT",
                resource_type="SYSTEM",
                resource_id="security",
                result=False,
                context={
                    "escalation_attempts": current_count,
                    "current_roles": current_roles,
                    "requested_action": requested_action,
                    **context
                }
            )
            
            logger.critical(
                "Multiple privilege escalation attempts detected",
                user_id=user_id,
                attempts=current_count,
                action=requested_action
            )
            return False
        
        # Check for suspicious role combinations
        high_privilege_roles = {"admin", "system_admin", "root"}
        user_high_privileges = set(current_roles) & high_privilege_roles
        
        if len(user_high_privileges) > 1:
            await self.audit_logger.log_access_attempt(
                user_id=user_id,
                action="SUSPICIOUS_ROLE_COMBINATION",
                resource_type="SYSTEM",
                resource_id="security",
                result=False,
                context={
                    "high_privilege_roles": list(user_high_privileges),
                    "all_roles": current_roles,
                    **context
                }
            )
            
            logger.warning(
                "User has multiple high privilege roles",
                user_id=user_id,
                high_privilege_roles=list(user_high_privileges)
            )
        
        # Check for time-based anomalies
        if "ip_address" in context:
            ip_key = f"user_ip:{user_id}"
            last_ips = await self.redis_client.lrange(ip_key, 0, 9)  # Last 10 IPs
            
            if context["ip_address"].encode() not in last_ips:
                # New IP for privileged operation
                if any(role in high_privilege_roles for role in current_roles):
                    await self.audit_logger.log_access_attempt(
                        user_id=user_id,
                        action="NEW_IP_PRIVILEGED_ACCESS",
                        resource_type="SYSTEM",
                        resource_id="security",
                        result=True,  # Not blocking, just logging
                        context={
                            "new_ip": context["ip_address"],
                            "last_ips": [ip.decode() for ip in last_ips],
                            "roles": current_roles,
                            **context
                        }
                    )
                
                # Update IP history
                await self.redis_client.lpush(ip_key, context["ip_address"])
                await self.redis_client.ltrim(ip_key, 0, 9)
                await self.redis_client.expire(ip_key, 30 * 24 * 60 * 60)  # 30 days
        
        return True
    
    async def detect_anomalous_access_patterns(self, user_id: str) -> List[str]:
        """Detect anomalous access patterns for a user."""
        anomalies = []
        
        # Check for rapid successive requests
        request_pattern_key = f"request_pattern:{user_id}:{datetime.utcnow().strftime('%Y-%m-%d-%H')}"
        request_count = await self.redis_client.get(request_pattern_key)
        
        if request_count and int(request_count) > 1000:  # More than 1000 requests per hour
            anomalies.append("high_request_frequency")
        
        # Check for off-hours access
        current_hour = datetime.utcnow().hour
        if current_hour < 6 or current_hour > 22:  # Outside business hours
            anomalies.append("off_hours_access")
        
        # Check for geographic anomalies (would need GeoIP service in real implementation)
        # For now, just check for multiple IPs in short timeframe
        ip_key = f"user_ip_timestamps:{user_id}"
        recent_ips = await self.redis_client.zrangebyscore(
            ip_key,
            (datetime.utcnow() - timedelta(hours=1)).timestamp(),
            datetime.utcnow().timestamp()
        )
        
        if len(set(recent_ips)) > 3:  # More than 3 different IPs in last hour
            anomalies.append("multiple_ip_addresses")
        
        return anomalies


class ResourceLevelSecurity:
    """Enhanced resource-level security controls."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def set_resource_sensitivity(
        self,
        resource_type: Resource,
        resource_id: str,
        sensitivity_level: str  # "public", "internal", "confidential", "restricted"
    ) -> None:
        """Set resource sensitivity level."""
        sensitivity_key = f"resource_sensitivity:{resource_type.value}:{resource_id}"
        await self.redis_client.set(sensitivity_key, sensitivity_level)
        
        logger.info(
            "Resource sensitivity set",
            resource_type=resource_type.value,
            resource_id=resource_id,
            sensitivity_level=sensitivity_level
        )
    
    async def get_resource_sensitivity(
        self,
        resource_type: Resource,
        resource_id: str
    ) -> str:
        """Get resource sensitivity level."""
        sensitivity_key = f"resource_sensitivity:{resource_type.value}:{resource_id}"
        sensitivity = await self.redis_client.get(sensitivity_key)
        return sensitivity.decode() if sensitivity else "internal"
    
    async def check_sensitivity_access(
        self,
        user_id: str,
        user_clearance: str,
        resource_type: Resource,
        resource_id: str
    ) -> bool:
        """Check if user has sufficient clearance for resource."""
        resource_sensitivity = await self.get_resource_sensitivity(resource_type, resource_id)
        
        # Define clearance hierarchy
        clearance_levels = {
            "public": 0,
            "internal": 1,
            "confidential": 2,
            "restricted": 3
        }
        
        user_level = clearance_levels.get(user_clearance, 0)
        resource_level = clearance_levels.get(resource_sensitivity, 1)
        
        return user_level >= resource_level
    
    async def set_resource_access_window(
        self,
        resource_type: Resource,
        resource_id: str,
        start_time: datetime,
        end_time: datetime
    ) -> None:
        """Set time-based access window for resource."""
        window_key = f"access_window:{resource_type.value}:{resource_id}"
        window_data = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat()
        }
        await self.redis_client.hset(window_key, mapping=window_data)
        
        # Set expiration after end time
        ttl = int((end_time - datetime.utcnow()).total_seconds())
        if ttl > 0:
            await self.redis_client.expire(window_key, ttl)
    
    async def check_access_window(
        self,
        resource_type: Resource,
        resource_id: str
    ) -> bool:
        """Check if current time is within resource access window."""
        window_key = f"access_window:{resource_type.value}:{resource_id}"
        window_data = await self.redis_client.hgetall(window_key)
        
        if not window_data:
            return True  # No restrictions
        
        current_time = datetime.utcnow()
        start_time = datetime.fromisoformat(window_data["start_time"])
        end_time = datetime.fromisoformat(window_data["end_time"])
        
        return start_time <= current_time <= end_time


class ComprehensiveAuthorizationSystem:
    """Comprehensive authorization system with all security features."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        jwt_helper: Optional[JWTAuthorizationHelper] = None
    ):
        self.redis_client = redis_client
        self.audit_logger = AuditLogger(redis_client)
        self.role_manager = RoleManager(redis_client)
        self.permission_manager = PermissionManager(redis_client)
        self.rbac_authorizer = RBACAuthorizer(
            self.role_manager,
            self.permission_manager,
            redis_client,
            jwt_helper,
            self.audit_logger
        )
        self.resource_authorizer = ResourceAuthorizer(redis_client)
        self.privilege_prevention = PrivilegeEscalationPrevention(redis_client, self.audit_logger)
        self.resource_security = ResourceLevelSecurity(redis_client)
    
    async def authorize_comprehensive(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action,
        context: Dict[str, Any] = None,
        user_clearance: str = "internal",
        jwt_token: str = None
    ) -> bool:
        """Comprehensive authorization check."""
        context = context or {}
        if jwt_token:
            context["jwt_token"] = jwt_token
        
        # Create access request
        request = AccessRequest(
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            context=context
        )
        
        # Check access window
        if not await self.resource_security.check_access_window(resource_type, resource_id):
            await self.audit_logger.log_access_attempt(
                user_id, action.value, resource_type.value, resource_id, False,
                context.get("ip_address"), context.get("user_agent"),
                {"reason": "outside_access_window", **context}
            )
            return False
        
        # Check sensitivity clearance
        if not await self.resource_security.check_sensitivity_access(
            user_id, user_clearance, resource_type, resource_id
        ):
            await self.audit_logger.log_access_attempt(
                user_id, action.value, resource_type.value, resource_id, False,
                context.get("ip_address"), context.get("user_agent"),
                {"reason": "insufficient_clearance", "user_clearance": user_clearance, **context}
            )
            return False
        
        # Check for privilege escalation
        user_roles = await self.role_manager.get_user_roles(user_id)
        if not await self.privilege_prevention.check_privilege_escalation(
            user_id, user_roles, action.value, context
        ):
            return False
        
        # Detect anomalous patterns
        anomalies = await self.privilege_prevention.detect_anomalous_access_patterns(user_id)
        if anomalies:
            context["detected_anomalies"] = anomalies
            logger.warning(
                "Anomalous access patterns detected",
                user_id=user_id,
                anomalies=anomalies
            )
        
        # Final RBAC authorization
        return await self.rbac_authorizer.authorize(request)
    
    async def initialize_system(self) -> None:
        """Initialize the authorization system."""
        await self.role_manager.initialize_default_roles()
        logger.info("Comprehensive authorization system initialized")


# Example usage and integration helper
async def create_authorization_system(redis_client: redis.Redis, jwt_manager=None) -> ComprehensiveAuthorizationSystem:
    """Factory function to create a fully configured authorization system."""
    
    # Create JWT helper if JWT manager provided
    jwt_helper = JWTAuthorizationHelper(jwt_manager) if jwt_manager else None
    
    # Create comprehensive system
    auth_system = ComprehensiveAuthorizationSystem(redis_client, jwt_helper)
    
    # Initialize system
    await auth_system.initialize_system()
    
    logger.info("Authorization system created and initialized")
    return auth_system


# FastAPI middleware integration helper
async def authorize_request(
    auth_system: ComprehensiveAuthorizationSystem,
    user_id: str,
    resource_type: Resource,
    resource_id: str,
    action: Action,
    request: Request = None,
    jwt_token: str = None,
    user_clearance: str = "internal"
) -> bool:
    """Helper function for FastAPI request authorization."""
    context = {}
    
    if request:
        context.update({
            "ip_address": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "method": request.method,
            "path": str(request.url.path),
            "query_params": dict(request.query_params)
        })
    
    return await auth_system.authorize_comprehensive(
        user_id=user_id,
        resource_type=resource_type,
        resource_id=resource_id,
        action=action,
        context=context,
        user_clearance=user_clearance,
        jwt_token=jwt_token
    )