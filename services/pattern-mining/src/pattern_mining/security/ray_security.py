"""
Ray Cluster Security Configuration

Provides secure Ray cluster setup with mutual TLS authentication, secure communication,
and access control for distributed pattern mining operations.
"""

import os
import ssl
import asyncio
import json
import secrets
from typing import Optional, Dict, Any, List
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
import tempfile
import shutil

import ray
from ray import serve
from cryptography import x509
from cryptography.x509.oid import NameOID, ExtensionOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger()


@dataclass
class RaySecurityConfig:
    """Ray cluster security configuration."""
    
    # TLS Configuration
    enable_tls: bool = True
    tls_ca_cert_path: Optional[str] = None
    tls_server_cert_path: Optional[str] = None
    tls_server_key_path: Optional[str] = None
    tls_client_cert_path: Optional[str] = None
    tls_client_key_path: Optional[str] = None
    tls_verify_mode: ssl.VerifyMode = ssl.CERT_REQUIRED
    tls_min_version: ssl.TLSVersion = ssl.TLSVersion.TLSv1_3
    
    # Certificate Management
    cert_rotation_days: int = 30
    cert_expiry_warning_days: int = 7
    auto_generate_certs: bool = False
    cert_storage_path: str = "/etc/ray/certs"
    
    # Authentication
    enable_auth: bool = True
    auth_token: Optional[str] = None
    auth_token_rotation_hours: int = 24
    dashboard_username: Optional[str] = "admin"
    dashboard_password: Optional[str] = None
    
    # Network Security
    allowed_client_ips: List[str] = field(default_factory=lambda: ["127.0.0.1", "::1"])
    bind_address: str = "127.0.0.1"
    dashboard_host: str = "127.0.0.1"
    dashboard_port: int = 8265
    enable_client_ip_filtering: bool = True
    
    # Ray Configuration
    ray_temp_dir: str = "/tmp/ray"
    ray_logging_level: str = "INFO"
    object_store_memory: int = 2_000_000_000  # 2GB
    
    # Security Monitoring
    enable_security_logging: bool = True
    log_connection_attempts: bool = True
    log_auth_failures: bool = True
    enable_intrusion_detection: bool = True
    max_failed_auth_attempts: int = 3
    
    # Resource Limits
    max_tasks_per_worker: int = 1000
    max_memory_per_task: int = 1_000_000_000  # 1GB
    max_cpu_per_task: float = 1.0
    task_timeout_seconds: int = 3600  # 1 hour
    
    def to_ray_init_args(self) -> Dict[str, Any]:
        """Convert to Ray initialization arguments."""
        args = {
            "ignore_reinit_error": True,
            "logging_level": self.ray_logging_level,
            "log_to_driver": True,
            "object_store_memory": self.object_store_memory,
            "_temp_dir": self.ray_temp_dir,
        }
        
        if self.enable_auth and self.auth_token:
            args["_ray_commit"] = self.auth_token
        
        if self.dashboard_host:
            args["dashboard_host"] = self.dashboard_host
            args["dashboard_port"] = self.dashboard_port
        
        # System config for production hardening
        args["_system_config"] = {
            "object_timeout_milliseconds": 600000,
            "num_heartbeats_timeout": 300,
            "raylet_heartbeat_timeout_milliseconds": 300000,
            "worker_register_timeout_seconds": 60,
            "redis_password": self.auth_token if self.enable_auth else None,
        }
        
        return args


class RayCertificateManager:
    """Manages Ray cluster TLS certificates."""
    
    def __init__(self, config: RaySecurityConfig):
        self.config = config
        self.cert_path = Path(config.cert_storage_path)
        self.cert_path.mkdir(parents=True, exist_ok=True)
        
    async def ensure_certificates(self) -> Dict[str, str]:
        """Ensure valid certificates exist for Ray cluster."""
        cert_files = {
            "ca_cert": self.cert_path / "ca.crt",
            "server_cert": self.cert_path / "server.crt",
            "server_key": self.cert_path / "server.key",
            "client_cert": self.cert_path / "client.crt",
            "client_key": self.cert_path / "client.key",
            "head_cert": self.cert_path / "head.crt",
            "head_key": self.cert_path / "head.key",
            "worker_cert": self.cert_path / "worker.crt",
            "worker_key": self.cert_path / "worker.key"
        }
        
        # Check if certificates exist and are valid
        if await self._certificates_valid(cert_files):
            logger.info("Ray certificates are valid")
            return {k: str(v) for k, v in cert_files.items()}
        
        if self.config.auto_generate_certs:
            logger.info("Generating new Ray certificates")
            return await self._generate_certificates()
        else:
            raise ValueError("Ray certificates not found or invalid, and auto-generation is disabled")
    
    async def _certificates_valid(self, cert_files: Dict[str, Path]) -> bool:
        """Check if certificates exist and are valid."""
        try:
            # Check if all files exist
            for cert_file in cert_files.values():
                if not cert_file.exists():
                    return False
            
            # Check server certificate expiration
            with open(cert_files["server_cert"], "rb") as f:
                cert_data = f.read()
                cert = x509.load_pem_x509_certificate(cert_data, default_backend())
                
                # Check expiration
                days_until_expiry = (cert.not_valid_after - datetime.utcnow()).days
                if days_until_expiry < self.config.cert_expiry_warning_days:
                    logger.warning(
                        "Ray certificate expiring soon",
                        days_until_expiry=days_until_expiry
                    )
                    if days_until_expiry <= 0:
                        return False
            
            return True
            
        except Exception as e:
            logger.error("Error checking certificate validity", error=str(e))
            return False
    
    async def _generate_certificates(self) -> Dict[str, str]:
        """Generate certificates for Ray cluster with mutual TLS."""
        # Generate CA
        ca_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        ca_cert = self._create_ca_certificate(ca_key)
        
        # Generate server certificates for different components
        server_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        server_cert = self._create_server_certificate(server_key, ca_key, ca_cert, "ray-server")
        
        head_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        head_cert = self._create_server_certificate(head_key, ca_key, ca_cert, "ray-head")
        
        worker_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        worker_cert = self._create_server_certificate(worker_key, ca_key, ca_cert, "ray-worker")
        
        # Generate client certificate
        client_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        client_cert = self._create_client_certificate(client_key, ca_key, ca_cert)
        
        # Save certificates
        cert_files = {
            "ca_cert": self._save_certificate(ca_cert, "ca.crt"),
            "server_cert": self._save_certificate(server_cert, "server.crt"),
            "server_key": self._save_private_key(server_key, "server.key"),
            "client_cert": self._save_certificate(client_cert, "client.crt"),
            "client_key": self._save_private_key(client_key, "client.key"),
            "head_cert": self._save_certificate(head_cert, "head.crt"),
            "head_key": self._save_private_key(head_key, "head.key"),
            "worker_cert": self._save_certificate(worker_cert, "worker.crt"),
            "worker_key": self._save_private_key(worker_key, "worker.key")
        }
        
        logger.info("Generated new Ray certificates", cert_files=cert_files)
        return cert_files
    
    def _create_ca_certificate(self, key: rsa.RSAPrivateKey) -> x509.Certificate:
        """Create CA certificate for Ray cluster."""
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Ray Cluster"),
            x509.NameAttribute(NameOID.COMMON_NAME, "Ray Cluster CA"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365 * 10)
        ).add_extension(
            x509.BasicConstraints(ca=True, path_length=0),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_cert_sign=True,
                crl_sign=True,
                key_encipherment=False,
                content_commitment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(key, hashes.SHA256(), backend=default_backend())
        
        return cert
    
    def _create_server_certificate(
        self,
        key: rsa.RSAPrivateKey,
        ca_key: rsa.RSAPrivateKey,
        ca_cert: x509.Certificate,
        common_name: str
    ) -> x509.Certificate:
        """Create server certificate for Ray component."""
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Ray Cluster"),
            x509.NameAttribute(NameOID.COMMON_NAME, common_name),
        ])
        
        # Add SANs for different hostnames Ray might use
        san_list = [
            x509.DNSName("localhost"),
            x509.DNSName(common_name),
            x509.DNSName("*.ray.local"),
            x509.IPAddress("127.0.0.1"),
            x509.IPAddress("::1"),
        ]
        
        # Add configured bind address if different
        if self.config.bind_address not in ["127.0.0.1", "0.0.0.0"]:
            san_list.append(x509.IPAddress(self.config.bind_address))
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=self.config.cert_rotation_days)
        ).add_extension(
            x509.SubjectAlternativeName(san_list),
            critical=False,
        ).add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=True,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.ExtendedKeyUsage([
                x509.oid.ExtensionOID.SERVER_AUTH,
                x509.oid.ExtensionOID.CLIENT_AUTH,  # For mutual TLS
            ]),
            critical=True,
        ).sign(ca_key, hashes.SHA256(), backend=default_backend())
        
        return cert
    
    def _create_client_certificate(
        self,
        key: rsa.RSAPrivateKey,
        ca_key: rsa.RSAPrivateKey,
        ca_cert: x509.Certificate
    ) -> x509.Certificate:
        """Create client certificate for Ray cluster access."""
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Ray Cluster"),
            x509.NameAttribute(NameOID.COMMON_NAME, "ray-client"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=self.config.cert_rotation_days)
        ).add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.ExtendedKeyUsage([x509.oid.ExtensionOID.CLIENT_AUTH]),
            critical=True,
        ).sign(ca_key, hashes.SHA256(), backend=default_backend())
        
        return cert
    
    def _save_certificate(self, cert: x509.Certificate, filename: str) -> str:
        """Save certificate to file."""
        cert_path = self.cert_path / filename
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        cert_path.chmod(0o644)
        return str(cert_path)
    
    def _save_private_key(self, key: rsa.RSAPrivateKey, filename: str) -> str:
        """Save private key to file."""
        key_path = self.cert_path / filename
        with open(key_path, "wb") as f:
            f.write(key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.TraditionalOpenSSL,
                encryption_algorithm=serialization.NoEncryption()
            ))
        key_path.chmod(0o600)
        return str(key_path)


class RaySecurityManager:
    """Manages Ray cluster security configuration and monitoring."""
    
    def __init__(self, config: RaySecurityConfig):
        self.config = config
        self.cert_manager = RayCertificateManager(config)
        self.auth_failures: Dict[str, int] = {}
        self.blocked_ips: set = set()
        
    async def setup_secure_cluster(self) -> Dict[str, Any]:
        """Setup secure Ray cluster with authentication and TLS."""
        try:
            # Ensure certificates if TLS is enabled
            certs = {}
            if self.config.enable_tls:
                certs = await self.cert_manager.ensure_certificates()
                logger.info("Ray TLS certificates configured")
            
            # Generate auth token if not provided
            if self.config.enable_auth and not self.config.auth_token:
                self.config.auth_token = secrets.token_urlsafe(32)
                logger.info("Generated Ray authentication token")
            
            # Create Ray configuration
            ray_config = self._create_ray_config(certs)
            
            # Write configuration files
            config_files = await self._write_config_files(ray_config, certs)
            
            logger.info("Ray security configuration completed", config_files=config_files)
            return {
                "config_files": config_files,
                "certificates": certs,
                "auth_token": self.config.auth_token if self.config.enable_auth else None,
                "dashboard_url": f"https://{self.config.dashboard_host}:{self.config.dashboard_port}"
            }
            
        except Exception as e:
            logger.error("Failed to setup secure Ray cluster", error=str(e))
            raise
    
    def _create_ray_config(self, certs: Dict[str, str]) -> Dict[str, Any]:
        """Create Ray configuration with security settings."""
        config = {
            "ray_version": ray.__version__,
            "cluster_name": "pattern-mining-cluster",
            "max_workers": 100,
            "upscaling_speed": 1.0,
            "idle_timeout_minutes": 5,
            
            # Authentication
            "auth": {
                "enabled": self.config.enable_auth,
                "token": self.config.auth_token,
            },
            
            # TLS Configuration
            "tls": {
                "enabled": self.config.enable_tls,
                "ca_cert": certs.get("ca_cert"),
                "server_cert": certs.get("server_cert"),
                "server_key": certs.get("server_key"),
            },
            
            # Security settings
            "security": {
                "dashboard": {
                    "username": self.config.dashboard_username,
                    "password": self.config.dashboard_password or secrets.token_urlsafe(16),
                    "bind_address": self.config.dashboard_host,
                    "port": self.config.dashboard_port,
                },
                "network": {
                    "allowed_ips": self.config.allowed_client_ips,
                    "enable_ip_filtering": self.config.enable_client_ip_filtering,
                },
                "resource_limits": {
                    "max_tasks_per_worker": self.config.max_tasks_per_worker,
                    "max_memory_per_task": self.config.max_memory_per_task,
                    "max_cpu_per_task": self.config.max_cpu_per_task,
                    "task_timeout_seconds": self.config.task_timeout_seconds,
                },
            },
        }
        
        return config
    
    async def _write_config_files(self, ray_config: Dict[str, Any], certs: Dict[str, str]) -> Dict[str, str]:
        """Write Ray configuration files with proper permissions."""
        config_dir = Path(self.config.ray_temp_dir) / "config"
        config_dir.mkdir(parents=True, exist_ok=True)
        
        files = {}
        
        # Write main configuration
        config_file = config_dir / "ray-config.yaml"
        with open(config_file, "w") as f:
            import yaml
            yaml.dump(ray_config, f, default_flow_style=False)
        config_file.chmod(0o600)
        files["config"] = str(config_file)
        
        # Write TLS configuration if enabled
        if self.config.enable_tls:
            tls_config = {
                "tls": {
                    "ca_cert": certs.get("ca_cert"),
                    "certfile": certs.get("server_cert"),
                    "keyfile": certs.get("server_key"),
                    "verify_mode": self.config.tls_verify_mode.name,
                    "min_version": self.config.tls_min_version.name,
                }
            }
            
            tls_file = config_dir / "tls-config.yaml"
            with open(tls_file, "w") as f:
                yaml.dump(tls_config, f, default_flow_style=False)
            tls_file.chmod(0o600)
            files["tls_config"] = str(tls_file)
        
        # Write authentication configuration
        if self.config.enable_auth:
            auth_file = config_dir / "auth-token"
            with open(auth_file, "w") as f:
                f.write(self.config.auth_token)
            auth_file.chmod(0o600)
            files["auth_token"] = str(auth_file)
        
        return files
    
    async def monitor_security_events(self, event_type: str, client_ip: str, details: Dict[str, Any]):
        """Monitor and respond to security events."""
        if not self.config.enable_security_logging:
            return
        
        logger.info("Ray security event", event_type=event_type, client_ip=client_ip, details=details)
        
        # Track authentication failures
        if event_type == "auth_failure":
            self.auth_failures[client_ip] = self.auth_failures.get(client_ip, 0) + 1
            
            if self.auth_failures[client_ip] >= self.config.max_failed_auth_attempts:
                logger.warning(
                    "Blocking IP due to multiple auth failures",
                    client_ip=client_ip,
                    failures=self.auth_failures[client_ip]
                )
                self.blocked_ips.add(client_ip)
        
        # Check for intrusion patterns
        if self.config.enable_intrusion_detection:
            if await self._detect_intrusion_pattern(event_type, client_ip, details):
                logger.error(
                    "SECURITY ALERT: Potential intrusion detected",
                    client_ip=client_ip,
                    event_type=event_type
                )
                self.blocked_ips.add(client_ip)
    
    async def _detect_intrusion_pattern(self, event_type: str, client_ip: str, details: Dict[str, Any]) -> bool:
        """Detect potential intrusion patterns."""
        # Simple pattern detection - can be enhanced with ML
        suspicious_patterns = [
            "port_scan",
            "brute_force",
            "privilege_escalation",
            "data_exfiltration"
        ]
        
        return any(pattern in str(details).lower() for pattern in suspicious_patterns)
    
    def is_ip_blocked(self, client_ip: str) -> bool:
        """Check if IP is blocked."""
        return client_ip in self.blocked_ips
    
    async def rotate_credentials(self):
        """Rotate authentication credentials."""
        logger.info("Rotating Ray cluster credentials")
        
        # Generate new auth token
        old_token = self.config.auth_token
        self.config.auth_token = secrets.token_urlsafe(32)
        
        # Generate new dashboard password
        self.config.dashboard_password = secrets.token_urlsafe(16)
        
        # Graceful credential rotation implementation
        # 1. Update configuration files
        # 2. Notify connected clients
        # 3. Grace period for old credentials
        # 4. Revoke old credentials
        
        logger.info("Credential rotation completed")


def get_secure_ray_config() -> RaySecurityConfig:
    """Get secure Ray configuration from settings."""
    settings = get_settings()
    
    # Generate secure auth token if not provided
    auth_token = os.getenv("RAY_AUTH_TOKEN")
    if not auth_token and settings.environment == "production":
        auth_token = secrets.token_urlsafe(32)
        logger.warning("Generated random Ray auth token for production")
    
    # Generate dashboard password if not provided
    dashboard_password = os.getenv("RAY_DASHBOARD_PASSWORD")
    if not dashboard_password and settings.environment == "production":
        dashboard_password = secrets.token_urlsafe(16)
        logger.warning("Generated random Ray dashboard password for production")
    
    return RaySecurityConfig(
        enable_tls=settings.environment in ["production", "staging"],
        enable_auth=settings.environment in ["production", "staging"],
        auth_token=auth_token,
        dashboard_username=os.getenv("RAY_DASHBOARD_USERNAME", "admin"),
        dashboard_password=dashboard_password,
        cert_storage_path=os.getenv("RAY_CERT_PATH", "/etc/ray/certs"),
        auto_generate_certs=settings.environment == "development",
        bind_address="0.0.0.0" if settings.environment == "production" else "127.0.0.1",
        dashboard_host="0.0.0.0" if settings.environment == "production" else "127.0.0.1",
        enable_security_logging=settings.environment == "production",
        enable_intrusion_detection=settings.environment == "production",
        allowed_client_ips=os.getenv("RAY_ALLOWED_IPS", "").split(",") if os.getenv("RAY_ALLOWED_IPS") else ["127.0.0.1", "::1"]
    )