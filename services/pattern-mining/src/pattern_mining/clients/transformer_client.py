"""
Transformer client with migration support.

This client handles routing between Python and Rust transformer implementations
with feature flags, monitoring, and fallback capabilities.
"""

import asyncio
import time
from typing import Dict, Any, Optional, Union
import aiohttp
from dataclasses import dataclass
import logging
from prometheus_client import Counter, Histogram, Gauge

from ..config.feature_flags import (
    get_feature_flag_service,
    TransformerRouter,
    TransformerRoutingConfig
)
from ..ast_processing.python_transformer import transform_python_code

logger = logging.getLogger(__name__)

# Prometheus metrics
TRANSFORMER_REQUESTS = Counter(
    'transformer_requests_total',
    'Total transformer requests',
    ['implementation', 'status']
)

TRANSFORMER_LATENCY = Histogram(
    'transformer_request_duration_seconds',
    'Transformer request latency',
    ['implementation']
)

TRANSFORMER_FALLBACKS = Counter(
    'transformer_fallbacks_total',
    'Total transformer fallback occurrences',
    ['from_implementation', 'to_implementation', 'reason']
)

TRANSFORMER_ROUTING_GAUGE = Gauge(
    'transformer_routing_percentage',
    'Current transformer routing percentage to Rust'
)


@dataclass
class TransformRequest:
    """Transform request data."""
    code: str
    language: str = "python"
    user_id: Optional[str] = None
    file_path: Optional[str] = None
    options: Dict[str, Any] = None


@dataclass
class TransformResponse:
    """Transform response data."""
    success: bool
    data: Dict[str, Any]
    implementation: str
    latency_ms: float
    used_fallback: bool = False
    error: Optional[str] = None


class TransformerClient:
    """Client for transformer service with migration support."""
    
    def __init__(self, config: Optional[TransformerRoutingConfig] = None):
        self.feature_flags = get_feature_flag_service()
        self.router = TransformerRouter(self.feature_flags)
        self.config = config or self.feature_flags.get_transformer_routing()
        
        # HTTP client with timeout
        timeout = aiohttp.ClientTimeout(
            total=self.config.timeout_ms / 1000
        )
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        # Update routing gauge
        TRANSFORMER_ROUTING_GAUGE.set(self.config.percentage)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def close(self):
        """Close HTTP session."""
        await self.session.close()
    
    async def transform(self, request: TransformRequest) -> TransformResponse:
        """Transform code using appropriate implementation."""
        start_time = time.time()
        user_id = request.user_id or "anonymous"
        
        # Determine which implementation to use
        use_rust = self.router.should_use_rust(user_id)
        implementation = "rust" if use_rust else "python"
        
        logger.info(
            f"Routing transform request for user {user_id} to {implementation} "
            f"(routing: {self.config.percentage}%)"
        )
        
        try:
            if use_rust:
                response = await self._transform_with_rust(request)
            else:
                response = await self._transform_with_python(request)
            
            # Record success metrics
            TRANSFORMER_REQUESTS.labels(implementation=implementation, status="success").inc()
            
        except Exception as e:
            logger.warning(
                f"Transform failed with {implementation}: {str(e)}, "
                f"attempting fallback"
            )
            
            # Record failure
            TRANSFORMER_REQUESTS.labels(implementation=implementation, status="failure").inc()
            
            # Attempt fallback if enabled
            if self.config.fallback.enabled:
                response = await self._attempt_fallback(request, implementation, str(e))
            else:
                response = TransformResponse(
                    success=False,
                    data={},
                    implementation=implementation,
                    latency_ms=(time.time() - start_time) * 1000,
                    error=str(e)
                )
        
        # Record latency
        latency = time.time() - start_time
        TRANSFORMER_LATENCY.labels(implementation=response.implementation).observe(latency)
        
        return response
    
    async def _transform_with_rust(self, request: TransformRequest) -> TransformResponse:
        """Transform using Rust implementation."""
        url = f"{self.config.rust_endpoint}/api/v1/transform/file"
        
        payload = {
            "file_path": request.file_path or f"temp_{request.user_id}.py",
            "stream_results": False,
            "config": request.options
        }
        
        async with self.session.post(url, json=payload) as resp:
            if resp.status not in [200, 201]:
                text = await resp.text()
                raise RuntimeError(f"Rust transformer error: {resp.status} - {text}")
            
            data = await resp.json()
            
            return TransformResponse(
                success=True,
                data=data,
                implementation="rust",
                latency_ms=0,  # Will be set by caller
                used_fallback=False
            )
    
    async def _transform_with_python(self, request: TransformRequest) -> TransformResponse:
        """Transform using Python implementation (internal)."""
        if not request.code:
            raise ValueError("Python transformer requires 'code' in TransformRequest")

        # Run in a worker thread to avoid blocking the event loop
        result = await asyncio.to_thread(
            transform_python_code,
            request.code,
            request.file_path or f"temp_{request.user_id or 'anonymous'}.py",
        )

        return TransformResponse(
            success=True,
            data=result,
            implementation="python",
            latency_ms=0,  # Will be set by caller
            used_fallback=False
        )
    
    async def _attempt_fallback(
        self,
        request: TransformRequest,
        failed_implementation: str,
        error_reason: str
    ) -> TransformResponse:
        """Attempt fallback to alternative implementation."""
        fallback_implementation = "python" if failed_implementation == "rust" else "rust"
        
        logger.info(
            f"Attempting fallback from {failed_implementation} to "
            f"{fallback_implementation}"
        )
        
        TRANSFORMER_FALLBACKS.labels(
            from_implementation=failed_implementation,
            to_implementation=fallback_implementation,
            reason=error_reason[:50]  # Truncate reason for label
        ).inc()
        
        try:
            if fallback_implementation == "rust":
                response = await self._transform_with_rust(request)
            else:
                response = await self._transform_with_python(request)
            
            response.used_fallback = True
            return response
            
        except Exception as e:
            logger.error(f"Fallback also failed: {str(e)}")
            
            return TransformResponse(
                success=False,
                data={},
                implementation=failed_implementation,
                latency_ms=0,  # Will be set by caller
                used_fallback=True,
                error=f"Both implementations failed. Original: {error_reason}, "
                      f"Fallback: {str(e)}"
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of both transformer implementations."""
        results = {
            "timestamp": time.time(),
            "routing_enabled": self.config.enabled,
            "routing_percentage": self.config.percentage,
            "implementations": {}
        }
        
        # Check Python (always available internally)
        results["implementations"]["python"] = {
            "healthy": True,
            "internal": True
        }
        
        # Check Rust
        try:
            url = f"{self.config.rust_endpoint}/health"
            async with self.session.get(url) as resp:
                results["implementations"]["rust"] = {
                    "healthy": resp.status == 200,
                    "status_code": resp.status,
                    "endpoint": self.config.rust_endpoint
                }
        except Exception as e:
            results["implementations"]["rust"] = {
                "healthy": False,
                "error": str(e),
                "endpoint": self.config.rust_endpoint
            }
        
        return results


class TransformerMetrics:
    """Metrics collector for transformer operations."""
    
    def __init__(self):
        self.start_times: Dict[str, float] = {}
    
    def start_request(self, request_id: str):
        """Start timing a request."""
        self.start_times[request_id] = time.time()
    
    def end_request(
        self,
        request_id: str,
        implementation: str,
        success: bool,
        used_fallback: bool = False
    ):
        """End timing and record metrics."""
        if request_id not in self.start_times:
            return
        
        duration = time.time() - self.start_times[request_id]
        del self.start_times[request_id]
        
        # Record metrics
        TRANSFORMER_LATENCY.labels(implementation=implementation).observe(duration)
        
        status = "success" if success else "failure"
        TRANSFORMER_REQUESTS.labels(
            implementation=implementation,
            status=status
        ).inc()
        
        if used_fallback:
            logger.info(f"Request {request_id} used fallback after {duration:.3f}s")


# Singleton client instance
_transformer_client: Optional[TransformerClient] = None


def get_transformer_client() -> TransformerClient:
    """Get or create transformer client singleton."""
    global _transformer_client
    
    if _transformer_client is None:
        _transformer_client = TransformerClient()
    
    return _transformer_client