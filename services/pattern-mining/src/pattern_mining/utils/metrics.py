"""
Production-ready metrics collector with Prometheus integration.

Provides a unified interface for application metrics with Prometheus
backend support and graceful fallback for testing environments.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, REGISTRY

logger = logging.getLogger(__name__)


@dataclass
class MetricDefinition:
    """Definition of a metric with metadata."""
    name: str
    description: str
    labels: List[str]
    metric_type: str  # counter, histogram, gauge, info


class PrometheusMetricsCollector:
    """
    Production metrics collector with Prometheus backend.
    
    Provides high-level interface for common metrics patterns:
    - Request counters with status codes
    - Latency histograms
    - Resource gauges (memory, connections)
    - Business metrics (patterns detected, files processed)
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or REGISTRY
        self._counters: Dict[str, Counter] = {}
        self._histograms: Dict[str, Histogram] = {}
        self._gauges: Dict[str, Gauge] = {}
        self._info_metrics: Dict[str, Info] = {}
        self._initialized = False
        self._init_core_metrics()
    
    def _init_core_metrics(self):
        """Initialize core application metrics."""
        try:
            # Pattern detection metrics
            self._counters['patterns_detected_total'] = Counter(
                'pattern_mining_patterns_detected_total',
                'Total patterns detected',
                ['pattern_type', 'severity', 'language'],
                registry=self.registry
            )
            
            self._counters['files_processed_total'] = Counter(
                'pattern_mining_files_processed_total',
                'Total files processed',
                ['language', 'status'],
                registry=self.registry
            )
            
            self._counters['api_requests_total'] = Counter(
                'pattern_mining_api_requests_total',
                'Total API requests',
                ['endpoint', 'method', 'status'],
                registry=self.registry
            )
            
            # Performance metrics
            self._histograms['request_duration_seconds'] = Histogram(
                'pattern_mining_request_duration_seconds',
                'Request duration in seconds',
                ['endpoint', 'method'],
                registry=self.registry
            )
            
            self._histograms['pattern_detection_duration_seconds'] = Histogram(
                'pattern_mining_detection_duration_seconds',
                'Pattern detection duration in seconds',
                ['detector_type'],
                registry=self.registry
            )
            
            self._histograms['file_processing_duration_seconds'] = Histogram(
                'pattern_mining_file_processing_duration_seconds',
                'File processing duration in seconds',
                ['language'],
                registry=self.registry
            )
            
            # Resource metrics
            self._gauges['active_connections'] = Gauge(
                'pattern_mining_active_connections',
                'Number of active connections',
                registry=self.registry
            )
            
            self._gauges['memory_usage_bytes'] = Gauge(
                'pattern_mining_memory_usage_bytes',
                'Memory usage in bytes',
                ['component'],
                registry=self.registry
            )
            
            self._gauges['cache_size'] = Gauge(
                'pattern_mining_cache_size',
                'Number of items in cache',
                ['cache_type'],
                registry=self.registry
            )
            
            # Service info
            self._info_metrics['service_info'] = Info(
                'pattern_mining_service_info',
                'Service information',
                registry=self.registry
            )
            
            self._initialized = True
            logger.info("Prometheus metrics initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Prometheus metrics: {e}")
            self._initialized = False
    
    async def increment_counter(
        self, 
        name: str, 
        value: int = 1, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Increment a counter metric."""
        if not self._initialized:
            return
        
        try:
            if name in self._counters:
                if labels:
                    self._counters[name].labels(**labels).inc(value)
                else:
                    self._counters[name].inc(value)
        except Exception as e:
            logger.warning(f"Failed to increment counter {name}: {e}")
    
    async def record_histogram(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a histogram observation."""
        if not self._initialized:
            return
        
        try:
            if name in self._histograms:
                if labels:
                    self._histograms[name].labels(**labels).observe(value)
                else:
                    self._histograms[name].observe(value)
        except Exception as e:
            logger.warning(f"Failed to record histogram {name}: {e}")
    
    async def set_gauge(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Set a gauge value."""
        if not self._initialized:
            return
        
        try:
            if name in self._gauges:
                if labels:
                    self._gauges[name].labels(**labels).set(value)
                else:
                    self._gauges[name].set(value)
        except Exception as e:
            logger.warning(f"Failed to set gauge {name}: {e}")
    
    async def update_info(self, name: str, info: Dict[str, str]) -> None:
        """Update info metric."""
        if not self._initialized:
            return
        
        try:
            if name in self._info_metrics:
                self._info_metrics[name].info(info)
        except Exception as e:
            logger.warning(f"Failed to update info {name}: {e}")
    
    # High-level convenience methods
    async def record_pattern_detected(
        self, 
        pattern_type: str, 
        severity: str, 
        language: str
    ) -> None:
        """Record a pattern detection."""
        await self.increment_counter(
            'patterns_detected_total',
            labels={
                'pattern_type': pattern_type,
                'severity': severity,
                'language': language
            }
        )
    
    async def record_file_processed(
        self, 
        language: str, 
        status: str, 
        duration_seconds: float
    ) -> None:
        """Record file processing completion."""
        await self.increment_counter(
            'files_processed_total',
            labels={'language': language, 'status': status}
        )
        await self.record_histogram(
            'file_processing_duration_seconds',
            duration_seconds,
            labels={'language': language}
        )
    
    async def record_api_request(
        self, 
        endpoint: str, 
        method: str, 
        status: str, 
        duration_seconds: float
    ) -> None:
        """Record API request completion."""
        await self.increment_counter(
            'api_requests_total',
            labels={
                'endpoint': endpoint,
                'method': method,
                'status': status
            }
        )
        await self.record_histogram(
            'request_duration_seconds',
            duration_seconds,
            labels={'endpoint': endpoint, 'method': method}
        )
    
    async def record_detection_duration(
        self, 
        detector_type: str, 
        duration_seconds: float
    ) -> None:
        """Record pattern detection duration."""
        await self.record_histogram(
            'pattern_detection_duration_seconds',
            duration_seconds,
            labels={'detector_type': detector_type}
        )
    
    async def update_cache_size(self, cache_type: str, size: int) -> None:
        """Update cache size gauge."""
        await self.set_gauge('cache_size', float(size), labels={'cache_type': cache_type})
    
    async def update_memory_usage(self, component: str, bytes_used: int) -> None:
        """Update memory usage gauge."""
        await self.set_gauge(
            'memory_usage_bytes', 
            float(bytes_used), 
            labels={'component': component}
        )
    
    def get_registry(self) -> CollectorRegistry:
        """Get the Prometheus registry for metrics export."""
        return self.registry


class FallbackMetricsCollector:
    """
    Fallback metrics collector for testing/development environments.
    
    Provides the same interface as PrometheusMetricsCollector but stores
    metrics in memory for testing purposes.
    """
    
    def __init__(self):
        self.counters: Dict[str, Dict[str, int]] = {}
        self.histograms: Dict[str, List[float]] = {}
        self.gauges: Dict[str, Dict[str, float]] = {}
        self.info: Dict[str, Dict[str, str]] = {}
    
    async def increment_counter(
        self, 
        name: str, 
        value: int = 1, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Increment a counter metric."""
        key = self._make_key(name, labels)
        if name not in self.counters:
            self.counters[name] = {}
        self.counters[name][key] = self.counters[name].get(key, 0) + value
    
    async def record_histogram(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a histogram observation."""
        if name not in self.histograms:
            self.histograms[name] = []
        self.histograms[name].append(value)
    
    async def set_gauge(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Set a gauge value."""
        key = self._make_key(name, labels)
        if name not in self.gauges:
            self.gauges[name] = {}
        self.gauges[name][key] = value
    
    async def update_info(self, name: str, info: Dict[str, str]) -> None:
        """Update info metric."""
        self.info[name] = info
    
    def _make_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Create a key from name and labels."""
        if not labels:
            return "default"
        return ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
    
    # High-level convenience methods (same interface as Prometheus collector)
    async def record_pattern_detected(
        self, pattern_type: str, severity: str, language: str
    ) -> None:
        await self.increment_counter(
            'patterns_detected_total',
            labels={'pattern_type': pattern_type, 'severity': severity, 'language': language}
        )
    
    async def record_file_processed(
        self, language: str, status: str, duration_seconds: float
    ) -> None:
        await self.increment_counter(
            'files_processed_total',
            labels={'language': language, 'status': status}
        )
        await self.record_histogram('file_processing_duration_seconds', duration_seconds)
    
    async def record_api_request(
        self, endpoint: str, method: str, status: str, duration_seconds: float
    ) -> None:
        await self.increment_counter(
            'api_requests_total',
            labels={'endpoint': endpoint, 'method': method, 'status': status}
        )
        await self.record_histogram('request_duration_seconds', duration_seconds)
    
    async def record_detection_duration(
        self, detector_type: str, duration_seconds: float
    ) -> None:
        await self.record_histogram(
            'pattern_detection_duration_seconds', duration_seconds
        )
    
    async def update_cache_size(self, cache_type: str, size: int) -> None:
        await self.set_gauge('cache_size', float(size), labels={'cache_type': cache_type})
    
    async def update_memory_usage(self, component: str, bytes_used: int) -> None:
        await self.set_gauge(
            'memory_usage_bytes', float(bytes_used), labels={'component': component}
        )


# Type alias for the metrics collector interface
MetricsCollector = PrometheusMetricsCollector | FallbackMetricsCollector

_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """
    Get or create the metrics collector singleton.
    
    Uses PrometheusMetricsCollector in production, FallbackMetricsCollector
    in test environments (when TESTING environment variable is set).
    """
    global _metrics_collector
    
    if _metrics_collector is None:
        import os
        
        # Use fallback collector in test environments
        if os.environ.get('TESTING') == '1' or os.environ.get('PYTEST_CURRENT_TEST'):
            _metrics_collector = FallbackMetricsCollector()
            logger.info("Using fallback metrics collector for testing")
        else:
            _metrics_collector = PrometheusMetricsCollector()
            logger.info("Using Prometheus metrics collector for production")
    
    return _metrics_collector


def reset_metrics_collector() -> None:
    """Reset the metrics collector singleton (for testing)."""
    global _metrics_collector
    _metrics_collector = None


