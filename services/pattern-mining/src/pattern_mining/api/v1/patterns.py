"""
Pattern Mining Service - Pattern Detection API Endpoints

Contract-compliant pattern detection endpoints implementing /api/v1/patterns/detect.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, HTTPException, Depends, Header, status
from fastapi.responses import JSONResponse

from ...contracts.models import (
    PatternInputV1,
    PatternOutputV1,
    PatternSummaryV1,
    PatternMetadataV1,
    ErrorResponseV1,
    DetectedPatternV1,
    PatternLocation,
    PatternRange,
    PatternImpact,
    PatternMetrics,
    DetectionMethod,
)
from ...contracts.validators import (
    validate_pattern_input,
    validate_pattern_output,
    create_contract_compliant_error,
)
from ...ast_processing import (
    ASTDataProcessor,
    PatternFeatureExtractor,
    EnhancedPatternDetector,
)
from ...ast_processing.pattern_detector import PatternDetectionResult
from ...performance import PerformanceStats
from .dependencies import (
    get_ast_processor,
    get_feature_extractor,
    get_pattern_detector,
    get_performance_stats,
)
from ...detectors.ml_detector import M<PERSON>atternDetector
from ...ml.gemini_analyzer import GeminiAnalyzer
from ...ml.gemini_client import GeminiClient
from ...detectors.fallback_detector import FallbackPatternDetector, DetectedPattern

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/patterns",
    tags=["patterns"],
    responses={
        400: {"description": "Bad request - Invalid input format"},
        422: {"description": "Validation error - Input doesn't match schema"},
        500: {"description": "Internal server error"},
        503: {"description": "Service temporarily unavailable"},
    }
)


@router.post(
    "/detect",
    response_model=PatternOutputV1,
    status_code=status.HTTP_200_OK,
    summary="Detect patterns in code",
    description="Detect design patterns, anti-patterns, security vulnerabilities, "
                "performance issues, code smells, and other patterns in AST data."
)
async def detect_patterns(
    request: PatternInputV1,
    x_correlation_id: Optional[str] = Header(None, description="Request correlation ID"),
    ast_processor: ASTDataProcessor = Depends(get_ast_processor),
    feature_extractor: PatternFeatureExtractor = Depends(get_feature_extractor),
    pattern_detector: EnhancedPatternDetector = Depends(get_pattern_detector),
    performance_stats: PerformanceStats = Depends(get_performance_stats),
) -> PatternOutputV1:
    """
    Contract-compliant pattern detection endpoint.
    
    Implements the pattern detection flow:
    1. Validate input against contract schema
    2. Process AST data to extract features
    3. Detect patterns using ML and rule-based methods
    4. Generate contract-compliant output
    
    Args:
        request: PatternInputV1 request from Repository Analysis service
        x_correlation_id: Optional correlation ID for request tracing
        
    Returns:
        PatternOutputV1 response with detected patterns
        
    Raises:
        HTTPException: For validation errors or processing failures
    """
    start_time = time.time()
    correlation_id = x_correlation_id or request.request_id or f"req_{datetime.utcnow().timestamp()}"
    
    try:
        # Log request
        logger.info(
            f"Pattern detection request received: "
            f"repository_id={request.repository_id}, "
            f"analysis_id={request.analysis_id}, "
            f"request_id={request.request_id}, "
            f"correlation_id={correlation_id}"
        )
        
        # Validate input
        try:
            validated_input = validate_pattern_input(request)
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=str(e)
            )
        
        # Process AST data
        logger.info("Processing AST data...")
        processed_data = await ast_processor.process_ast_data(
            ast_data=validated_input.ast_data,
            repository_id=validated_input.repository_id,
            analysis_id=validated_input.analysis_id
        )
        
        if processed_data.errors:
            logger.error(f"AST processing errors: {processed_data.errors}")
            if processed_data.stats.processed_files == 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to process any files from AST data"
                )
        
        # Extract features
        logger.info("Extracting pattern features...")
        features = feature_extractor.extract_features(processed_data)
        
        # Initialize ML components for advanced pattern detection
        logger.info("Initializing pattern detection components...")
        try:
            ml_detector = MLPatternDetector()
            use_ml_detection = True
            logger.info("ML detector initialized successfully")
        except Exception as ml_init_error:
            logger.warning(f"ML detector unavailable, will use fallback: {ml_init_error}")
            ml_detector = None
            use_ml_detection = False
        
        # Initialize fallback detector as backup
        fallback_detector = FallbackPatternDetector()
        
        # Detect patterns using both traditional and ML methods
        logger.info("Detecting patterns with AI integration...")
        detection_results = {}
        
        # Process each file with ML detection
        for file_path, file_data in processed_data.files.items():
            try:
                # Get features for this file
                file_features = features.get(file_path)
                if not file_features:
                    logger.warning(f"No features extracted for {file_path}")
                    continue
                
                # Prepare code content - extract from AST nodes
                code_content = getattr(file_data, 'content', '') or getattr(file_data, 'source_code', '')
                if not code_content and hasattr(file_data, 'ast_nodes') and file_data.ast_nodes:
                    # Extract code from AST nodes - prioritize nodes with text content
                    for node in file_data.ast_nodes:
                        if hasattr(node, 'text') and node.text:
                            code_content = node.text
                            break
                
                # If still no content, try to get it from processed data
                if not code_content:
                    # Try accessing the original AST data that came in
                    for ast_file in validated_input.ast_data.files:
                        if ast_file.file_path == file_path and ast_file.ast_nodes:
                            for node in ast_file.ast_nodes:
                                if node.text:
                                    code_content = node.text
                                    break
                            break
                
                if not code_content:
                    logger.warning(f"No code content found for {file_path}")
                    # Create empty result
                    detection_results[file_path] = PatternDetectionResult(
                        file_path=file_path,
                        patterns=[],
                        detection_time_ms=0
                    )
                    continue
                
                # Try ML detection first, fall back to rule-based if unavailable
                pattern_results = []
                
                if use_ml_detection and ml_detector:
                    try:
                        logger.info(f"Running ML pattern detection on {file_path}...")
                        pattern_results = await ml_detector.detect_patterns(
                            code=code_content,
                            language=file_features.language,
                            features=file_features.structural.__dict__ if hasattr(file_features, 'structural') else {}
                        )
                        logger.info(f"ML detection found {len(pattern_results)} patterns")
                    except Exception as ml_error:
                        logger.warning(f"ML detection failed for {file_path}, using fallback: {ml_error}")
                        pattern_results = []
                
                # Use fallback detector if ML failed or returned no patterns
                if not pattern_results:
                    logger.info(f"Using fallback pattern detection for {file_path}...")
                    fallback_patterns = fallback_detector.detect_patterns(
                        code=code_content,
                        language=file_features.language,
                        features=file_features.structural.__dict__ if hasattr(file_features, 'structural') else {}
                    )
                    logger.info(f"Fallback detection found {len(fallback_patterns)} patterns")
                    # Convert fallback patterns to DetectedPatternV1 format directly
                    detected_patterns = []
                    for fp in fallback_patterns:
                        try:
                            # Generate a proper pattern ID with exactly 16 alphanumeric chars
                            import uuid
                            pattern_id = f"pattern_{uuid.uuid4().hex[:16]}"
                            
                            # Ensure snippet is not too long
                            snippet = fp.code_snippet[:2000] if fp.code_snippet and len(fp.code_snippet) > 2000 else fp.code_snippet
                            
                            # Calculate end_line properly
                            snippet_lines = snippet.split('\n') if snippet else ['']
                            end_line = max(fp.line_number, fp.line_number + len(snippet_lines) - 1)
                            
                            # Create PatternLocation with proper validation
                            location = PatternLocation(
                                file_path=file_path,
                                range=PatternRange(
                                    start_line=max(1, fp.line_number),
                                    end_line=max(1, end_line),
                                    start_column=0,
                                    end_column=80
                                ),
                                symbol_name="",  # Optional field, empty string for contract compliance
                                context={
                                    "function_name": "",
                                    "class_name": "",
                                    "namespace": "",
                                    "module_name": file_path.split('/')[-1].split('.')[0]
                                },
                                snippet=snippet
                            )
                            
                            detected_pattern = DetectedPatternV1(
                                id=pattern_id,
                                pattern_type=fp.pattern_type,
                                pattern_name=fp.pattern_name,
                                confidence=fp.confidence,
                                severity=fp.severity,
                                locations=[location],
                                description=fp.description,
                                explanation=fp.description,
                                recommendations=[fp.recommendation],
                                examples=[],
                                related_patterns=[],
                                tags=fp.tags,
                                impact=PatternImpact(
                                    maintainability="negative" if "maintainability" in str(fp.tags) else "neutral",
                                    performance="negative" if "performance" in fp.pattern_type else "neutral",
                                    security="negative" if "security" in fp.pattern_type else "neutral",
                                    readability="negative" if "smell" in fp.pattern_type else "neutral",
                                    testability="neutral"
                                ),
                                metrics=PatternMetrics(
                                    complexity_increase=2 if "complex" in str(fp.tags) else 0,
                                    lines_affected=len(snippet.split('\n')) if snippet else 1,
                                    files_affected=1,
                                    estimated_fix_time_minutes=15
                                ),
                                detection_method=DetectionMethod(
                                    algorithm="rule_based",
                                    model_name="fallback_detector_v1",
                                    rule_set="comprehensive_rules",
                                    features_used=fp.tags
                                )
                            )
                            detected_patterns.append(detected_pattern)
                            logger.debug(f"Successfully converted pattern: {pattern_id}")
                            
                        except Exception as pattern_error:
                            logger.error(f"Failed to convert pattern {fp.pattern_id}: {pattern_error}")
                            continue
                    
                    # Skip the ML pattern conversion since fallback patterns are ready
                    detection_results[file_path] = PatternDetectionResult(
                        file_path=file_path,
                        patterns=detected_patterns,
                        detection_time_ms=50  # Approximate time
                    )
                    logger.info(f"Fallback detection completed for {file_path}: {len(detected_patterns)} patterns found")
                    if detected_patterns:
                        logger.info(f"Sample pattern: {detected_patterns[0].pattern_name} (confidence: {detected_patterns[0].confidence})")
                    continue  # Skip to next file
                
                # Convert ML results to contract format
                detected_patterns = []
                for result in pattern_results:
                    # Convert PatternResult to DetectedPatternV1
                    if hasattr(result, 'pattern_name'):
                        detected_pattern = DetectedPatternV1(
                            id=f"pattern_{uuid.uuid4().hex[:16]}",
                            pattern_type=getattr(result, 'pattern_type', 'design_pattern'),
                            pattern_name=result.pattern_name,
                            confidence=getattr(result, 'confidence', 0.8),
                            severity=getattr(result, 'severity', 'medium'),
                            locations=[
                                PatternLocation(
                                    file_path=file_path,
                                    range=PatternRange(
                                        start_line=getattr(result, 'start_line', 1),
                                        end_line=getattr(result, 'end_line', len(code_content.split('\n'))),
                                        start_column=0,
                                        end_column=0
                                    ),
                                    symbol_name=getattr(result, 'symbol_name', "") or "",
                                    context={
                                        "function_name": getattr(result, 'function_name', "") or "",
                                        "class_name": getattr(result, 'class_name', "") or "",
                                        "namespace": getattr(result, 'namespace', "") or "",
                                        "module_name": file_path.split('/')[-1].split('.')[0]
                                    },
                                    snippet=code_content[:500] if len(code_content) <= 500 else code_content[:497] + "..."
                                )
                            ],
                            description=getattr(result, 'description', f"{result.pattern_name} pattern detected"),
                            explanation=getattr(result, 'explanation', f"AI-detected {result.pattern_name} pattern in code"),
                            recommendations=getattr(result, 'recommendations', ["Review pattern implementation", "Consider best practices"]),
                            examples=[],
                            related_patterns=[],
                            tags=getattr(result, 'tags', []),
                            impact=PatternImpact(
                                maintainability=getattr(result, 'maintainability_impact', 'neutral'),
                                performance=getattr(result, 'performance_impact', 'neutral'),
                                security=getattr(result, 'security_impact', 'neutral'),
                                readability=getattr(result, 'readability_impact', 'neutral'),
                                testability=getattr(result, 'testability_impact', 'neutral')
                            ) if hasattr(result, 'maintainability_impact') else None,
                            metrics=PatternMetrics(
                                complexity_increase=getattr(result, 'complexity_increase', 0),
                                lines_affected=getattr(result, 'lines_affected', 1),
                                files_affected=1,
                                estimated_fix_time_minutes=getattr(result, 'fix_time_minutes', 15)
                            ) if hasattr(result, 'complexity_increase') else None,
                            detection_method=DetectionMethod(
                                algorithm="ml_hybrid",
                                model_name="gemini-2.5-flash",
                                rule_set="ai_enhanced_detection",
                                features_used=list(file_features.structural.__dict__.keys()) if hasattr(file_features, 'structural') else []
                            )
                        )
                        detected_patterns.append(detected_pattern)
                
                # Create detection result
                detection_results[file_path] = PatternDetectionResult(
                    file_path=file_path,
                    patterns=detected_patterns,
                    detection_time_ms=50  # Approximate time
                )
                
                logger.info(f"ML detection completed for {file_path}: {len(detected_patterns)} patterns found")
                
            except Exception as e:
                logger.error(f"ML pattern detection failed for {file_path}: {e}")
                # Fallback to traditional detection for this file
                detection_results[file_path] = PatternDetectionResult(
                    file_path=file_path,
                    patterns=[],
                    detection_time_ms=0,
                    errors=[str(e)]
                )
        
        # Aggregate patterns from all files
        all_patterns = []
        total_detection_time = 0
        
        for file_path, result in detection_results.items():
            logger.info(f"File {file_path}: {len(result.patterns)} patterns")
            all_patterns.extend(result.patterns)
            total_detection_time += result.detection_time_ms
        
        logger.info(f"Total patterns before filtering: {len(all_patterns)}")
        
        # Apply global limits
        max_patterns = validated_input.detection_config.performance_limits.get(
            'max_patterns_total', 1000
        )
        if len(all_patterns) > max_patterns:
            # Sort by severity and confidence
            all_patterns.sort(
                key=lambda p: (
                    _severity_score(p.severity),
                    p.confidence
                ),
                reverse=True
            )
            all_patterns = all_patterns[:max_patterns]
        
        # Generate summary
        summary = _generate_pattern_summary(all_patterns)
        
        # Generate metadata
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        # Create performance stats model
        from ...contracts.models import PerformanceStats as PerfStatsModel
        perf_stats = PerfStatsModel(
            files_processed=processed_data.stats.processed_files if hasattr(processed_data, 'stats') and hasattr(processed_data.stats, 'processed_files') else 1,
            patterns_detected=len(all_patterns),
            avg_confidence=sum(p.confidence for p in all_patterns) / len(all_patterns) if all_patterns else 0.0,
            memory_peak_mb=processed_data.stats.memory_usage_mb if hasattr(processed_data, 'stats') and hasattr(processed_data.stats, 'memory_usage_mb') else None,
            cache_hit_ratio=processed_data.stats.cache_hits / (processed_data.stats.cache_hits + processed_data.stats.cache_misses) if hasattr(processed_data, 'stats') and hasattr(processed_data.stats, 'cache_hits') and (processed_data.stats.cache_hits + processed_data.stats.cache_misses) > 0 else None,
        )
        
        metadata = PatternMetadataV1(
            version="1.0.0",
            timestamp=datetime.utcnow(),
            processing_time_ms=processing_time_ms,
            model_versions={
                "pattern_detector": "1.0.0",
                "ast_processor": "1.0.0",
                "feature_extractor": "1.0.0",
            },
            performance_stats=perf_stats,
            detection_config={
                "enabled_detectors": validated_input.detection_config.enabled_detectors,
                "confidence_threshold": validated_input.detection_config.confidence_threshold,
                "language_filters": list(processed_data.language_distribution.keys()) if hasattr(processed_data, 'language_distribution') else ["python"],
            }
        )
        
        # Create response
        response = PatternOutputV1(
            request_id=validated_input.request_id,
            repository_id=validated_input.repository_id,
            analysis_id=validated_input.analysis_id,
            patterns=all_patterns,
            summary=summary,
            metadata=metadata
        )
        
        # Validate output
        try:
            validated_output = validate_pattern_output(response)
        except Exception as e:
            logger.error(f"Output validation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal error: Invalid output format"
            )
        
        # Update performance stats
        await performance_stats.record_request(
            endpoint="/api/v1/patterns/detect",
            method="POST",
            status_code=200,
            response_time_ms=processing_time_ms,
            patterns_detected=len(all_patterns)
        )
        
        logger.info(
            f"Pattern detection completed: "
            f"patterns={len(all_patterns)}, "
            f"processing_time={processing_time_ms}ms, "
            f"request_id={validated_output.request_id}"
        )
        
        return validated_output
        
    except HTTPException:
        # Re-raise FastAPI exceptions
        raise
        
    except Exception as e:
        logger.error(f"Pattern detection error: {e}", exc_info=True)
        
        # Create contract-compliant error response
        error = create_contract_compliant_error(
            service="pattern-mining",
            error_type="internal",
            message=f"Pattern detection failed: {str(e)}",
            retryable=True,
            user_message="An error occurred while detecting patterns. Please try again.",
            correlation_id=correlation_id,
            context={
                "repository_id": request.repository_id,
                "analysis_id": request.analysis_id,
                "request_id": request.request_id,
            }
        )
        
        # Update performance stats
        processing_time_ms = int((time.time() - start_time) * 1000)
        await performance_stats.record_request(
            endpoint="/api/v1/patterns/detect",
            method="POST",
            status_code=500,
            response_time_ms=processing_time_ms,
            error=str(e)
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error.model_dump(mode='json')
        )


def _generate_pattern_summary(patterns: list) -> PatternSummaryV1:
    """Generate pattern summary from detected patterns"""
    
    # Count patterns by type
    by_type = {}
    by_severity = {"info": 0, "low": 0, "medium": 0, "high": 0, "critical": 0}
    by_language = {}
    
    for pattern in patterns:
        # By type
        pattern_type = pattern.pattern_type
        by_type[pattern_type] = by_type.get(pattern_type, 0) + 1
        
        # By severity
        by_severity[pattern.severity] = by_severity.get(pattern.severity, 0) + 1
        
        # By language (extract from file path)
        for location in pattern.locations:
            # Simple language detection from file extension
            ext = location.file_path.split('.')[-1] if '.' in location.file_path else 'unknown'
            lang = _extension_to_language(ext)
            by_language[lang] = by_language.get(lang, 0) + 1
    
    # Calculate top patterns
    pattern_counts = {}
    for pattern in patterns:
        pattern_counts[pattern.pattern_name] = pattern_counts.get(pattern.pattern_name, 0) + 1
    
    top_patterns = [
        {
            "pattern_name": name,
            "count": count,
            "avg_confidence": sum(p.confidence for p in patterns if p.pattern_name == name) / count
        }
        for name, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    ]
    
    # Calculate quality scores
    total_patterns = len(patterns)
    critical_patterns = by_severity.get("critical", 0)
    high_patterns = by_severity.get("high", 0)
    
    # Simple scoring algorithm
    overall_score = max(0, 100 - (critical_patterns * 10) - (high_patterns * 5) - (total_patterns * 0.5))
    security_score = max(0, 100 - (sum(1 for p in patterns if p.pattern_type == "security_vulnerability") * 20))
    maintainability_score = max(0, 100 - (sum(1 for p in patterns if p.pattern_type in ["anti_pattern", "code_smell"]) * 5))
    
    # Generate recommendations
    recommendations = []
    
    if critical_patterns > 0:
        recommendations.append({
            "priority": "critical",
            "category": "security",
            "description": f"Address {critical_patterns} critical security vulnerabilities immediately",
            "affected_files": len(set(loc.file_path for p in patterns if p.severity == "critical" for loc in p.locations)),
            "estimated_effort_hours": critical_patterns * 4.0
        })
    
    if by_type.get("anti_pattern", 0) > 5:
        recommendations.append({
            "priority": "high",
            "category": "architecture",
            "description": "Refactor code to eliminate anti-patterns and improve design",
            "affected_files": len(set(loc.file_path for p in patterns if p.pattern_type == "anti_pattern" for loc in p.locations)),
            "estimated_effort_hours": by_type["anti_pattern"] * 2.0
        })
    
    if by_type.get("performance_issue", 0) > 3:
        recommendations.append({
            "priority": "medium",
            "category": "performance",
            "description": "Optimize performance bottlenecks for better scalability",
            "affected_files": len(set(loc.file_path for p in patterns if p.pattern_type == "performance_issue" for loc in p.locations)),
            "estimated_effort_hours": by_type["performance_issue"] * 3.0
        })
    
    return PatternSummaryV1(
        total_patterns=total_patterns,
        by_type=by_type,
        by_severity=by_severity,
        by_language=by_language,
        top_patterns=top_patterns,
        quality_scores={
            "overall_score": overall_score,
            "maintainability_score": maintainability_score,
            "security_score": security_score,
            "performance_score": max(0, 100 - (by_type.get("performance_issue", 0) * 10)),
            "readability_score": max(0, 100 - (by_type.get("code_smell", 0) * 3)),
            "testability_score": _calculate_testability_score(patterns)
        },
        recommendations=recommendations,
        trends={}  # No historical data available
    )


def _calculate_testability_score(patterns: List[DetectedPatternV1]) -> float:
    """
    Calculate testability score based on detected patterns and their impact.
    
    Score factors:
    - Test patterns found: +10 each (max +30)
    - High coupling patterns: -15 each
    - God class/Long method patterns: -10 each  
    - Singleton patterns: -5 each
    - Good architectural patterns: +5 each
    - Missing error handling: -8 each
    
    Base score: 75.0, clamped to [0, 100]
    """
    base_score = 75.0
    score_adjustments = 0
    
    test_patterns = 0
    high_coupling = 0
    god_class_patterns = 0
    singleton_patterns = 0
    good_arch_patterns = 0
    missing_error_handling = 0
    
    for pattern in patterns:
        pattern_name_lower = pattern.pattern_name.lower()
        pattern_type = pattern.pattern_type
        
        # Positive factors
        if pattern_type == "test_pattern":
            test_patterns += 1
        elif pattern_type == "architectural_pattern" and pattern.severity == "info":
            good_arch_patterns += 1
        
        # Negative factors
        elif "coupling" in pattern_name_lower and pattern.severity in ["high", "critical"]:
            high_coupling += 1
        elif "god class" in pattern_name_lower or "long method" in pattern_name_lower:
            god_class_patterns += 1
        elif "singleton" in pattern_name_lower:
            singleton_patterns += 1
        elif "error handling" in pattern_name_lower and "missing" in pattern_name_lower:
            missing_error_handling += 1
    
    # Apply scoring rules
    score_adjustments += min(test_patterns * 10, 30)  # Max +30 for test patterns
    score_adjustments -= high_coupling * 15
    score_adjustments -= god_class_patterns * 10
    score_adjustments -= singleton_patterns * 5
    score_adjustments += good_arch_patterns * 5
    score_adjustments -= missing_error_handling * 8
    
    final_score = base_score + score_adjustments
    return max(0.0, min(100.0, final_score))


def _severity_score(severity: str) -> int:
    """Convert severity to numeric score for sorting"""
    return {"critical": 5, "high": 4, "medium": 3, "low": 2, "info": 1}.get(severity, 0)


def _extension_to_language(ext: str) -> str:
    """Map file extension to language"""
    mapping = {
        "py": "python",
        "js": "javascript",
        "ts": "typescript",
        "java": "java",
        "rs": "rust",
        "go": "go",
        "cs": "csharp",
        "cpp": "cpp",
        "c": "c",
        "rb": "ruby",
        "php": "php",
        "kt": "kotlin",
        "swift": "swift",
        "scala": "scala",
    }
    return mapping.get(ext, "unknown")


# ==================== SIMPLE PATTERN DETECTION ENDPOINT ====================

from pydantic import BaseModel, Field
from typing import List
import uuid
import ast
import json

class SimplePatternRequest(BaseModel):
    """Simple pattern detection request for easy API usage"""
    code: str = Field(..., min_length=1, description="Source code to analyze")
    language: str = Field(..., description="Programming language")
    file_path: Optional[str] = Field(None, description="Optional file path")

class SimplePatternResponse(BaseModel):
    """Simple pattern detection response"""
    patterns: List[Dict[str, Any]] = Field(..., description="Detected patterns")
    summary: Dict[str, Any] = Field(..., description="Analysis summary")
    request_id: str = Field(..., description="Request ID for tracking")

@router.post(
    "/detect-simple",
    response_model=SimplePatternResponse,
    status_code=status.HTTP_200_OK,
    summary="Simple pattern detection",
    description="Simplified endpoint for basic pattern detection in code snippets",
    responses={
        200: {"description": "Patterns detected successfully"},
        400: {"description": "Invalid request"},
        422: {"description": "Validation error"},
        500: {"description": "Internal server error"},
    }
)
async def detect_patterns_simple(
    request: SimplePatternRequest,
    x_correlation_id: Optional[str] = Header(None, description="Request correlation ID"),
    ast_processor: ASTDataProcessor = Depends(get_ast_processor),
    feature_extractor: PatternFeatureExtractor = Depends(get_feature_extractor),
    pattern_detector: EnhancedPatternDetector = Depends(get_pattern_detector),
    performance_stats: PerformanceStats = Depends(get_performance_stats),
) -> SimplePatternResponse:
    """
    Simple pattern detection endpoint for basic usage.
    
    This endpoint provides an easier interface for pattern detection
    by handling the complex contract creation internally.
    
    Args:
        request: SimplePatternRequest with code and language
        x_correlation_id: Optional correlation ID
        
    Returns:
        SimplePatternResponse with detected patterns
    """
    start_time = time.time()
    request_id = f"req_{uuid.uuid4().hex[:16]}"
    
    try:
        # Log request
        logger.info(f"Simple pattern detection request: language={request.language}, code_length={len(request.code)}")
        
        # Create AST data from code
        try:
            if request.language.lower() == 'python':
                # Parse Python AST
                tree = ast.parse(request.code)
                ast_data = {
                    "type": "Module",
                    "body": [{"type": node.__class__.__name__} for node in tree.body],
                    "language": "python",
                    "source_code": request.code
                }
            else:
                # For non-Python languages, create a simple AST structure
                ast_data = {
                    "type": "Program",
                    "language": request.language.lower(),
                    "source_code": request.code,
                    "body": []
                }
        except Exception as ast_error:
            logger.warning(f"AST parsing failed, using simple structure: {ast_error}")
            ast_data = {
                "type": "Program", 
                "language": request.language.lower(),
                "source_code": request.code,
                "body": []
            }
        
        # Create contract-compliant input
        from ...contracts.models import PatternInputV1, ASTDataV1, DetectionConfigV1, ContextV1, FileASTData, PatternASTNode, PatternRange
        import hashlib
        
        try:
            # Create SHA-256 hash for the code
            content_hash = hashlib.sha256(request.code.encode()).hexdigest()
            
            # Create pattern AST node from the simple AST data
            pattern_node = PatternASTNode(
                id="root",
                type=ast_data["type"],
                name="root",
                range=PatternRange(
                    start_line=1, 
                    end_line=len(request.code.split('\n')),
                    start_column=0,
                    end_column=0
                ),
                parent_id="",
                children_ids=[],
                properties={},
                text=request.code[:1000],  # Limit text size
                annotations=[]
            )
            
            # Create file AST data
            file_ast_data = FileASTData(
                file_path=request.file_path or "untitled.py",
                language=request.language.lower(),
                content_hash=content_hash,
                size_bytes=len(request.code),
                ast_nodes=[pattern_node],
                metrics={
                    "lines_of_code": len(request.code.split('\n')),
                    "complexity": 1,
                    "function_count": request.code.count('def ') if request.language.lower() == 'python' else 1
                },
                symbols=[],
                imports=[],
                code_features={}
            )
            
            # Create AST data with repository metrics
            ast_data_v1 = ASTDataV1(
                files=[file_ast_data],
                repository_metrics={
                    "total_files": 1,
                    "total_lines": len(request.code.split('\n')),
                    "languages": {request.language.lower(): {"files": 1, "lines": len(request.code.split('\n'))}}
                }
            )
            
            pattern_input = PatternInputV1(
                repository_id=f"repo_{uuid.uuid4().hex[:16]}",
                analysis_id=f"analysis_{uuid.uuid4().hex[:16]}",
                request_id=request_id,
                ast_data=ast_data_v1,
                detection_config=DetectionConfigV1(
                    enabled_detectors=["design_patterns", "anti_patterns", "security_vulnerabilities"],
                    confidence_threshold=0.6,
                    language_specific={},
                    performance_limits={},
                    output_preferences={}
                ),
                context=ContextV1(
                    user_id="",
                    organization_id="",
                    priority="normal",
                    callback_url="",
                    tags=["simple-api"]
                )
            )
        except Exception as contract_error:
            logger.error(f"Contract creation failed: {contract_error}")
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create analysis request: {str(contract_error)}"
            )
        
        # Call the main detection endpoint internally
        try:
            result = await detect_patterns(
                request=pattern_input,
                x_correlation_id=x_correlation_id,
                ast_processor=ast_processor,
                feature_extractor=feature_extractor,
                pattern_detector=pattern_detector,
                performance_stats=performance_stats
            )
        except Exception as detection_error:
            logger.error(f"Pattern detection failed: {detection_error}")
            raise HTTPException(
                status_code=500,
                detail=f"Pattern detection failed: {str(detection_error)}"
            )
        
        # Convert complex result to simple format
        simple_patterns = []
        for pattern in result.patterns:
            simple_pattern = {
                "pattern_name": pattern.pattern_name,
                "pattern_type": pattern.pattern_type,
                "description": pattern.description,
                "severity": pattern.severity,
                "confidence": pattern.confidence,
                "locations": [
                    {
                        "line": loc.range.start_line if loc.range else 1,
                        "column": loc.range.start_column if loc.range else 1,
                        "snippet": loc.snippet[:200] if loc.snippet else None
                    }
                    for loc in pattern.locations
                ]
            }
            simple_patterns.append(simple_pattern)
        
        # Create simple summary
        processing_time = (time.time() - start_time) * 1000
        summary = {
            "total_patterns": len(simple_patterns),
            "processing_time_ms": round(processing_time, 2),
            "language": request.language,
            "code_length": len(request.code),
            "quality_scores": result.summary.quality_scores if result.summary else {},
            "by_severity": result.summary.by_severity if result.summary else {},
            "by_type": result.summary.by_type if result.summary else {}
        }
        
        return SimplePatternResponse(
            patterns=simple_patterns,
            summary=summary,
            request_id=request_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Simple pattern detection failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Pattern detection failed: {str(e)}"
        )