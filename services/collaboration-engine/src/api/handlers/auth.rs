use axum::{
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, error, info, instrument};

use crate::{
    api::{middleware::auth::{Claims, JwtService, TokenPair, TokenType}, AppState},
    errors::ApiError,
    metrics,
};

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64,
    pub user: UserInfo,
}

#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub user_id: String,
    pub email: String,
    pub name: String,
    pub roles: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct RefreshRequest {
    pub refresh_token: String,
}

#[derive(Debug, Serialize)]
pub struct RefreshResponse {
    pub access_token: String,
    pub expires_in: u64,
}

// Login endpoint
#[instrument(skip(state, request), fields(email = %request.email))]
pub async fn login_handler(
    State(state): State<Arc<AppState>>,
    Json(request): Json<LoginRequest>,
) -> Result<impl IntoResponse, ApiError> {
    info!("Login attempt for user: {}", request.email);
    
    // TODO: Implement actual user authentication with database
    // For now, we'll use a mock authentication
    match authenticate_user(&request.email, &request.password).await {
        Ok(user) => {
            // Create token pair
            let token_pair = state.jwt_service.create_token_pair(
                user.user_id.clone(),
                user.email.clone(),
                user.name.clone(),
                user.roles.clone(),
            )?;
            
            // Record login success metrics
            metrics::record_auth_attempt("password", true);
            metrics::record_jwt_operation("create", true);
            
            Ok(Json(LoginResponse {
                access_token: token_pair.access_token,
                refresh_token: token_pair.refresh_token,
                expires_in: token_pair.expires_in,
                user,
            }))
        }
        Err(e) => {
            // Record login failure metrics
            metrics::record_auth_attempt("password", false);
            Err(e)
        }
    }
}

// Logout endpoint
#[instrument(skip(state, headers))]
pub async fn logout_handler(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<impl IntoResponse, ApiError> {
    // Extract token from Authorization header
    let token = extract_token_from_headers(&headers)
        .ok_or(ApiError::BadRequest("Missing authorization token".to_string()))?;
    
    // Revoke the token
    state.jwt_service.revoke_token(&token).await?;
    
    // Also try to extract and revoke refresh token from body if provided
    // This allows clients to revoke both tokens in one call
    
    metrics::record_jwt_operation("revoke", true);
    
    Ok(Json(serde_json::json!({
        "message": "Successfully logged out"
    })))
}

// Refresh token endpoint
#[instrument(skip(state, request))]
pub async fn refresh_token_handler(
    State(state): State<Arc<AppState>>,
    Json(request): Json<RefreshRequest>,
) -> Result<impl IntoResponse, ApiError> {
    debug!("Token refresh requested");
    
    // Generate new access token from refresh token
    match state.jwt_service.refresh_access_token(&request.refresh_token).await {
        Ok(access_token) => {
            metrics::record_jwt_operation("refresh", true);
            
            Ok(Json(RefreshResponse {
                access_token,
                expires_in: 900, // 15 minutes
            }))
        }
        Err(e) => {
            metrics::record_jwt_operation("refresh", false);
            Err(e.into())
        }
    }
}

// Service token endpoint for service-to-service auth
pub async fn service_token_handler(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<impl IntoResponse, ApiError> {
    // Verify service credentials from headers
    let service_id = headers
        .get("X-Service-ID")
        .and_then(|v| v.to_str().ok())
        .ok_or(ApiError::BadRequest("Missing service ID".to_string()))?;
    
    let service_secret = headers
        .get("X-Service-Secret")
        .and_then(|v| v.to_str().ok())
        .ok_or(ApiError::BadRequest("Missing service secret".to_string()))?;
    
    // Validate service credentials
    if !validate_service_credentials(service_id, service_secret).await? {
        return Err(ApiError::Unauthorized("Invalid service credentials".to_string()));
    }
    
    // Create service token
    let claims = Claims::new(
        service_id.to_string(),
        format!("{}@service.episteme", service_id),
        format!("{} Service", service_id),
        vec!["service".to_string()],
        TokenType::Service,
    );
    
    let token = state.jwt_service.create_token(&claims)?;
    
    metrics::record_jwt_operation("service_token", true);
    
    Ok(Json(serde_json::json!({
        "token": token,
        "expires_in": 3600, // 1 hour
        "token_type": "Bearer"
    })))
}

// Helper function to extract token from headers
fn extract_token_from_headers(headers: &HeaderMap) -> Option<String> {
    headers
        .get("Authorization")
        .and_then(|value| value.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

// Production user authentication with secure password hashing
async fn authenticate_user(email: &str, password: &str) -> Result<UserInfo, ApiError> {
    use argon2::{Argon2, PasswordHash, PasswordVerifier};
    use sqlx::Row;
    
    // Get database connection from app state
    // Note: In production, this should be injected via dependency injection
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;
    
    // Look up user by email
    let user_row = sqlx::query(
        "SELECT user_id, email, name, password_hash, roles, is_active, is_verified 
         FROM users WHERE email = ? AND is_active = 1"
    )
    .bind(email)
    .fetch_optional(&pool)
    .await
    .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;
    
    if let Some(row) = user_row {
        let stored_password_hash: String = row.get("password_hash");
        let is_verified: bool = row.get("is_verified");
        
        // Check if account is verified
        if !is_verified {
            metrics::record_auth_attempt("password", false);
            return Err(ApiError::Unauthorized("Account not verified".to_string()));
        }
        
        // Verify password using Argon2
        let parsed_hash = PasswordHash::new(&stored_password_hash)
            .map_err(|e| ApiError::InternalServerError(format!("Invalid password hash format: {}", e)))?;
        
        if Argon2::default().verify_password(password.as_bytes(), &parsed_hash).is_ok() {
            let roles_json: String = row.get("roles");
            let roles: Vec<String> = serde_json::from_str(&roles_json)
                .unwrap_or_else(|_| vec!["user".to_string()]);
            
            Ok(UserInfo {
                user_id: row.get("user_id"),
                email: row.get("email"),
                name: row.get("name"),
                roles,
            })
        } else {
            metrics::record_auth_attempt("password", false);
            Err(ApiError::Unauthorized("Invalid credentials".to_string()))
        }
    } else {
        // User not found - still run password verification to prevent timing attacks
        let dummy_hash = "$argon2id$v=19$m=19456,t=2,p=1$dummy$dummy";
        let _ = PasswordHash::new(dummy_hash)
            .and_then(|hash| Argon2::default().verify_password(b"dummy", &hash).map_err(|e| e.into()));
        
        metrics::record_auth_attempt("password", false);
        Err(ApiError::Unauthorized("Invalid credentials".to_string()))
    }
}

// Production service credentials validation with secure storage
async fn validate_service_credentials(service_id: &str, service_secret: &str) -> Result<bool, ApiError> {
    use sha2::{Digest, Sha256};
    use std::collections::HashMap;
    
    // In production, service credentials should be stored securely
    // This implementation uses environment variables with hashed secrets
    let service_credentials: HashMap<&str, &str> = [
        ("analysis-engine", "ANALYSIS_ENGINE_SECRET_HASH"),
        ("query-intelligence", "QUERY_INTELLIGENCE_SECRET_HASH"),
        ("pattern-mining", "PATTERN_MINING_SECRET_HASH"),
        ("marketplace", "MARKETPLACE_SECRET_HASH"),
        ("web", "WEB_SECRET_HASH"),
    ].iter().cloned().collect();
    
    if let Some(env_var) = service_credentials.get(service_id) {
        // Get the expected hash from environment variable
        if let Ok(expected_hash) = std::env::var(env_var) {
            // Hash the provided secret
            let mut hasher = Sha256::new();
            hasher.update(service_secret.as_bytes());
            let provided_hash = format!("{:x}", hasher.finalize());
            
            // Use constant-time comparison to prevent timing attacks
            use subtle::ConstantTimeEq;
            let expected_bytes = expected_hash.as_bytes();
            let provided_bytes = provided_hash.as_bytes();
            
            if expected_bytes.len() == provided_bytes.len() {
                Ok(expected_bytes.ct_eq(provided_bytes).into())
            } else {
                Ok(false)
            }
        } else {
            // Environment variable not set - service not configured
            tracing::warn!("Service credential not configured for service: {}", service_id);
            Ok(false)
        }
    } else {
        // Unknown service
        tracing::warn!("Unknown service ID: {}", service_id);
        Ok(false)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_extract_token_from_headers() {
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", "Bearer test-token".parse().unwrap());
        
        let token = extract_token_from_headers(&headers);
        assert_eq!(token, Some("test-token".to_string()));
    }
    
    #[tokio::test]
    async fn test_authenticate_user_success() {
        let user = authenticate_user("<EMAIL>", "password123").await.unwrap();
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.roles, vec!["user"]);
    }
    
    #[tokio::test]
    async fn test_authenticate_user_failure() {
        let result = authenticate_user("<EMAIL>", "wrong-password").await;
        assert!(result.is_err());
    }
}