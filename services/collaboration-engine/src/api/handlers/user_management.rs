//! User Management Handlers for Collaboration Engine
//!
//! This module provides production-ready user management functionality including:
//! - User registration with secure password hashing
//! - Email verification
//! - Password reset functionality
//! - User profile management
//! - Account deactivation

use crate::api::errors::ApiError;
use crate::api::middleware::auth::{AuthUser, Claims, TokenType};
use crate::state::AppState;
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, warn};
use uuid::Uuid;

#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub email: String,
    pub password: String,
    pub name: String,
    pub confirm_password: String,
}

#[derive(Debug, Serialize)]
pub struct RegisterResponse {
    pub user_id: String,
    pub email: String,
    pub name: String,
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct PasswordResetRequest {
    pub email: String,
}

#[derive(Debug, Deserialize)]
pub struct PasswordResetConfirmRequest {
    pub token: String,
    pub new_password: String,
    pub confirm_password: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateProfileRequest {
    pub name: Option<String>,
    pub email: Option<String>,
}

/// Register a new user with secure password hashing
pub async fn register_user(
    State(state): State<Arc<AppState>>,
    Json(request): Json<RegisterRequest>,
) -> Result<impl IntoResponse, ApiError> {
    use argon2::{Argon2, PasswordHasher};
    use argon2::password_hash::{rand_core::OsRng, SaltString};
    use sqlx::Row;

    info!("User registration attempt for email: {}", request.email);

    // Validate input
    if request.password != request.confirm_password {
        return Err(ApiError::BadRequest("Passwords do not match".to_string()));
    }

    if request.password.len() < 8 {
        return Err(ApiError::BadRequest("Password must be at least 8 characters long".to_string()));
    }

    if !is_valid_email(&request.email) {
        return Err(ApiError::BadRequest("Invalid email format".to_string()));
    }

    // Get database connection
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    // Check if user already exists
    let existing_user = sqlx::query("SELECT user_id FROM users WHERE email = ?")
        .bind(&request.email)
        .fetch_optional(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;

    if existing_user.is_some() {
        return Err(ApiError::Conflict("User with this email already exists".to_string()));
    }

    // Hash password using Argon2
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();
    let password_hash = argon2
        .hash_password(request.password.as_bytes(), &salt)
        .map_err(|e| ApiError::InternalServerError(format!("Password hashing failed: {}", e)))?
        .to_string();

    // Generate user ID and verification token
    let user_id = Uuid::new_v4().to_string();
    let verification_token = Uuid::new_v4().to_string();

    // Insert user into database
    let roles_json = serde_json::to_string(&vec!["user"]).unwrap();
    
    sqlx::query(
        "INSERT INTO users (user_id, email, name, password_hash, roles, is_active, is_verified, verification_token, created_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))"
    )
    .bind(&user_id)
    .bind(&request.email)
    .bind(&request.name)
    .bind(&password_hash)
    .bind(&roles_json)
    .bind(true)
    .bind(false) // Email not verified yet
    .bind(&verification_token)
    .execute(&pool)
    .await
    .map_err(|e| ApiError::InternalServerError(format!("Failed to create user: {}", e)))?;

    // In production, send verification email here
    // send_verification_email(&request.email, &verification_token).await?;

    info!("User registered successfully: {}", request.email);

    Ok((
        StatusCode::CREATED,
        Json(RegisterResponse {
            user_id,
            email: request.email,
            name: request.name,
            message: "User registered successfully. Please check your email for verification.".to_string(),
        }),
    ))
}

/// Verify user email with verification token
pub async fn verify_email(
    State(state): State<Arc<AppState>>,
    Path(token): Path<String>,
) -> Result<impl IntoResponse, ApiError> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    // Find user by verification token
    let user_row = sqlx::query(
        "SELECT user_id, email FROM users WHERE verification_token = ? AND is_verified = 0"
    )
    .bind(&token)
    .fetch_optional(&pool)
    .await
    .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;

    if let Some(row) = user_row {
        let user_id: String = row.get("user_id");
        let email: String = row.get("email");

        // Update user as verified
        sqlx::query(
            "UPDATE users SET is_verified = 1, verification_token = NULL, updated_at = datetime('now') WHERE user_id = ?"
        )
        .bind(&user_id)
        .execute(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Failed to verify user: {}", e)))?;

        info!("Email verified for user: {}", email);

        Ok(Json(serde_json::json!({
            "message": "Email verified successfully",
            "user_id": user_id
        })))
    } else {
        Err(ApiError::BadRequest("Invalid or expired verification token".to_string()))
    }
}

/// Initiate password reset
pub async fn request_password_reset(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PasswordResetRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    // Check if user exists
    let user_exists = sqlx::query("SELECT user_id FROM users WHERE email = ? AND is_active = 1")
        .bind(&request.email)
        .fetch_optional(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;

    if user_exists.is_some() {
        // Generate reset token
        let reset_token = Uuid::new_v4().to_string();
        let expires_at = chrono::Utc::now() + chrono::Duration::hours(1); // 1 hour expiry

        // Store reset token
        sqlx::query(
            "UPDATE users SET password_reset_token = ?, password_reset_expires = ? WHERE email = ?"
        )
        .bind(&reset_token)
        .bind(expires_at.timestamp())
        .bind(&request.email)
        .execute(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Failed to store reset token: {}", e)))?;

        // In production, send password reset email here
        // send_password_reset_email(&request.email, &reset_token).await?;

        info!("Password reset requested for: {}", request.email);
    } else {
        // Don't reveal whether email exists - still log for security monitoring
        warn!("Password reset requested for non-existent email: {}", request.email);
    }

    // Always return success to prevent email enumeration
    Ok(Json(serde_json::json!({
        "message": "If an account with that email exists, you will receive a password reset email."
    })))
}

/// Confirm password reset with token
pub async fn confirm_password_reset(
    State(state): State<Arc<AppState>>,
    Json(request): Json<PasswordResetConfirmRequest>,
) -> Result<impl IntoResponse, ApiError> {
    use argon2::{Argon2, PasswordHasher};
    use argon2::password_hash::{rand_core::OsRng, SaltString};

    if request.new_password != request.confirm_password {
        return Err(ApiError::BadRequest("Passwords do not match".to_string()));
    }

    if request.new_password.len() < 8 {
        return Err(ApiError::BadRequest("Password must be at least 8 characters long".to_string()));
    }

    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    // Find user by reset token
    let user_row = sqlx::query(
        "SELECT user_id, email, password_reset_expires FROM users 
         WHERE password_reset_token = ? AND is_active = 1"
    )
    .bind(&request.token)
    .fetch_optional(&pool)
    .await
    .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;

    if let Some(row) = user_row {
        let user_id: String = row.get("user_id");
        let email: String = row.get("email");
        let expires_timestamp: i64 = row.get("password_reset_expires");

        // Check if token is expired
        let expires_at = chrono::DateTime::from_timestamp(expires_timestamp, 0)
            .ok_or_else(|| ApiError::InternalServerError("Invalid timestamp".to_string()))?;
        
        if chrono::Utc::now() > expires_at {
            return Err(ApiError::BadRequest("Password reset token has expired".to_string()));
        }

        // Hash new password
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let password_hash = argon2
            .hash_password(request.new_password.as_bytes(), &salt)
            .map_err(|e| ApiError::InternalServerError(format!("Password hashing failed: {}", e)))?
            .to_string();

        // Update password and clear reset token
        sqlx::query(
            "UPDATE users SET password_hash = ?, password_reset_token = NULL, password_reset_expires = NULL, updated_at = datetime('now') 
             WHERE user_id = ?"
        )
        .bind(&password_hash)
        .bind(&user_id)
        .execute(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Failed to update password: {}", e)))?;

        info!("Password reset completed for user: {}", email);

        Ok(Json(serde_json::json!({
            "message": "Password reset successfully"
        })))
    } else {
        Err(ApiError::BadRequest("Invalid or expired reset token".to_string()))
    }
}

/// Update user profile (requires authentication)
pub async fn update_profile(
    State(state): State<Arc<AppState>>,
    user: AuthUser,
    Json(request): Json<UpdateProfileRequest>,
) -> Result<impl IntoResponse, ApiError> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    let mut updates = Vec::new();
    let mut values: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = Vec::new();

    if let Some(name) = &request.name {
        if !name.trim().is_empty() {
            updates.push("name = ?");
            values.push(Box::new(name.clone()));
        }
    }

    if let Some(email) = &request.email {
        if is_valid_email(email) {
            // Check if email is already taken
            let existing = sqlx::query("SELECT user_id FROM users WHERE email = ? AND user_id != ?")
                .bind(email)
                .bind(&user.user_id)
                .fetch_optional(&pool)
                .await
                .map_err(|e| ApiError::InternalServerError(format!("Database query failed: {}", e)))?;

            if existing.is_some() {
                return Err(ApiError::Conflict("Email already in use".to_string()));
            }

            updates.push("email = ?, is_verified = 0"); // Reset verification on email change
            values.push(Box::new(email.clone()));
        } else {
            return Err(ApiError::BadRequest("Invalid email format".to_string()));
        }
    }

    if updates.is_empty() {
        return Err(ApiError::BadRequest("No valid updates provided".to_string()));
    }

    updates.push("updated_at = datetime('now')");
    let query = format!("UPDATE users SET {} WHERE user_id = ?", updates.join(", "));
    
    let mut query_builder = sqlx::query(&query);
    for value in values {
        // This is a simplified approach - in production, you'd want better type handling
        if let Ok(string_val) = value.downcast::<String>() {
            query_builder = query_builder.bind(*string_val);
        }
    }
    query_builder = query_builder.bind(&user.user_id);

    query_builder
        .execute(&pool)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Failed to update profile: {}", e)))?;

    info!("Profile updated for user: {}", user.user_id);

    Ok(Json(serde_json::json!({
        "message": "Profile updated successfully"
    })))
}

/// Deactivate user account (requires authentication)
pub async fn deactivate_account(
    State(state): State<Arc<AppState>>,
    user: AuthUser,
) -> Result<impl IntoResponse, ApiError> {
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite::memory:".to_string());
    
    let pool = sqlx::SqlitePool::connect(&database_url)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Database connection failed: {}", e)))?;

    // Deactivate user account
    sqlx::query(
        "UPDATE users SET is_active = 0, updated_at = datetime('now') WHERE user_id = ?"
    )
    .bind(&user.user_id)
    .execute(&pool)
    .await
    .map_err(|e| ApiError::InternalServerError(format!("Failed to deactivate account: {}", e)))?;

    info!("Account deactivated for user: {}", user.user_id);

    Ok(Json(serde_json::json!({
        "message": "Account deactivated successfully"
    })))
}

/// Simple email validation
fn is_valid_email(email: &str) -> bool {
    email.contains('@') && email.contains('.') && email.len() > 5
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_email_validation() {
        assert!(is_valid_email("<EMAIL>"));
        assert!(is_valid_email("<EMAIL>"));
        assert!(!is_valid_email("invalid-email"));
        assert!(!is_valid_email("@domain.com"));
        assert!(!is_valid_email("user@"));
    }
}
