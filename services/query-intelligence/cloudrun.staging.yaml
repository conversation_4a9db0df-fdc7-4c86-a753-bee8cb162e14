apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence-staging
  labels:
    service: query-intelligence
    environment: staging
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "30"
    run.googleapis.com/timeout: "300"
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "30"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/timeout: "300"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 50
      timeoutSeconds: 300
      containers:
      - name: query-intelligence
        image: gcr.io/vibe-match-463114/query-intelligence:staging
        ports:
        - name: http1
          containerPort: 8002
        env:
        # Service Configuration
        - name: PROJECT_NAME
          value: "Query Intelligence Service"
        - name: SERVICE_NAME
          value: "query-intelligence"
        - name: PORT
          value: "8002"
        - name: ENVIRONMENT
          value: "staging"
        - name: LOG_LEVEL
          value: "DEBUG"
        
        # External Service URLs (staging)
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: redis-url
        - name: ANALYSIS_ENGINE_URL
          value: "https://analysis-engine-staging-xxxxx.a.run.app"
        - name: PATTERN_MINING_URL
          value: "https://pattern-mining-staging-xxxxx.a.run.app"
        - name: COLLABORATION_URL
          value: "https://collaboration-staging-xxxxx.a.run.app"
        
        # Google GenAI SDK Configuration
        - name: USE_VERTEX_AI
          value: "false"
        - name: GEMINI_MODEL_NAME
          value: "gemini-2.5-flash"
        - name: EMBEDDING_MODEL_NAME
          value: "sentence-transformers/all-mpnet-base-v2"
        
        # Model Routing Strategy
        - name: USE_MODEL_ROUTING
          value: "true"
        - name: SIMPLE_QUERY_MODEL
          value: "gemini-2.5-flash-lite"
        - name: COMPLEX_QUERY_MODEL
          value: "gemini-2.5-pro"
        
        # GCP Configuration
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_REGION
          value: "us-central1"
        - name: SERVICE_ACCOUNT_PATH
          value: "/var/secrets/google/key.json"
        
        # Gemini API Configuration
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: google-api-key
        
        # Pinecone Configuration
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: pinecone-api-key
        - name: PINECONE_INDEX_NAME
          value: "ccl-code-embeddings-staging"
        - name: PINECONE_CLOUD
          value: "aws"
        - name: PINECONE_REGION
          value: "us-west-2"
        
        # Cache Configuration (reduced for staging)
        - name: CACHE_TTL_HOURS
          value: "12"
        - name: CACHE_MAX_SIZE
          value: "10000"
        - name: SEMANTIC_CACHE_ENABLED
          value: "true"
        
        # Performance Configuration (reduced for staging)
        - name: MAX_QUERY_LENGTH
          value: "25000"
        - name: MAX_CODE_CHUNKS
          value: "100"
        - name: MAX_CONTEXT_TOKENS
          value: "50000"
        - name: MAX_OUTPUT_TOKENS
          value: "4096"
        - name: RESPONSE_TIMEOUT
          value: "120"
        
        # Rate Limiting (relaxed for staging)
        - name: RATE_LIMIT_PER_MINUTE
          value: "200"
        - name: RATE_LIMIT_BURST
          value: "50"
        - name: MAX_CONCURRENT_REQUESTS
          value: "30"
        
        # Circuit Breaker Configuration
        - name: CIRCUIT_BREAKER_THRESHOLD
          value: "5"
        - name: CIRCUIT_BREAKER_TIMEOUT
          value: "30"
        - name: CIRCUIT_BREAKER_RECOVERY_TIMEOUT
          value: "120"
        
        # Streaming Configuration
        - name: ENABLE_STREAMING
          value: "true"
        - name: STREAM_CHUNK_SIZE
          value: "512"
        - name: STREAM_DELAY_MS
          value: "100"
        
        # Monitoring Configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: METRICS_PORT
          value: "9090"
        - name: ENABLE_TRACING
          value: "true"
        
        # Security Configuration (staging secrets)
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: api-key
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: admin-api-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: jwt-secret
        
        # CORS Configuration (allowing localhost for testing)
        - name: CORS_ORIGINS
          value: "https://staging.episteme.app,https://localhost:3000,https://localhost:8080"
        
        # Feature Flags (more permissive for staging)
        - name: ENABLE_EXPERIMENTAL_FEATURES
          value: "true"
        - name: ENABLE_DEBUG_LOGGING
          value: "true"
        - name: ENABLE_QUERY_OPTIMIZATION
          value: "true"
        - name: ENABLE_SEMANTIC_SEARCH
          value: "true"
        
        # WebSocket Configuration
        - name: WEBSOCKET_ENABLED
          value: "true"
        - name: WEBSOCKET_MAX_CONNECTIONS
          value: "1000"
        - name: WEBSOCKET_HEARTBEAT_INTERVAL
          value: "30"
        
        # Database Configuration (if needed)
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: query-intelligence-staging-secrets
              key: database-url
        - name: DATABASE_POOL_SIZE
          value: "10"
        - name: DATABASE_MAX_OVERFLOW
          value: "20"
        
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
        
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        
        # Health probes (faster for staging)
        startupProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 6
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      
      volumes:
      - name: google-cloud-key
        secret:
          secretName: query-intelligence-staging-gcp-key