"""
Configuration settings for Query Intelligence Service
Updated July 2025 for Google GenAI SDK migration
"""

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from functools import lru_cache
from typing import Optional
import os


class Settings(BaseSettings):
    # Service Configuration
    PROJECT_NAME: str = "Query Intelligence Service"
    SERVICE_NAME: str = "query-intelligence"
    PORT: int = Field(default=int(os.getenv("PORT", "8002")), ge=1, le=65535)
    ENVIRONMENT: str = Field(
        default="development",
        description="Environment (development/staging/production)",
    )
    LOG_LEVEL: str = "INFO"

    # External Service URLs
    REDIS_URL: str = Field(
        default="",
        description="Redis connection URL - REQUIRED for production",
    )
    ANALYSIS_ENGINE_URL: str = Field(
        default="",
        description="Analysis Engine service URL - REQUIRED for production",
    )
    PATTERN_MINING_URL: str = Field(
        default="",
        description="Pattern Mining service URL - REQUIRED for production",
    )
    COLLABORATION_URL: str = Field(
        default="",
        description="Collaboration service URL - REQUIRED for production",
    )

    # ===== CRITICAL: Google GenAI SDK Configuration (July 2025) =====
    # The Vertex AI SDK was deprecated on June 24, 2025
    USE_VERTEX_AI: bool = Field(
        default=False,
        description="Deprecated. Vertex AI backend removed. Always False.",
    )

    # Model Configuration - Updated for Gemini 2.5 models
    GEMINI_MODEL_NAME: str = Field(
        default="gemini-2.5-flash",
        description="Primary model - use gemini-2.5-flash, gemini-2.5-flash-lite, or gemini-2.5-pro",
    )
    EMBEDDING_MODEL_NAME: str = Field(
        default="sentence-transformers/all-mpnet-base-v2",
        description="Model for generating embeddings",
    )

    # Model Routing Strategy
    USE_MODEL_ROUTING: bool = Field(
        default=True, description="Enable intelligent routing between model tiers"
    )
    SIMPLE_QUERY_MODEL: str = Field(
        default="gemini-2.5-flash-lite", description="Model for simple/fast queries"
    )
    COMPLEX_QUERY_MODEL: str = Field(
        default="gemini-2.5-pro", description="Model for complex analysis"
    )

    # GCP Configuration (for Vertex AI backend)
    GCP_PROJECT_ID: Optional[str] = Field(default=None, description="GCP project ID")
    GCP_REGION: str = Field(default="us-central1", description="GCP region")
    SERVICE_ACCOUNT_PATH: Optional[str] = Field(
        default=None, description="Path to service account JSON (for production)"
    )

    # Gemini Developer API Configuration (for development)
    GOOGLE_API_KEY: Optional[str] = Field(
        default=None, description="Gemini API key (for development only)"
    )

    # Pinecone Configuration
    PINECONE_API_KEY: Optional[str] = Field(
        default=None, description="Pinecone API key"
    )
    PINECONE_INDEX_NAME: str = Field(
        default="ccl-code-embeddings", description="Pinecone index name"
    )
    PINECONE_CLOUD: str = Field(default="aws", description="Pinecone cloud provider")
    PINECONE_REGION: str = Field(default="us-west-2", description="Pinecone region")

    # Cache Configuration
    CACHE_TTL_HOURS: int = Field(default=24, description="Cache TTL in hours", ge=0)
    CACHE_MAX_SIZE: int = Field(default=10000, description="Maximum cache size", ge=0)
    SEMANTIC_CACHE_ENABLED: bool = Field(
        default=True, description="Enable semantic caching for LLM responses"
    )

    # Performance Configuration
    MAX_QUERY_LENGTH: int = Field(default=10000, description="Maximum query length", ge=0)
    MAX_CODE_CHUNKS: int = Field(
        default=20, description="Maximum code chunks to process"
    )
    MAX_RESPONSE_TOKENS: int = Field(
        default=2048, description="Maximum response tokens"
    )
    QUERY_TIMEOUT_SECONDS: int = Field(
        default=30, description="Query timeout in seconds"
    )

    # Cloud Run Optimization
    MIN_INSTANCES: int = Field(default=5, description="Minimum Cloud Run instances")
    MAX_INSTANCES: int = Field(default=200, description="Maximum Cloud Run instances")
    CONCURRENCY: int = Field(
        default=20, description="Max concurrent requests per instance"
    )
    CPU_BOOST_ENABLED: bool = Field(
        default=True, description="Enable startup CPU boost"
    )

    # Security Configuration - CRITICAL: Use Secret Manager in production!
    JWT_SECRET_KEY: str = Field(
        default="",
        description="JWT secret key - REQUIRED for production, use Secret Manager",
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_EXPIRATION_MINUTES: int = Field(
        default=60, description="JWT expiration in minutes"
    )
    API_KEY_HEADER: str = Field(default="X-API-Key", description="API key header name")

    # Secret Manager Configuration
    USE_SECRET_MANAGER: bool = Field(
        default=False, description="Use GCP Secret Manager for secrets"
    )
    SECRET_PROJECT_ID: Optional[str] = Field(
        default=None, description="Project ID for Secret Manager"
    )

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Max requests per window")
    RATE_LIMIT_WINDOW_SECONDS: int = Field(default=60, description="Rate limit window")
    RATE_LIMIT_PER_USER: bool = Field(
        default=True, description="Apply rate limiting per user instead of per IP"
    )

    # WebSocket Settings
    WEBSOCKET_MAX_MESSAGE_SIZE_BYTES: int = Field(
        default=64 * 1024, description="Maximum WebSocket message size in bytes"
    )
    WEBSOCKET_MESSAGE_RATE_LIMIT: int = Field(
        default=60, description="Max WebSocket messages per window"
    )
    WEBSOCKET_MESSAGE_WINDOW_SECONDS: int = Field(
        default=60, description="WebSocket message rate limit window"
    )
    WEBSOCKET_CONNECTION_RATE_LIMIT: int = Field(
        default=10, description="Max WebSocket connection attempts per window"
    )
    WEBSOCKET_CONNECTION_WINDOW_SECONDS: int = Field(
        default=60, description="WebSocket connection rate limit window"
    )

    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    ENABLE_ADMIN_API: bool = Field(default=True, description="Enable admin API endpoints")
    METRICS_PORT: int = Field(default=9090, description="Metrics port")
    ENABLE_TRACING: bool = Field(default=True, description="Enable distributed tracing")

    # Security Features
    ENABLE_INPUT_VALIDATION: bool = Field(
        default=True, description="Enable input validation and sanitization"
    )
    ENABLE_PROMPT_INJECTION_DETECTION: bool = Field(
        default=True, description="Detect and prevent prompt injection attacks"
    )
    ENABLE_PII_DETECTION: bool = Field(
        default=True, description="Detect and redact PII in queries"
    )

    # CORS Configuration
    CORS_ALLOWED_ORIGINS: list[str] = Field(
        default_factory=list,
        description="Allowed CORS origins - REQUIRED for production, restrict to your domains",
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(
        default=True, description="Allow credentials in CORS requests"
    )
    CORS_ALLOWED_METHODS: list[str] = Field(
        default_factory=lambda: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="Allowed HTTP methods for CORS",
    )
    CORS_ALLOWED_HEADERS: list[str] = Field(
        default_factory=lambda: ["*"], description="Allowed headers for CORS requests"
    )

    # Circuit Breaker Configuration
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = Field(
        default=5, description="Number of failures before circuit opens"
    )
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT: int = Field(
        default=60, description="Seconds to wait before attempting recovery"
    )
    CIRCUIT_BREAKER_CALL_TIMEOUT: int = Field(
        default=10, description="Seconds to wait for a call before timing out"
    )

    model_config = SettingsConfigDict(
        extra="ignore", env_file=".env", env_file_encoding="utf-8", case_sensitive=False
    )

    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"

    def validate_production_settings(self):
        """Validate critical settings for production"""
        if self.is_production():
            errors = []

            if not self.JWT_SECRET_KEY:
                errors.append("JWT_SECRET_KEY must be set in production")
            elif len(self.JWT_SECRET_KEY) < 32:
                errors.append("JWT_SECRET_KEY must be at least 32 characters long")

            if not self.USE_SECRET_MANAGER:
                errors.append("USE_SECRET_MANAGER must be enabled in production")

            # Enforce Gemini API in production
            if self.USE_VERTEX_AI:
                errors.append(
                    "USE_VERTEX_AI must be false in production; migrate to Gemini API"
                )

            # Validate required service URLs
            if not self.REDIS_URL:
                errors.append("REDIS_URL must be set in production")
            if not self.ANALYSIS_ENGINE_URL:
                errors.append("ANALYSIS_ENGINE_URL must be set in production")
            if not self.PATTERN_MINING_URL:
                errors.append("PATTERN_MINING_URL must be set in production")

            # Validate CORS configuration
            if not self.CORS_ALLOWED_ORIGINS:
                errors.append("CORS_ALLOWED_ORIGINS must be configured in production")
            elif any(origin.startswith("http://localhost") for origin in self.CORS_ALLOWED_ORIGINS):
                errors.append("CORS_ALLOWED_ORIGINS cannot contain localhost in production")

            # Validate security settings
            if not self.ENABLE_INPUT_VALIDATION:
                errors.append("ENABLE_INPUT_VALIDATION must be enabled in production")
            if not self.ENABLE_PROMPT_INJECTION_DETECTION:
                errors.append("ENABLE_PROMPT_INJECTION_DETECTION must be enabled in production")
            if not self.ENABLE_PII_DETECTION:
                errors.append("ENABLE_PII_DETECTION must be enabled in production")

            if errors:
                raise ValueError(
                    f"Production configuration errors: {'; '.join(errors)}"
                )

    def get_jwt_secret(self) -> str:
        """Get JWT secret from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from query_intelligence.services.secret_manager import get_jwt_secret

            return get_jwt_secret()
        return self.JWT_SECRET_KEY

    def get_pinecone_api_key(self) -> str:
        """Get Pinecone API key from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from query_intelligence.services.secret_manager import get_pinecone_api_key

            return get_pinecone_api_key()
        return self.PINECONE_API_KEY or ""

    def get_google_api_key(self) -> str:
        """Get Google API key from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from query_intelligence.services.secret_manager import get_google_api_key

            return get_google_api_key()
        return self.GOOGLE_API_KEY or ""


@lru_cache()
def get_settings():
    """Get cached settings instance"""
    settings = Settings()
    settings.validate_production_settings()
    return settings


# Keep backward compatibility
settings = get_settings()
