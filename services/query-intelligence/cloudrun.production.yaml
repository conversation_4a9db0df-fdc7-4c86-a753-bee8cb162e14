apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence-prod
  labels:
    service: query-intelligence
    environment: production
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-throttling: "false"
    run.googleapis.com/startup-cpu-boost: "true"
    autoscaling.knative.dev/minScale: "5"
    autoscaling.knative.dev/maxScale: "200"
    run.googleapis.com/timeout: "300"
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "5"
        autoscaling.knative.dev/maxScale: "200"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/startup-cpu-boost: "true"
        run.googleapis.com/timeout: "300"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - name: query-intelligence
        image: gcr.io/vibe-match-463114/query-intelligence:production
        ports:
        - name: http1
          containerPort: 8002
        env:
        # Service Configuration
        - name: PROJECT_NAME
          value: "Query Intelligence Service"
        - name: SERVICE_NAME
          value: "query-intelligence"
        - name: PORT
          value: "8002"
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        
        # External Service URLs
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: redis-url
        - name: ANALYSIS_ENGINE_URL
          value: "https://analysis-engine-prod-xxxxx.a.run.app"
        - name: PATTERN_MINING_URL
          value: "https://pattern-mining-prod-xxxxx.a.run.app"
        - name: COLLABORATION_URL
          value: "https://collaboration-prod-xxxxx.a.run.app"
        
        # Google GenAI SDK Configuration (July 2025)
        - name: USE_VERTEX_AI
          value: "false"  # Deprecated as of June 24, 2025
        - name: GEMINI_MODEL_NAME
          value: "gemini-2.5-flash"
        - name: EMBEDDING_MODEL_NAME
          value: "sentence-transformers/all-mpnet-base-v2"
        
        # Model Routing Strategy
        - name: USE_MODEL_ROUTING
          value: "true"
        - name: SIMPLE_QUERY_MODEL
          value: "gemini-2.5-flash-lite"
        - name: COMPLEX_QUERY_MODEL
          value: "gemini-2.5-pro"
        
        # GCP Configuration
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_REGION
          value: "us-central1"
        - name: SERVICE_ACCOUNT_PATH
          value: "/var/secrets/google/key.json"
        
        # Gemini API Configuration (Production uses service account)
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: google-api-key
        
        # Pinecone Configuration
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: pinecone-api-key
        - name: PINECONE_INDEX_NAME
          value: "ccl-code-embeddings-prod"
        - name: PINECONE_CLOUD
          value: "aws"
        - name: PINECONE_REGION
          value: "us-west-2"
        
        # Cache Configuration
        - name: CACHE_TTL_HOURS
          value: "24"
        - name: CACHE_MAX_SIZE
          value: "50000"
        - name: SEMANTIC_CACHE_ENABLED
          value: "true"
        
        # Performance Configuration
        - name: MAX_QUERY_LENGTH
          value: "50000"
        - name: MAX_CODE_CHUNKS
          value: "200"
        - name: MAX_CONTEXT_TOKENS
          value: "100000"
        - name: MAX_OUTPUT_TOKENS
          value: "8192"
        - name: RESPONSE_TIMEOUT
          value: "240"
        
        # Rate Limiting
        - name: RATE_LIMIT_PER_MINUTE
          value: "1000"
        - name: RATE_LIMIT_BURST
          value: "200"
        - name: MAX_CONCURRENT_REQUESTS
          value: "100"
        
        # Circuit Breaker Configuration
        - name: CIRCUIT_BREAKER_THRESHOLD
          value: "10"
        - name: CIRCUIT_BREAKER_TIMEOUT
          value: "60"
        - name: CIRCUIT_BREAKER_RECOVERY_TIMEOUT
          value: "300"
        
        # Streaming Configuration
        - name: ENABLE_STREAMING
          value: "true"
        - name: STREAM_CHUNK_SIZE
          value: "1024"
        - name: STREAM_DELAY_MS
          value: "50"
        
        # Monitoring Configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: METRICS_PORT
          value: "9090"
        - name: ENABLE_TRACING
          value: "true"
        - name: JAEGER_ENDPOINT
          value: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/traces"
        
        # Security Configuration
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: api-key
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: admin-api-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: jwt-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: encryption-key
        
        # CORS Configuration
        - name: CORS_ORIGINS
          value: "https://episteme-frontend.app,https://api.episteme.app"
        
        # Feature Flags
        - name: ENABLE_EXPERIMENTAL_FEATURES
          value: "false"
        - name: ENABLE_DEBUG_LOGGING
          value: "false"
        - name: ENABLE_QUERY_OPTIMIZATION
          value: "true"
        - name: ENABLE_SEMANTIC_SEARCH
          value: "true"
        
        # WebSocket Configuration
        - name: WEBSOCKET_ENABLED
          value: "true"
        - name: WEBSOCKET_MAX_CONNECTIONS
          value: "10000"
        - name: WEBSOCKET_HEARTBEAT_INTERVAL
          value: "30"
        
        # Database Configuration (if needed)
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: query-intelligence-secrets
              key: database-url
        - name: DATABASE_POOL_SIZE
          value: "20"
        - name: DATABASE_MAX_OVERFLOW
          value: "40"
        
        resources:
          requests:
            cpu: "1000m"
            memory: "2Gi"
          limits:
            cpu: "2000m"
            memory: "4Gi"
        
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        
        # Health probes
        startupProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
          successThreshold: 1
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8002
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
      
      volumes:
      - name: google-cloud-key
        secret:
          secretName: query-intelligence-gcp-key