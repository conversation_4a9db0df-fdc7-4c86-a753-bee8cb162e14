services:
  # TypeScript/Node.js Collaboration Service (Primary)
  collaboration:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8005:8005"
    environment:
      - NODE_ENV=development
      - PORT=8005
      - LOG_LEVEL=info
      - JWT_SECRET=dev-jwt-secret-key-super-secure-for-local-dev-only-minimum-32-chars
      - JWT_EXPIRES_IN=24h
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - GOOGLE_CLOUD_PROJECT_ID=vibe-match-463114
      - GCP_PROJECT_ID=vibe-match-463114
      - FIRESTORE_DATABASE_ID=(default)
      - SPANNER_INSTANCE_ID=collaboration-instance
      - SPANNER_DATABASE_ID=collaboration-db
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
      - ENA<PERSON>E_METRICS=true
      # Integration endpoints
      - ANALYSIS_ENGINE_URL=https://analysis-engine-572735000332.us-central1.run.app
      - QUERY_INTELLIGENCE_URL=http://host.docker.internal:8002
      - PATTERN_MINING_URL=http://host.docker.internal:8003
    volumes:
      - .:/app
      - node_modules:/app/node_modules
      - ~/.gcp:/home/<USER>/.gcp:ro
    depends_on:
      - redis
    networks:
      - collaboration-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and pub/sub
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - collaboration-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  node_modules:
  redis-data:

networks:
  collaboration-network:
    driver: bridge