#!/bin/bash
set -e

# Collaboration Service Startup Script
echo "🚀 Starting Collaboration Service Stack..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Load environment variables
if [ -f .env.dev ]; then
    export $(cat .env.dev | grep -v '^#' | xargs)
    echo "✅ Loaded development environment variables"
else
    echo "⚠️  No .env.dev file found, using defaults"
fi

# Create external network if it doesn't exist
docker network create episteme_default 2>/dev/null || echo "📡 Network episteme_default already exists"

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to become healthy..."

# Function to check service health
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1

    echo "🔍 Checking $service_name health..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            echo "✅ $service_name is healthy"
            return 0
        fi
        echo "⏳ Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to become healthy"
    return 1
}

# Check all services
services_healthy=true

echo "📋 Health check results:"

# Redis
if check_service_health "Redis" "redis://localhost:6379"; then
    echo "✅ Redis: HEALTHY"
else
    echo "❌ Redis: FAILED"
    services_healthy=false
fi

# Collaboration Engine (Rust)
if check_service_health "Collaboration Engine" "http://localhost:8006/health"; then
    echo "✅ Collaboration Engine: HEALTHY"
else
    echo "❌ Collaboration Engine: FAILED"
    services_healthy=false
fi

# Collaboration Service (Node.js)
if check_service_health "Collaboration Service" "http://localhost:8005/health"; then
    echo "✅ Collaboration Service: HEALTHY"
else
    echo "❌ Collaboration Service: FAILED"
    services_healthy=false
fi

# Nginx Gateway
if check_service_health "Nginx Gateway" "http://localhost:80/health"; then
    echo "✅ Nginx Gateway: HEALTHY"
else
    echo "❌ Nginx Gateway: FAILED"
    services_healthy=false
fi

echo ""
echo "📊 Service Status Summary:"
echo "=========================="

if [ "$services_healthy" = true ]; then
    echo "🎉 All services are healthy and ready!"
    echo ""
    echo "🌐 Service Endpoints:"
    echo "   • Main Service:     http://localhost:8005"
    echo "   • WebSocket Engine: http://localhost:8006"
    echo "   • Gateway:          http://localhost:80"
    echo "   • Prometheus:       http://localhost:9090"
    echo "   • Grafana:          http://localhost:3001 (admin/admin123)"
    echo ""
    echo "📊 Metrics Available:"
    echo "   • Node.js metrics:  http://localhost:9005/metrics"
    echo "   • Rust metrics:     http://localhost:9006/metrics"
    echo ""
    echo "🧪 Test Commands:"
    echo "   curl http://localhost:8005/health    # Main service health"
    echo "   curl http://localhost:8006/health    # WebSocket engine health"
    echo "   wscat -c ws://localhost:8006/ws      # WebSocket connection test"
    echo ""
    exit 0
else
    echo "❌ Some services failed to start properly"
    echo ""
    echo "🔍 Debugging Commands:"
    echo "   docker-compose logs collaboration         # Node.js service logs"
    echo "   docker-compose logs collaboration-engine  # Rust engine logs"
    echo "   docker-compose ps                        # Service status"
    echo ""
    exit 1
fi