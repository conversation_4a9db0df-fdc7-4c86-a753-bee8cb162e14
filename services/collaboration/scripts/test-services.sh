#!/bin/bash

# Service Integration Testing Script
# Tests all collaboration service endpoints and integrations

set -e

# Configuration
SERVICE_URL=${SERVICE_URL:-"http://localhost:8005"}
TIMEOUT=${TIMEOUT:-30}
VERBOSE=${VERBOSE:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test function
test_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    log_info "Testing: $description"
    
    if [ "$VERBOSE" = true ]; then
        echo "  URL: ${SERVICE_URL}${endpoint}"
    fi
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/response.json --max-time $TIMEOUT "${SERVICE_URL}${endpoint}" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log_success "$description - Status: $response"
        if [ "$VERBOSE" = true ] && [ -f "/tmp/response.json" ]; then
            echo "  Response: $(cat /tmp/response.json | jq -r '.status // .message // "OK"' 2>/dev/null || cat /tmp/response.json)"
        fi
        return 0
    elif [ "$response" = "000" ]; then
        log_error "$description - Connection failed"
        return 1
    else
        log_warning "$description - Expected: $expected_status, Got: $response"
        if [ "$VERBOSE" = true ] && [ -f "/tmp/response.json" ]; then
            echo "  Response: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

# Main test execution
main() {
    log_info "Starting collaboration service integration tests..."
    log_info "Service URL: $SERVICE_URL"
    log_info "Timeout: ${TIMEOUT}s"
    echo

    local failed_tests=0
    local total_tests=0

    # Basic Health Checks
    log_info "=== Basic Health Checks ==="
    
    ((total_tests++))
    test_endpoint "/" 200 "Root endpoint" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/health" 200 "Basic health check" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/health/live" 200 "Liveness probe" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/health/ready" 200 "Readiness probe" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/health/detailed" 200 "Detailed health check" || ((failed_tests++))
    
    echo
    
    # Metrics
    log_info "=== Metrics Endpoint ==="
    
    ((total_tests++))
    test_endpoint "/metrics" 200 "Prometheus metrics" || ((failed_tests++))
    
    echo
    
    # Integration Tests
    log_info "=== Integration Tests ==="
    
    ((total_tests++))
    test_endpoint "/api/integration/services" 200 "External services health" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/api/integration/databases" 200 "Database connectivity" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/api/integration/websocket" 200 "WebSocket info" || ((failed_tests++))
    
    ((total_tests++))
    test_endpoint "/api/integration/analysis-engine" 200 "Analysis Engine integration" || ((failed_tests++))
    
    echo
    
    # Error Handling
    log_info "=== Error Handling ==="
    
    ((total_tests++))
    test_endpoint "/nonexistent" 404 "404 handling" || ((failed_tests++))
    
    echo
    
    # Results Summary
    log_info "=== Test Results ==="
    echo "Total tests: $total_tests"
    echo "Passed: $((total_tests - failed_tests))"
    echo "Failed: $failed_tests"
    
    if [ $failed_tests -eq 0 ]; then
        log_success "All tests passed! 🎉"
        exit 0
    else
        log_error "$failed_tests tests failed"
        exit 1
    fi
}

# Show usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -u, --url URL         Service URL (default: http://localhost:8005)"
    echo "  -t, --timeout SECONDS Timeout for requests (default: 30)"
    echo "  -v, --verbose         Verbose output"
    echo "  -h, --help           Show this help message"
    echo
    echo "Environment variables:"
    echo "  SERVICE_URL          Service URL"
    echo "  TIMEOUT              Request timeout in seconds"
    echo "  VERBOSE              Set to 'true' for verbose output"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            SERVICE_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check dependencies
if ! command -v curl >/dev/null 2>&1; then
    log_error "curl is required but not installed"
    exit 1
fi

if ! command -v jq >/dev/null 2>&1; then
    log_warning "jq is not installed - JSON output will be raw"
fi

# Run main function
main