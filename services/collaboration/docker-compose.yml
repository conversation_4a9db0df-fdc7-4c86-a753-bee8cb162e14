version: '3.8'

services:
  # TypeScript/Node.js Collaboration Service (Primary)
  collaboration:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8005:8005"
      - "9005:9005"  # Metrics port
    environment:
      - NODE_ENV=development
      - PORT=8005
      - LOG_LEVEL=info
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-at-least-32-characters-long}
      - JWT_EXPIRES_IN=24h
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - GOOGLE_CLOUD_PROJECT_ID=vibe-match-463114
      - GCP_PROJECT_ID=vibe-match-463114
      - FIRESTORE_DATABASE_ID=(default)
      - SPANNER_INSTANCE_ID=${SPANNER_INSTANCE_ID:-episteme-instance}
      - SPANNER_DATABASE_ID=${SPANNER_DATABASE_ID:-episteme}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
      - MAX_CONNECTIONS_PER_SESSION=50
      - SESSION_TIMEOUT_MS=3600000
      - ENABLE_CLUSTERING=false
      - ENABLE_METRICS=true
      - METRICS_PORT=9005
      # Integration endpoints
      - ANALYSIS_ENGINE_URL=https://analysis-engine-572735000332.us-central1.run.app
      - QUERY_INTELLIGENCE_URL=http://host.docker.internal:8002
      - PATTERN_MINING_URL=http://host.docker.internal:8003
      - COLLABORATION_ENGINE_URL=http://collaboration-engine:8006
    volumes:
      - .:/app
      - node_modules:/app/node_modules
      - ~/.gcp:/home/<USER>/.gcp:ro  # GCP credentials
    depends_on:
      - redis
      - collaboration-engine
    networks:
      - collaboration-network
      - episteme-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Rust WebSocket Engine (High-Performance)
  collaboration-engine:
    build:
      context: ../collaboration-engine
      dockerfile: Dockerfile
    ports:
      - "8006:8003"  # Map to 8006 externally, 8003 internally
      - "9006:9003"  # Metrics port
    environment:
      - PORT=8003
      - METRICS_PORT=9003
      - ENVIRONMENT=development
      - RUST_LOG=collaboration_engine=info,tower_http=info
      - RUST_BACKTRACE=1
      - TOKIO_WORKER_THREADS=4
      # JWT Configuration
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-at-least-32-characters-long}
      - JWT_ISSUER=episteme
      - JWT_AUDIENCE=episteme-platform
      - JWT_EXPIRY_HOURS=24
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_POOL_SIZE=10
      - REDIS_CONNECTION_TIMEOUT_MS=5000
      # Spanner Configuration
      - GCP_PROJECT_ID=vibe-match-463114
      - GOOGLE_CLOUD_PROJECT_ID=vibe-match-463114
      - SPANNER_INSTANCE_ID=${SPANNER_INSTANCE_ID:-episteme-instance}
      - SPANNER_DATABASE_ID=${SPANNER_DATABASE_ID:-episteme}
      - SPANNER_POOL_MIN=1
      - SPANNER_POOL_MAX=10
      # WebSocket Configuration
      - WS_HEARTBEAT_INTERVAL_SECS=30
      - WS_CLIENT_TIMEOUT_SECS=60
      - WS_MAX_CONNECTIONS_PER_USER=5
      - WS_MAX_MESSAGE_SIZE=65536
      # Rate Limiting
      - RATE_LIMIT_REQUESTS_PER_MINUTE=100
      - RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE=60
      # Session Configuration
      - MAX_SESSION_PARTICIPANTS=50
      - SESSION_IDLE_TIMEOUT_MINUTES=30
      - MAX_MESSAGE_HISTORY=100
      # Feature Flags
      - ENABLE_MESSAGE_PERSISTENCE=true
      - ENABLE_PRESENCE_TRACKING=true
      - ENABLE_ANALYTICS=true
      # Integration Configuration
      - ANALYSIS_ENGINE_URL=https://analysis-engine-572735000332.us-central1.run.app
      - QUERY_INTELLIGENCE_URL=http://host.docker.internal:8002
      - PUBSUB_SUBSCRIPTION=collaboration-events
      - PUBSUB_MAX_CONCURRENT_MESSAGES=100
      - SERVICE_TIMEOUT_SECS=30
      - INTEGRATION_CACHE_TTL_SECS=300
    volumes:
      - ~/.gcp:/home/<USER>/.gcp:ro  # GCP credentials
    depends_on:
      - redis
    networks:
      - collaboration-network
      - episteme-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and pub/sub
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - collaboration-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development database (Firestore emulator)
  firestore-emulator:
    image: mtlynch/firestore-emulator
    ports:
      - "8080:8080"
    environment:
      - FIRESTORE_PROJECT_ID=vibe-match-463114
      - PORT=8080
    networks:
      - collaboration-network
    restart: unless-stopped

  # Spanner emulator for development
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator
    ports:
      - "9010:9010"
      - "9020:9020"
    environment:
      - SPANNER_EMULATOR_HOST=0.0.0.0:9010
    networks:
      - collaboration-network
    restart: unless-stopped

  # Nginx reverse proxy for routing
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - collaboration
      - collaboration-engine
    networks:
      - collaboration-network
    restart: unless-stopped

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - collaboration-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - collaboration-network
    restart: unless-stopped

volumes:
  node_modules:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  collaboration-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  episteme-network:
    external: true
    name: episteme_default