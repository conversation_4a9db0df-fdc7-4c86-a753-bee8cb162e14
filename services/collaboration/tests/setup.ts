/**
 * Global test setup
 */

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Mock environment variables for tests
process.env.JWT_SECRET = 'test-jwt-secret-key-that-is-long-enough-for-testing-purposes';
process.env.GITHUB_CLIENT_ID = 'test-github-client-id';
process.env.GITHUB_CLIENT_SECRET = 'test-github-client-secret';
process.env.GITHUB_REDIRECT_URI = 'http://localhost:3000/auth/callback';
process.env.GOOGLE_CLOUD_PROJECT_ID = 'test-project';
process.env.FIRESTORE_DATABASE_ID = '(default)';
process.env.SPANNER_INSTANCE_ID = 'test-instance';
process.env.SPANNER_DATABASE_ID = 'test-db';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

// Increase test timeout
jest.setTimeout(30000);