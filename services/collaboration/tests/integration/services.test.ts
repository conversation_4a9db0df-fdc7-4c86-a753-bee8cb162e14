/**
 * Service Integration Tests
 * 
 * Tests for integration with external platform services.
 */

import request from 'supertest';
import { app } from '../../src/app';
import axios from 'axios';

// Mock axios for controlled testing
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Service Integration Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/integration/services', () => {
    it('should test all external services', async () => {
      // Mock successful responses
      mockedAxios.get.mockImplementation((url) => {
        if (url.includes('analysis-engine')) {
          return Promise.resolve({
            status: 200,
            data: { status: 'healthy', version: '1.0.0' }
          });
        }
        if (url.includes('query-intelligence')) {
          return Promise.resolve({
            status: 200,
            data: { status: 'healthy', version: '1.0.0' }
          });
        }
        if (url.includes('pattern-mining')) {
          return Promise.resolve({
            status: 200,
            data: { status: 'healthy', version: '1.0.0' }
          });
        }
        return Promise.reject(new Error('Service not found'));
      });

      const response = await request(app)
        .get('/api/integration/services')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        totalResponseTime: expect.any(Number),
        timestamp: expect.any(String),
        services: expect.arrayContaining([
          expect.objectContaining({
            service: 'analysis-engine',
            healthy: true,
            responseTime: expect.any(Number),
          }),
          expect.objectContaining({
            service: 'query-intelligence',
            healthy: true,
            responseTime: expect.any(Number),
          }),
          expect.objectContaining({
            service: 'pattern-mining',
            healthy: true,
            responseTime: expect.any(Number),
          }),
        ]),
        summary: {
          total: 3,
          healthy: 3,
          unhealthy: 0,
        },
      });
    });

    it('should handle service failures gracefully', async () => {
      // Mock failed responses
      mockedAxios.get.mockRejectedValue(new Error('Connection refused'));

      const response = await request(app)
        .get('/api/integration/services')
        .expect(503);

      expect(response.body.status).toBe('degraded');
      expect(response.body.summary.unhealthy).toBe(3);
      
      response.body.services.forEach((service: any) => {
        expect(service.healthy).toBe(false);
        expect(service.error).toBeDefined();
      });
    });
  });

  describe('GET /api/integration/analysis-engine', () => {
    it('should test analysis engine integration', async () => {
      mockedAxios.get.mockResolvedValue({
        status: 200,
        data: { status: 'healthy', version: '1.0.0' }
      });
      
      mockedAxios.post.mockResolvedValue({
        status: 200,
        data: { analysis: 'sample result' }
      });

      const response = await request(app)
        .get('/api/integration/analysis-engine')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        responseTime: expect.any(Number),
        service: 'analysis-engine',
        health: expect.any(Object),
        sampleAnalysis: {
          successful: true,
          results: expect.any(Object),
        },
      });
    });

    it('should handle analysis engine failure', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Service unavailable'));

      const response = await request(app)
        .get('/api/integration/analysis-engine')
        .expect(503);

      expect(response.body.status).toBe('unhealthy');
      expect(response.body.error).toBeDefined();
    });
  });

  describe('GET /api/integration/websocket', () => {
    it('should provide websocket connectivity information', async () => {
      const response = await request(app)
        .get('/api/integration/websocket')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'info',
        websocket: {
          url: expect.stringMatching(/^ws:\/\/localhost:\d+$/),
          port: expect.any(Number),
          message: expect.any(String),
          endpoints: expect.any(Array),
        },
      });
    });
  });

  describe('GET /api/integration/databases', () => {
    it('should test database connectivity', async () => {
      const response = await request(app)
        .get('/api/integration/databases')
        .expect((res) => {
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toMatchObject({
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
        responseTime: expect.any(Number),
        databases: {
          redis: {
            connected: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
          firestore: {
            connected: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
          spanner: {
            connected: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
        },
        summary: {
          allConnected: expect.any(Boolean),
          criticalConnected: expect.any(Boolean),
          message: expect.any(String),
        },
      });
    });
  });
});