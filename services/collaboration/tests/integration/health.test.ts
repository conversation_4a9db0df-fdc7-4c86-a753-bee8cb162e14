/**
 * Health Endpoint Integration Tests
 * 
 * Comprehensive tests for health check endpoints and service monitoring.
 */

import request from 'supertest';
import { app } from '../../src/app';
import { redisClient } from '../../src/clients/redis.client';

describe('Health Endpoints', () => {
  beforeAll(async () => {
    // Initialize Redis connection for tests
    try {
      await redisClient.connect();
    } catch (error) {
      console.warn('Redis connection failed in tests:', error);
    }
  });

  afterAll(async () => {
    // Clean up connections
    try {
      await redisClient.disconnect();
    } catch (error) {
      console.warn('Redis disconnect failed in tests:', error);
    }
  });

  describe('GET /health', () => {
    it('should return basic health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toEqual({
        status: 'healthy',
        service: 'collaboration',
        version: expect.any(String),
        timestamp: expect.any(String),
        uptime: expect.any(Number),
      });
    });

    it('should have valid timestamp format', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      const timestamp = new Date(response.body.timestamp);
      expect(timestamp.toISOString()).toBe(response.body.timestamp);
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health information', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect((res) => {
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toMatchObject({
        status: expect.stringMatching(/^(healthy|degraded|unhealthy)$/),
        service: 'collaboration',
        version: expect.any(String),
        timestamp: expect.any(String),
        responseTime: expect.any(Number),
        dependencies: {
          redis: {
            healthy: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
          firestore: {
            healthy: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
          spanner: {
            healthy: expect.any(Boolean),
            responseTime: expect.any(Number),
          },
        },
        system: {
          memory: expect.any(Object),
          cpu: expect.any(Object),
          uptime: expect.any(Number),
          pid: expect.any(Number),
          platform: expect.any(String),
          nodeVersion: expect.any(String),
        },
        environment: expect.any(String),
        gcpProject: expect.any(String),
      });
    });

    it('should have reasonable response time', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect((res) => {
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body.responseTime).toBeLessThan(5000); // 5 seconds max
    });
  });

  describe('GET /health/ready', () => {
    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')
        .expect((res) => {
          expect([200, 503]).toContain(res.status);
        });

      expect(response.body).toMatchObject({
        status: expect.stringMatching(/^(ready|not_ready)$/),
        timestamp: expect.any(String),
      });

      if (response.body.status === 'not_ready') {
        expect(response.body.dependencies).toBeDefined();
      }
    });
  });

  describe('GET /health/live', () => {
    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(response.body).toEqual({
        status: 'alive',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
      });
    });

    it('should always return 200 for liveness', async () => {
      // Liveness should always pass unless the service is completely dead
      await request(app)
        .get('/health/live')
        .expect(200);
    });
  });

  describe('Error handling', () => {
    it('should handle health check gracefully when services are unavailable', async () => {
      // This test assumes some services might be unavailable in test environment
      const response = await request(app)
        .get('/health/detailed');

      // Should not crash, should return either healthy or degraded
      expect([200, 503]).toContain(response.status);
      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);
    });
  });

  describe('Performance', () => {
    it('should respond quickly to basic health check', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/health')
        .expect(200);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should be under 1 second
    });

    it('should respond within reasonable time for detailed check', async () => {
      const start = Date.now();
      
      await request(app)
        .get('/health/detailed');
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(10000); // Should be under 10 seconds
    });
  });
});