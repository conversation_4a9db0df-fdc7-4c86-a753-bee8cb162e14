/**
 * Integration test setup
 */

import { redisClient } from '../../src/clients/redis.client';

beforeAll(async () => {
  // Give services time to start
  await new Promise(resolve => setTimeout(resolve, 2000));
});

afterAll(async () => {
  // Clean up connections
  try {
    if (redisClient.isConnected()) {
      await redisClient.disconnect();
    }
  } catch (error) {
    console.warn('Cleanup error:', error);
  }
});