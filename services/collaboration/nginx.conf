events {
    worker_connections 1024;
}

http {
    upstream collaboration_backend {
        server collaboration:8005;
    }

    upstream collaboration_engine {
        server collaboration-engine:8003;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=websocket:10m rate=5r/s;

    # WebSocket proxy configuration
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    server {
        listen 80;
        server_name localhost;

        # Enable compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Main collaboration service (TypeScript/Node.js)
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://collaboration_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # WebSocket connections (Rust engine)
        location /ws/ {
            limit_req zone=websocket burst=10 nodelay;
            proxy_pass http://collaboration_engine;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;  # Allow longer WebSocket connections
        }

        # Health checks
        location /health {
            proxy_pass http://collaboration_backend/health;
            proxy_set_header Host $host;
        }

        location /engine/health {
            proxy_pass http://collaboration_engine/health;
            proxy_set_header Host $host;
        }

        # Metrics endpoints (development only)
        location /metrics {
            proxy_pass http://collaboration_backend/metrics;
            proxy_set_header Host $host;
            # Restrict access in production
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }

        location /engine/metrics {
            proxy_pass http://collaboration_engine/metrics;
            proxy_set_header Host $host;
            # Restrict access in production
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }

        # Static files fallback
        location / {
            return 200 "Collaboration Service Gateway";
            add_header Content-Type text/plain;
        }

        # Error pages
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            return 500 "Service temporarily unavailable";
            add_header Content-Type text/plain;
        }
    }
}