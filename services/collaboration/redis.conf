# Redis configuration for Collaboration Service
# Security
protected-mode yes
port 6379
bind 127.0.0.1 ::1

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Pub/Sub
notify-keyspace-events Ex

# Security settings for development
# requirepass should be set via environment in production