{"name": "@episteme/collaboration", "version": "0.1.0", "description": "Real-time collaboration service for the CCL platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "build:docker": "docker build -t collaboration-service .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:services": "./scripts/test-services.sh", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:simple": "docker-compose -f docker-compose-simple.yml up -d", "health": "curl -s http://localhost:8005/health | jq", "health:detailed": "curl -s http://localhost:8005/health/detailed | jq", "integration:test": "curl -s http://localhost:8005/api/integration/services | jq"}, "keywords": ["collaboration", "real-time", "websocket", "typescript", "ccl"], "author": "CCL Team", "license": "MIT", "dependencies": {"@google-cloud/firestore": "^7.1.0", "@google-cloud/spanner": "^7.1.0", "@types/axios": "^0.9.36", "@types/bcrypt": "^6.0.0", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "prom-client": "^15.1.0", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "winston": "^3.11.0", "y-websocket": "^1.5.0", "yjs": "^13.6.10", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.20.5", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/episteme/collaboration-service.git"}, "bugs": {"url": "https://github.com/episteme/collaboration-service/issues"}, "homepage": "https://github.com/episteme/collaboration-service#readme"}