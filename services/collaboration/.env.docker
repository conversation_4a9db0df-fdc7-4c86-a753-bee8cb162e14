# Docker Environment Configuration for Collaboration Service
# Uses existing vibe-match-463114 GCP project

# Core Configuration
NODE_ENV=development
PORT=8005
LOG_LEVEL=info

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-super-secure-for-local-dev-only-minimum-32-chars
JWT_EXPIRES_IN=24h
JWT_ISSUER=ccl-platform
JWT_AUDIENCE=episteme-platform

# Google Cloud Configuration (Existing Project)
GCP_PROJECT_ID=vibe-match-463114
GOOGLE_CLOUD_PROJECT_ID=vibe-match-463114
FIRESTORE_DATABASE_ID=(default)

# Spanner Configuration (Using existing instances)
SPANNER_INSTANCE_ID=collaboration-instance
SPANNER_DATABASE_ID=collaboration-db

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://redis:6379

# Service Integration URLs (Production Analysis Engine)
ANALYSIS_ENGINE_URL=https://analysis-engine-572735000332.us-central1.run.app
QUERY_INTELLIGENCE_URL=http://host.docker.internal:8002
PATTERN_MINING_URL=http://host.docker.internal:8003
MARKETPLACE_URL=http://host.docker.internal:8005
COLLABORATION_ENGINE_URL=http://collaboration-engine:8006

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

# Session & Connection Limits
MAX_CONNECTIONS_PER_SESSION=50
SESSION_TIMEOUT_MS=3600000
MAX_SESSION_PARTICIPANTS=50

# Feature Flags
ENABLE_CLUSTERING=false
ENABLE_METRICS=true
ENABLE_MESSAGE_PERSISTENCE=true
ENABLE_PRESENCE_TRACKING=true
ENABLE_ANALYTICS=true

# Metrics Configuration
METRICS_PORT=9005

# GitHub OAuth (Development)
GITHUB_CLIENT_ID=dev-github-client-id
GITHUB_CLIENT_SECRET=dev-github-client-secret
GITHUB_REDIRECT_URI=http://localhost:8005/auth/github/callback

# Development Settings
ENABLE_CORS=true
RUST_LOG=collaboration_engine=info,tower_http=info
RUST_BACKTRACE=1

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123