/**
 * Express Application Configuration
 * 
 * Configures the Express application with middleware, routes,
 * and error handling for the collaboration service.
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { json, urlencoded } from 'express';

// Middleware
import { requestLogger } from './middleware/logging.middleware';
import { errorHandler } from './middleware/error-handler.middleware';
import { authMiddleware } from './middleware/auth.middleware';
import { performanceMonitoring, memoryMonitoring } from './middleware/performance.middleware';

// Controllers
import { healthController } from './controllers/health.controller';
import { teamsController } from './controllers/teams.controller';
import { sessionsController } from './controllers/sessions.controller';
import { authController } from './controllers/auth.controller';
import { integrationController } from './controllers/integration.controller';

// Utils
import { logger } from './utils/logger';

// Metrics
import { register as metricsRegistry } from './monitoring/metrics';

/**
 * Create and configure Express application
 */
export const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(json({ limit: '10mb' }));
app.use(urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Performance monitoring middleware
app.use(performanceMonitoring);
app.use(memoryMonitoring);

// Health check endpoint (no auth required)
app.use('/health', healthController);

// Metrics endpoint (no auth required)
app.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', metricsRegistry.contentType);
    const metrics = await metricsRegistry.metrics();
    res.end(metrics);
  } catch (error) {
    logger.error('Error generating metrics', { error });
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to generate metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

// Authentication endpoints (public entrypoints)
app.use('/api/auth', authController);

// API routes (require authentication)
app.use('/api/teams', authMiddleware, teamsController);
app.use('/api/sessions', authMiddleware, sessionsController);

// Integration testing endpoints (no auth required for monitoring)
app.use('/api/integration', integrationController);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'collaboration',
    version: process.env.npm_package_version || '0.1.0',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

logger.info('Express application configured successfully');