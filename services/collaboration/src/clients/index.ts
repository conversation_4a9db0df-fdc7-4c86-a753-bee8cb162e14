/**
 * External Client Initialization
 * 
 * Initializes and manages connections to external services
 * including Redis, Firestore, and Spanner.
 */

import { logger } from '../utils/logger';
import { redisClient } from './redis.client';
import { firestoreClient } from './firestore.client';
import { spannerClient } from './spanner.client';

/**
 * Initialize all external clients with graceful failure handling
 */
export async function initializeClients(): Promise<void> {
  const failures: string[] = [];

  // Initialize Redis client
  try {
    logger.info('Initializing Redis client');
    await redisClient.connect();
    logger.info('Redis client connected successfully');
  } catch (error) {
    logger.error('Failed to initialize Redis client', { error });
    failures.push('Redis');
  }

  // Initialize Firestore client (graceful failure)
  try {
    logger.info('Initializing Firestore client');
    await firestoreClient.initialize();
    logger.info('Firestore client initialized successfully');
  } catch (error) {
    logger.warn('Failed to initialize Firestore client - continuing without it', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Enable Firestore API in GCP console if needed'
    });
    failures.push('Firestore');
  }

  // Initialize Spanner client (graceful failure)
  try {
    logger.info('Initializing Spanner client');
    await spannerClient.initialize();
    logger.info('Spanner client initialized successfully');
  } catch (error) {
    logger.warn('Failed to initialize Spanner client - continuing without it', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      suggestion: 'Check Spanner configuration and credentials'
    });
    failures.push('Spanner');
  }

  // Log summary
  if (failures.length === 0) {
    logger.info('All external clients initialized successfully');
  } else {
    logger.info('Client initialization completed with some failures', {
      initialized: ['Redis', 'Firestore', 'Spanner'].filter(c => !failures.includes(c)),
      failed: failures,
      status: 'Service will continue with limited functionality'
    });
  }

  // Only throw if Redis fails (critical for basic functionality)
  if (failures.includes('Redis')) {
    throw new Error('Redis client failed to initialize - service cannot start');
  }
}

/**
 * Close all external clients
 */
export async function closeClients(): Promise<void> {
  try {
    logger.info('Closing external clients');

    // Close Redis client
    if (redisClient.isConnected()) {
      await redisClient.disconnect();
      logger.info('Redis client closed');
    }

    // Close Firestore client
    await firestoreClient.close();
    logger.info('Firestore client closed');

    // Close Spanner client
    await spannerClient.close();
    logger.info('Spanner client closed');

    logger.info('All external clients closed successfully');
  } catch (error) {
    logger.error('Error closing external clients', { error });
    throw error;
  }
}

// Export individual clients
export { redisClient } from './redis.client';
export { firestoreClient } from './firestore.client';
export { spannerClient } from './spanner.client';