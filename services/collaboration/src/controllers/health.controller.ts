/**
 * Health Check Controller
 * 
 * Comprehensive health monitoring endpoint with performance metrics,
 * system status, and dependency health checks.
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/error-handler.middleware';
import { 
  getHealthMetrics, 
  performanceCollector, 
  metricsRegister 
} from '../middleware/performance.middleware';
import { redisClient } from '../clients/redis.client';
import { firestoreClient } from '../clients/firestore.client';
import { spannerClient } from '../clients/spanner.client';
import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { auditService } from '../services/audit.service';
import os from 'os';

const router = Router();

/**
 * Basic health check endpoint
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const health = {
      status: 'healthy',
      service: 'collaboration',
      version: process.env.npm_package_version || '0.1.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };

    res.status(200).json(health);
  } catch (error) {
    logger.error('Health check error', { error });
    res.status(500).json({
      status: 'unhealthy',
      error: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Detailed health check with dependencies
 */
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    
    // Check Redis
    const redisHealth = await checkRedisHealth();
    
    // Check Firestore
    const firestoreHealth = await checkFirestoreHealth();
    
    // Check Spanner
    const spannerHealth = await checkSpannerHealth();
    
    // System metrics
    const systemMetrics = {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      uptime: process.uptime(),
      pid: process.pid,
      platform: process.platform,
      nodeVersion: process.version,
    };

    const responseTime = Date.now() - startTime;
    // Service is healthy if Redis is working (required for basic functionality)
    // Firestore and Spanner failures are non-critical
    const allHealthy = redisHealth.healthy;
    const degraded = !firestoreHealth.healthy || !spannerHealth.healthy;

    const health = {
      status: !allHealthy ? 'unhealthy' : (degraded ? 'degraded' : 'healthy'),
      service: 'collaboration',
      version: process.env.npm_package_version || '0.1.0',
      timestamp: new Date().toISOString(),
      responseTime,
      dependencies: {
        redis: redisHealth,
        firestore: firestoreHealth,
        spanner: spannerHealth,
      },
      system: systemMetrics,
      environment: process.env.NODE_ENV || 'development',
      gcpProject: appConfig.gcp.projectId,
    };

    res.status(allHealthy ? 200 : 503).json(health);
  } catch (error) {
    logger.error('Detailed health check error', { error });
    res.status(500).json({
      status: 'unhealthy',
      error: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Readiness probe endpoint
 */
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Service is ready if Redis is connected (critical dependency)
    const redisReady = redisClient.isConnected();
    const firestoreReady = await checkFirestoreHealth();
    const spannerReady = await checkSpannerHealth();

    // Only Redis is required for readiness
    const isReady = redisReady;

    if (isReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        dependencies: {
          redis: redisReady,
          firestore: firestoreReady.healthy,
          spanner: spannerReady.healthy,
        },
      });
    }
  } catch (error) {
    logger.error('Readiness check error', { error });
    res.status(503).json({
      status: 'not_ready',
      error: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Liveness probe endpoint
 */
router.get('/live', async (req: Request, res: Response) => {
  try {
    // Simple liveness check
    res.status(200).json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  } catch (error) {
    logger.error('Liveness check error', { error });
    res.status(500).json({
      status: 'dead',
      error: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Check Redis health
 */
async function checkRedisHealth(): Promise<{ healthy: boolean; responseTime: number; error?: string }> {
  const startTime = Date.now();
  
  try {
    if (!redisClient.isConnected()) {
      return {
        healthy: false,
        responseTime: Date.now() - startTime,
        error: 'Redis client not ready',
      };
    }

    await redisClient.set('health_check', 'ok', 10);
    await redisClient.get('health_check');
    
    return {
      healthy: true,
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      healthy: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Check Firestore health with graceful failure
 */
async function checkFirestoreHealth(): Promise<{ healthy: boolean; responseTime: number; error?: string }> {
  const startTime = Date.now();
  
  try {
    await firestoreClient.getRawClient().collection('_healthcheck').limit(1).get();
    
    return {
      healthy: true,
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    // Graceful failure - service continues without Firestore
    logger.warn('Firestore health check failed, continuing with limited functionality', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    return {
      healthy: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Firestore API not enabled',
    };
  }
}

/**
 * Check Spanner health with graceful failure
 */
async function checkSpannerHealth(): Promise<{ healthy: boolean; responseTime: number; error?: string }> {
  const startTime = Date.now();
  
  try {
    const healthy = await spannerClient.healthCheck();
    
    return {
      healthy,
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    // Graceful failure - service continues without Spanner
    logger.warn('Spanner health check failed, continuing with limited functionality', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    return {
      healthy: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Spanner not configured',
    };
  }
}

export { router as healthController };