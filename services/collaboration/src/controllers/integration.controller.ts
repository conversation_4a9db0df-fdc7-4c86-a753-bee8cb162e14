/**
 * Integration Testing Controller
 * 
 * Provides endpoints for testing integration with other platform services
 * including analysis engine, pattern mining, and query intelligence.
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/error-handler.middleware';
import { logger } from '../utils/logger';
import axios from 'axios';

const router = Router();

interface ServiceHealth {
  service: string;
  healthy: boolean;
  responseTime: number;
  version?: string;
  error?: string;
}

/**
 * Test all external service integrations
 */
router.get('/services', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  const services = [
    {
      name: 'analysis-engine',
      url: process.env.ANALYSIS_ENGINE_URL || 'https://analysis-engine-572735000332.us-central1.run.app',
      healthPath: '/health'
    },
    {
      name: 'query-intelligence', 
      url: process.env.QUERY_INTELLIGENCE_URL || 'http://host.docker.internal:8002',
      healthPath: '/health'
    },
    {
      name: 'pattern-mining',
      url: process.env.PATTERN_MINING_URL || 'http://host.docker.internal:8003', 
      healthPath: '/health'
    }
  ];

  const results: ServiceHealth[] = [];

  // Test each service concurrently
  await Promise.all(
    services.map(async (service) => {
      const serviceStartTime = Date.now();
      
      try {
        const response = await axios.get(`${service.url}${service.healthPath}`, {
          timeout: 5000,
          headers: {
            'User-Agent': 'collaboration-service-health-check'
          }
        });
        
        results.push({
          service: service.name,
          healthy: response.status === 200,
          responseTime: Date.now() - serviceStartTime,
          version: response.data?.version,
        });
      } catch (error) {
        results.push({
          service: service.name,
          healthy: false,
          responseTime: Date.now() - serviceStartTime,
          error: error instanceof Error ? error.message : 'Connection failed',
        });
      }
    })
  );

  const totalResponseTime = Date.now() - startTime;
  const allHealthy = results.every(r => r.healthy);
  
  res.status(allHealthy ? 200 : 503).json({
    status: allHealthy ? 'healthy' : 'degraded',
    totalResponseTime,
    timestamp: new Date().toISOString(),
    services: results,
    summary: {
      total: results.length,
      healthy: results.filter(r => r.healthy).length,
      unhealthy: results.filter(r => !r.healthy).length,
    },
  });
}));

/**
 * Test Analysis Engine integration
 */
router.get('/analysis-engine', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  const analysisEngineUrl = process.env.ANALYSIS_ENGINE_URL || 'https://analysis-engine-572735000332.us-central1.run.app';
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get(`${analysisEngineUrl}/health`, {
      timeout: 5000,
    });
    
    // Test analyze endpoint with sample data
    const analyzeResponse = await axios.post(`${analysisEngineUrl}/api/analyze`, {
      code: 'console.log("Hello World");',
      language: 'javascript',
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    res.json({
      status: 'healthy',
      responseTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      service: 'analysis-engine',
      url: analysisEngineUrl,
      health: healthResponse.data,
      sampleAnalysis: {
        successful: analyzeResponse.status === 200,
        results: analyzeResponse.data,
      },
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      service: 'analysis-engine',
      url: analysisEngineUrl,
      error: error instanceof Error ? error.message : 'Connection failed',
    });
  }
}));

/**
 * Test WebSocket connectivity
 */
router.get('/websocket', asyncHandler(async (req: Request, res: Response) => {
  const wsPort = process.env.WS_PORT || 8006;
  const wsUrl = `ws://localhost:${wsPort}`;
  
  res.json({
    status: 'info',
    timestamp: new Date().toISOString(),
    websocket: {
      url: wsUrl,
      port: wsPort,
      message: 'WebSocket connectivity can be tested using the collaboration client SDK',
      endpoints: [
        'POST /api/sessions - Create collaboration session',
        'WebSocket connection to session room for real-time collaboration',
      ],
    },
  });
}));

/**
 * Test database connectivity
 */
router.get('/databases', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  // Import clients here to avoid circular dependencies
  const { redisClient } = await import('../clients/redis.client');
  const { firestoreClient } = await import('../clients/firestore.client');
  const { spannerClient } = await import('../clients/spanner.client');
  
  const results = {
    redis: {
      connected: false,
      responseTime: 0,
      error: undefined as string | undefined,
    },
    firestore: {
      connected: false,
      responseTime: 0, 
      error: undefined as string | undefined,
    },
    spanner: {
      connected: false,
      responseTime: 0,
      error: undefined as string | undefined,
    },
  };

  // Test Redis
  try {
    const redisStart = Date.now();
    await redisClient.ping();
    results.redis.connected = true;
    results.redis.responseTime = Date.now() - redisStart;
  } catch (error) {
    results.redis.error = error instanceof Error ? error.message : 'Unknown error';
  }

  // Test Firestore
  try {
    const firestoreStart = Date.now();
    await firestoreClient.getRawClient().collection('_healthcheck').limit(1).get();
    results.firestore.connected = true;
    results.firestore.responseTime = Date.now() - firestoreStart;
  } catch (error) {
    results.firestore.error = error instanceof Error ? error.message : 'Unknown error';
  }

  // Test Spanner
  try {
    const spannerStart = Date.now();
    const healthy = await spannerClient.healthCheck();
    results.spanner.connected = healthy;
    results.spanner.responseTime = Date.now() - spannerStart;
  } catch (error) {
    results.spanner.error = error instanceof Error ? error.message : 'Unknown error';
  }

  const allConnected = results.redis.connected && results.firestore.connected && results.spanner.connected;
  const criticalConnected = results.redis.connected; // Only Redis is critical

  res.status(criticalConnected ? 200 : 503).json({
    status: allConnected ? 'healthy' : (criticalConnected ? 'degraded' : 'unhealthy'),
    responseTime: Date.now() - startTime,
    timestamp: new Date().toISOString(),
    databases: results,
    summary: {
      allConnected,
      criticalConnected,
      message: 'Redis is required, Firestore and Spanner are optional',
    },
  });
}));

export { router as integrationController };