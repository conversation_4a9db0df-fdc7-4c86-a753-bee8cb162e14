global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Collaboration Service (Node.js)
  - job_name: 'collaboration'
    static_configs:
      - targets: ['collaboration:9005']
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http

  # Collaboration Engine (Rust)
  - job_name: 'collaboration-engine'
    static_configs:
      - targets: ['collaboration-engine:9003']
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

  # Self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Integration with other Episteme services
  - job_name: 'analysis-engine'
    static_configs:
      - targets: ['host.docker.internal:8001']
    scrape_interval: 30s
    metrics_path: /metrics
    scheme: http

  - job_name: 'query-intelligence'
    static_configs:
      - targets: ['host.docker.internal:8002']
    scrape_interval: 30s
    metrics_path: /metrics
    scheme: http

  - job_name: 'pattern-mining'
    static_configs:
      - targets: ['host.docker.internal:8003']
    scrape_interval: 30s
    metrics_path: /metrics
    scheme: http

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093