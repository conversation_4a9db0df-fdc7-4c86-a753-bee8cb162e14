# Development Environment for Collaboration Service
NODE_ENV=development
PORT=8005
LOG_LEVEL=info

# JWT Configuration
JWT_SECRET=development-secret-key-must-be-at-least-32-characters-long-for-security
JWT_EXPIRES_IN=24h

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Google Cloud Configuration (Development with emulators)
GOOGLE_CLOUD_PROJECT_ID=episteme-dev
FIRESTORE_DATABASE_ID=(default)
SPANNER_INSTANCE_ID=episteme-instance
SPANNER_DATABASE_ID=episteme

# Service Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080
MAX_CONNECTIONS_PER_SESSION=50
SESSION_TIMEOUT_MS=3600000

# Performance Configuration
ENABLE_CLUSTERING=false
ENABLE_METRICS=true
METRICS_PORT=9005

# Integration URLs
ANALYSIS_ENGINE_URL=http://localhost:8001
QUERY_INTELLIGENCE_URL=http://localhost:8002
PATTERN_MINING_URL=http://localhost:8003

# Docker Configuration
GCP_PROJECT_ID=episteme-dev
SPANNER_INSTANCE_ID=episteme-instance
SPANNER_DATABASE_ID=episteme
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123