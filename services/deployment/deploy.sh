#!/bin/bash

# Episteme Services Cloud Run Deployment Script
# Deploys Pattern Mining and Query Intelligence services to Google Cloud Run

set -euo pipefail

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
REGISTRY="gcr.io"

# Default values
ENVIRONMENT="production"
SERVICE=""
BUILD_ONLY=false
DEPLOY_ONLY=false
DRY_RUN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Episteme services to Google Cloud Run.

OPTIONS:
    -s, --service SERVICE       Service to deploy (pattern-mining, query-intelligence, all)
    -e, --environment ENV       Environment (staging, production) [default: production]
    -b, --build-only           Only build Docker images, don't deploy
    -d, --deploy-only          Only deploy (skip building images)
    -n, --dry-run              Show what would be done without executing
    -h, --help                 Show this help message

EXAMPLES:
    $0 --service all --environment production
    $0 --service pattern-mining --environment staging --build-only
    $0 --service query-intelligence --deploy-only
    $0 --dry-run --service all

EOF
}

# Logging functions
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--service)
                SERVICE="$2"
                shift 2
                ;;
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -b|--build-only)
                BUILD_ONLY=true
                shift
                ;;
            -d|--deploy-only)
                DEPLOY_ONLY=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$SERVICE" ]]; then
        error "Service must be specified. Use --service pattern-mining, --service query-intelligence, or --service all"
        usage
        exit 1
    fi

    if [[ "$SERVICE" != "pattern-mining" && "$SERVICE" != "query-intelligence" && "$SERVICE" != "all" ]]; then
        error "Invalid service: $SERVICE. Must be 'pattern-mining', 'query-intelligence', or 'all'"
        exit 1
    fi

    if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
        error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
        exit 1
    fi

    if [[ "$BUILD_ONLY" == true && "$DEPLOY_ONLY" == true ]]; then
        error "Cannot use both --build-only and --deploy-only"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    step "Checking prerequisites..."

    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        error "gcloud CLI not found. Please install Google Cloud SDK"
        exit 1
    fi

    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker not found. Please install Docker"
        exit 1
    fi

    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        error "No active gcloud authentication found. Please run: gcloud auth login"
        exit 1
    fi

    # Set project
    log "Setting project to $PROJECT_ID"
    if [[ "$DRY_RUN" == false ]]; then
        gcloud config set project $PROJECT_ID
    fi

    # Enable required APIs
    log "Ensuring required APIs are enabled..."
    if [[ "$DRY_RUN" == false ]]; then
        gcloud services enable \
            cloudbuild.googleapis.com \
            run.googleapis.com \
            containerregistry.googleapis.com
    fi
}

# Build Docker image
build_image() {
    local service=$1
    local env=$2
    
    step "Building $service Docker image for $env..."
    
    local service_dir="services/$service"
    local image_name="$REGISTRY/$PROJECT_ID/$service:$env"
    local dockerfile_path="$service_dir/Dockerfile"
    
    if [[ ! -d "$service_dir" ]]; then
        error "Service directory not found: $service_dir"
        return 1
    fi
    
    if [[ ! -f "$dockerfile_path" ]]; then
        error "Dockerfile not found: $dockerfile_path"
        return 1
    fi
    
    log "Building image: $image_name"
    log "Context: $service_dir"
    log "Dockerfile: $dockerfile_path"
    
    if [[ "$DRY_RUN" == true ]]; then
        debug "Would run: docker build -t $image_name $service_dir"
        debug "Would run: docker push $image_name"
        return 0
    fi
    
    # Build the image
    if ! docker build -t "$image_name" "$service_dir"; then
        error "Failed to build Docker image for $service"
        return 1
    fi
    
    # Push the image
    log "Pushing image to registry..."
    if ! docker push "$image_name"; then
        error "Failed to push Docker image for $service"
        return 1
    fi
    
    log "Successfully built and pushed $service image"
}

# Deploy service to Cloud Run
deploy_service() {
    local service=$1
    local env=$2
    
    step "Deploying $service to Cloud Run ($env)..."
    
    local config_file="services/$service/cloudrun.$env.yaml"
    
    if [[ ! -f "$config_file" ]]; then
        error "Cloud Run configuration not found: $config_file"
        return 1
    fi
    
    log "Using configuration: $config_file"
    
    if [[ "$DRY_RUN" == true ]]; then
        debug "Would run: gcloud run services replace $config_file --region=$REGION"
        return 0
    fi
    
    # Deploy the service
    if ! gcloud run services replace "$config_file" --region="$REGION"; then
        error "Failed to deploy $service to Cloud Run"
        return 1
    fi
    
    # Get service URL
    local service_url=$(gcloud run services describe "${service}-${env}" \
        --region="$REGION" \
        --format="value(status.url)" 2>/dev/null || echo "")
    
    if [[ -n "$service_url" ]]; then
        log "Service deployed successfully!"
        log "Service URL: $service_url"
        
        # Test health endpoint
        log "Testing health endpoint..."
        sleep 10  # Give service time to start
        
        if curl -f -s "$service_url/health" > /dev/null; then
            log "✅ Health check passed"
        else
            warn "⚠️  Health check failed - service may still be starting"
        fi
    else
        warn "Could not retrieve service URL"
    fi
}

# Process a single service
process_service() {
    local service=$1
    local env=$2
    
    log "Processing $service for $env environment..."
    
    # Build image (unless deploy-only)
    if [[ "$DEPLOY_ONLY" == false ]]; then
        build_image "$service" "$env"
    fi
    
    # Deploy service (unless build-only)
    if [[ "$BUILD_ONLY" == false ]]; then
        deploy_service "$service" "$env"
    fi
    
    log "Completed processing $service"
}

# Validate deployment
validate_deployment() {
    local service=$1
    local env=$2
    
    step "Validating $service deployment..."
    
    if [[ "$DRY_RUN" == true ]]; then
        debug "Would validate deployment for $service"
        return 0
    fi
    
    local service_name="${service}-${env}"
    
    # Check service status
    local status=$(gcloud run services describe "$service_name" \
        --region="$REGION" \
        --format="value(status.conditions[0].type)" 2>/dev/null || echo "")
    
    if [[ "$status" == "Ready" ]]; then
        log "✅ Service $service_name is ready"
    else
        warn "⚠️  Service $service_name status: $status"
    fi
    
    # Check traffic allocation
    local traffic=$(gcloud run services describe "$service_name" \
        --region="$REGION" \
        --format="value(status.traffic[0].percent)" 2>/dev/null || echo "")
    
    if [[ "$traffic" == "100" ]]; then
        log "✅ Traffic allocation: 100%"
    else
        warn "⚠️  Traffic allocation: $traffic%"
    fi
    
    # Show recent logs (last 10 lines)
    log "Recent logs for $service_name:"
    gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=$service_name" \
        --limit=10 \
        --format="table(timestamp,severity,textPayload)" \
        2>/dev/null || warn "Could not retrieve logs"
}

# Main function
main() {
    log "Starting Episteme Cloud Run deployment..."
    log "Project: $PROJECT_ID"
    log "Region: $REGION"
    log "Environment: $ENVIRONMENT"
    log "Service(s): $SERVICE"
    log "Dry run: $DRY_RUN"
    
    # Check prerequisites
    check_prerequisites
    
    # Process services
    if [[ "$SERVICE" == "all" ]]; then
        log "Deploying all services..."
        process_service "pattern-mining" "$ENVIRONMENT"
        process_service "query-intelligence" "$ENVIRONMENT"
        
        # Validate all deployments
        if [[ "$BUILD_ONLY" == false ]]; then
            validate_deployment "pattern-mining" "$ENVIRONMENT"
            validate_deployment "query-intelligence" "$ENVIRONMENT"
        fi
    else
        process_service "$SERVICE" "$ENVIRONMENT"
        
        # Validate deployment
        if [[ "$BUILD_ONLY" == false ]]; then
            validate_deployment "$SERVICE" "$ENVIRONMENT"
        fi
    fi
    
    log "🎉 Deployment complete!"
    
    if [[ "$DRY_RUN" == false && "$BUILD_ONLY" == false ]]; then
        log ""
        log "Service URLs:"
        
        if [[ "$SERVICE" == "all" || "$SERVICE" == "pattern-mining" ]]; then
            local pm_url=$(gcloud run services describe "pattern-mining-${ENVIRONMENT}" \
                --region="$REGION" \
                --format="value(status.url)" 2>/dev/null || echo "Not found")
            log "Pattern Mining: $pm_url"
        fi
        
        if [[ "$SERVICE" == "all" || "$SERVICE" == "query-intelligence" ]]; then
            local qi_url=$(gcloud run services describe "query-intelligence-${ENVIRONMENT}" \
                --region="$REGION" \
                --format="value(status.url)" 2>/dev/null || echo "Not found")
            log "Query Intelligence: $qi_url"
        fi
        
        log ""
        log "Next steps:"
        log "1. Update any dependent services with new URLs"
        log "2. Run integration tests"
        log "3. Monitor service health and performance"
    fi
}

# Parse arguments and run main function
parse_args "$@"
main