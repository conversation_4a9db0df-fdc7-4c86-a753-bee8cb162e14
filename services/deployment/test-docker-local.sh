#!/bin/bash

# Test services locally with <PERSON><PERSON> before Cloud Run deployment
# This ensures our containers actually work

set -e

echo "🐳 Testing Services Locally with Docker"
echo "========================================"

# Test Pattern Mining
test_service() {
    local SERVICE=$1
    local DIR=$2
    local PORT=$3
    
    echo "Testing $SERVICE..."
    cd "$DIR"
    
    # Build Docker image
    echo "Building Docker image..."
    docker build -t $SERVICE-test -f Dockerfile.production . 2>/dev/null || \
    docker build -t $SERVICE-test -f Dockerfile . 2>/dev/null || \
    docker build -t $SERVICE-test -f Dockerfile.minimal . 2>/dev/null || {
        echo "Creating emergency Dockerfile..."
        cat > Dockerfile.emergency << 'EOF'
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
ENV PORT=8080
EXPOSE 8080
CMD ["sh", "-c", "python -m uvicorn $(find . -name 'main.py' | head -1 | sed 's/\\.py$//' | sed 's/\\//./g' | sed 's/^\\.//' | sed 's/src\\./src./'):app --host 0.0.0.0 --port 8080"]
EOF
        docker build -t $SERVICE-test -f Dockerfile.emergency .
    }
    
    # Run container
    echo "Running container..."
    docker run -d --name $SERVICE-test \
        -p $PORT:8080 \
        -e PORT=8080 \
        -e ENVIRONMENT=production \
        -e REDIS_URL="" \
        -e DATABASE_URL="" \
        $SERVICE-test
    
    # Wait for startup
    echo "Waiting for service to start..."
    sleep 10
    
    # Test health endpoint
    echo "Testing health endpoint..."
    if curl -s http://localhost:$PORT/health | grep -q "healthy\|ok\|ready"; then
        echo "✅ $SERVICE is healthy!"
    else
        echo "❌ $SERVICE health check failed"
        echo "Container logs:"
        docker logs $SERVICE-test
    fi
    
    # Cleanup
    docker stop $SERVICE-test
    docker rm $SERVICE-test
    
    cd -
    echo ""
}

# Test each service
test_service "pattern-mining" "/Users/<USER>/Documents/GitHub/episteme/services/pattern-mining" 8003
test_service "query-intelligence" "/Users/<USER>/Documents/GitHub/episteme/services/query-intelligence" 8002

echo "✅ Local Docker tests complete!"