#!/bin/bash

# Production Deployment Script for Cloud Run Services
# This script deploys all microservices to Google Cloud Run

set -e

PROJECT_ID="vibe-match-463114"
REGION="us-central1"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 Starting production deployment to Cloud Run${NC}"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# Function to deploy a service
deploy_service() {
    local SERVICE_NAME=$1
    local SERVICE_DIR=$2
    local PORT=$3
    local MIN_INSTANCES=$4
    local MAX_INSTANCES=$5
    
    echo -e "${GREEN}📦 Deploying $SERVICE_NAME...${NC}"
    
    cd "$SERVICE_DIR"
    
    # Build and deploy
    gcloud run deploy $SERVICE_NAME \
        --source . \
        --port $PORT \
        --region $REGION \
        --project $PROJECT_ID \
        --platform managed \
        --allow-unauthenticated \
        --service-account "${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
        --min-instances $MIN_INSTANCES \
        --max-instances $MAX_INSTANCES \
        --cpu 2 \
        --memory 2Gi \
        --timeout 300 \
        --concurrency 100 \
        --cpu-boost \
        --set-env-vars "PORT=$PORT,ENVIRONMENT=production,GCP_PROJECT_ID=$PROJECT_ID" \
        --set-secrets "REDIS_URL=${SERVICE_NAME}-redis-url:latest" \
        --set-secrets "GEMINI_API_KEY=gemini-api-key:latest" \
        --set-secrets "ANTHROPIC_API_KEY=anthropic-api-key:latest" \
        --set-secrets "PINECONE_API_KEY=pinecone-api-key:latest" \
        --set-secrets "JWT_SECRET_KEY=${SERVICE_NAME}-jwt-secret:latest" \
        --update-labels "environment=production,service=$SERVICE_NAME,version=latest"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $SERVICE_NAME deployed successfully!${NC}"
    else
        echo -e "${RED}❌ $SERVICE_NAME deployment failed!${NC}"
        exit 1
    fi
    
    cd -
    echo ""
}

# Function to validate service health
validate_health() {
    local SERVICE_NAME=$1
    local SERVICE_URL=$2
    
    echo -e "${YELLOW}🏥 Validating $SERVICE_NAME health...${NC}"
    
    # Wait for service to be ready
    sleep 10
    
    # Check health endpoint
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "${GREEN}✅ $SERVICE_NAME is healthy!${NC}"
    else
        echo -e "${RED}⚠️  $SERVICE_NAME health check returned status $HTTP_STATUS${NC}"
    fi
    
    echo ""
}

# Main deployment sequence
echo -e "${GREEN}📋 Deployment Plan:${NC}"
echo "1. Pattern Mining Service (dependency for others)"
echo "2. Query Intelligence Service"
echo "3. Collaboration Service (orchestrator)"
echo ""

# Deploy Pattern Mining
echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
deploy_service "pattern-mining" "services/pattern-mining" "8080" "1" "10"
PATTERN_MINING_URL=$(gcloud run services describe pattern-mining --region=$REGION --format="value(status.url)")
validate_health "pattern-mining" "$PATTERN_MINING_URL"

# Deploy Query Intelligence
echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
deploy_service "query-intelligence" "services/query-intelligence" "8080" "2" "20"
QUERY_INTELLIGENCE_URL=$(gcloud run services describe query-intelligence --region=$REGION --format="value(status.url)")
validate_health "query-intelligence" "$QUERY_INTELLIGENCE_URL"

# Deploy Collaboration Service
echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
deploy_service "collaboration" "services/collaboration" "8080" "1" "10"
COLLABORATION_URL=$(gcloud run services describe collaboration --region=$REGION --format="value(status.url)")
validate_health "collaboration" "$COLLABORATION_URL"

# Summary
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo ""
echo "Service URLs:"
echo "  Pattern Mining: $PATTERN_MINING_URL"
echo "  Query Intelligence: $QUERY_INTELLIGENCE_URL"
echo "  Collaboration: $COLLABORATION_URL"
echo ""
echo "Next steps:"
echo "1. Update API keys in Secret Manager with actual values"
echo "2. Configure custom domains"
echo "3. Set up monitoring alerts"
echo "4. Run integration tests"
echo ""