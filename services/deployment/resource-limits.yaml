# Resource Limits and Monitoring Configuration for Cloud Run Services
# This file documents recommended resource limits and monitoring thresholds

# Pattern Mining Service Resource Configuration
pattern-mining:
  production:
    resources:
      requests:
        cpu: "2000m"        # 2 vCPUs
        memory: "4Gi"       # 4 GB RAM
      limits:
        cpu: "4000m"        # 4 vCPUs max
        memory: "8Gi"       # 8 GB RAM max
    
    scaling:
      minInstances: 3       # Always keep 3 instances warm
      maxInstances: 100     # Scale up to 100 instances under load
      containerConcurrency: 80  # 80 concurrent requests per instance
    
    timeouts:
      requestTimeout: 300   # 5 minutes max request time
      startupProbe:
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 6
      livenessProbe:
        initialDelaySeconds: 60
        periodSeconds: 30
        timeoutSeconds: 10
        failureThreshold: 3
      readinessProbe:
        initialDelaySeconds: 10
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 3
    
    monitoring:
      alerts:
        cpu_usage_high: 80%     # Alert when CPU > 80%
        memory_usage_high: 85%  # Alert when memory > 85%
        error_rate_high: 5%     # Alert when error rate > 5%
        response_time_high: 10s # Alert when p95 response time > 10s
        instance_count_high: 80 # Alert when instances > 80
      
      sla:
        availability: 99.9%     # 99.9% uptime target
        p50_response_time: 2s   # 50th percentile < 2s
        p95_response_time: 10s  # 95th percentile < 10s
        p99_response_time: 30s  # 99th percentile < 30s
  
  staging:
    resources:
      requests:
        cpu: "500m"         # 0.5 vCPUs
        memory: "1Gi"       # 1 GB RAM
      limits:
        cpu: "1000m"        # 1 vCPU max
        memory: "2Gi"       # 2 GB RAM max
    
    scaling:
      minInstances: 1       # Keep 1 instance warm
      maxInstances: 20      # Scale up to 20 instances
      containerConcurrency: 50  # 50 concurrent requests per instance
    
    timeouts:
      requestTimeout: 300
      startupProbe:
        initialDelaySeconds: 15
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 6
      livenessProbe:
        initialDelaySeconds: 30
        periodSeconds: 15
        timeoutSeconds: 5
        failureThreshold: 3
      readinessProbe:
        initialDelaySeconds: 5
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 3

# Query Intelligence Service Resource Configuration
query-intelligence:
  production:
    resources:
      requests:
        cpu: "1000m"        # 1 vCPU
        memory: "2Gi"       # 2 GB RAM
      limits:
        cpu: "2000m"        # 2 vCPUs max
        memory: "4Gi"       # 4 GB RAM max
    
    scaling:
      minInstances: 5       # Keep 5 instances warm (high availability)
      maxInstances: 200     # Scale up to 200 instances
      containerConcurrency: 100  # 100 concurrent requests per instance
    
    timeouts:
      requestTimeout: 300   # 5 minutes max request time
      startupProbe:
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 6
      livenessProbe:
        initialDelaySeconds: 60
        periodSeconds: 30
        timeoutSeconds: 10
        failureThreshold: 3
      readinessProbe:
        initialDelaySeconds: 10
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 3
    
    monitoring:
      alerts:
        cpu_usage_high: 75%     # Alert when CPU > 75%
        memory_usage_high: 80%  # Alert when memory > 80%
        error_rate_high: 3%     # Alert when error rate > 3%
        response_time_high: 5s  # Alert when p95 response time > 5s
        instance_count_high: 150 # Alert when instances > 150
        gemini_api_errors: 10   # Alert when Gemini API errors > 10/min
      
      sla:
        availability: 99.95%    # 99.95% uptime target
        p50_response_time: 1s   # 50th percentile < 1s
        p95_response_time: 5s   # 95th percentile < 5s
        p99_response_time: 15s  # 99th percentile < 15s
  
  staging:
    resources:
      requests:
        cpu: "500m"         # 0.5 vCPUs
        memory: "1Gi"       # 1 GB RAM
      limits:
        cpu: "1000m"        # 1 vCPU max
        memory: "2Gi"       # 2 GB RAM max
    
    scaling:
      minInstances: 1       # Keep 1 instance warm
      maxInstances: 30      # Scale up to 30 instances
      containerConcurrency: 50  # 50 concurrent requests per instance
    
    timeouts:
      requestTimeout: 300
      startupProbe:
        initialDelaySeconds: 15
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 6
      livenessProbe:
        initialDelaySeconds: 30
        periodSeconds: 15
        timeoutSeconds: 5
        failureThreshold: 3
      readinessProbe:
        initialDelaySeconds: 5
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 3

# Shared Infrastructure Requirements
infrastructure:
  databases:
    postgresql:
      production:
        instance_type: "db-custom-4-16384"  # 4 vCPUs, 16GB RAM
        storage: "500GB"
        backup_retention: "30 days"
        connections: 100
        connection_timeout: "30s"
      staging:
        instance_type: "db-custom-2-8192"   # 2 vCPUs, 8GB RAM
        storage: "100GB"
        backup_retention: "7 days"
        connections: 50
        connection_timeout: "30s"
  
  redis:
    production:
      memory_size: "16GB"
      tier: "STANDARD_HA"     # High availability
      version: "7.0"
      connections: 1000
      maxmemory_policy: "allkeys-lru"
    staging:
      memory_size: "4GB"
      tier: "BASIC"           # Single instance
      version: "7.0"
      connections: 200
      maxmemory_policy: "allkeys-lru"

# Cost Optimization Settings
cost_optimization:
  pattern-mining:
    production:
      # Use CPU allocation only when processing
      cpu_allocation: "allocated_during_request"
      # Reduce minimum instances during off-peak hours (11 PM - 6 AM PST)
      scheduled_scaling:
        off_peak_min_instances: 1
        peak_min_instances: 3
    staging:
      # Scale to zero when not in use
      min_instances: 0
      cpu_allocation: "allocated_during_request"
  
  query-intelligence:
    production:
      cpu_allocation: "allocated_during_request"
      # Reduce minimum instances during off-peak hours
      scheduled_scaling:
        off_peak_min_instances: 2
        peak_min_instances: 5
    staging:
      # Scale to zero when not in use
      min_instances: 0
      cpu_allocation: "allocated_during_request"

# Security and Compliance Settings
security:
  network:
    vpc_connector: true     # Use VPC connector for private communication
    egress: "private-ranges-only"  # Only allow private IP egress
    ingress: "all"          # Allow public ingress (will be filtered by IAP/LB)
  
  service_accounts:
    pattern-mining:
      production: "<EMAIL>"
      staging: "<EMAIL>"
    query-intelligence:
      production: "<EMAIL>"
      staging: "<EMAIL>"
  
  secrets:
    # All secrets stored in Google Secret Manager
    # Secrets are mounted as files or environment variables
    # Automatic rotation enabled for critical secrets
    rotation_policy:
      api_keys: "90 days"
      database_passwords: "30 days"
      encryption_keys: "180 days"

# Monitoring and Alerting Configuration
monitoring:
  metrics:
    # Cloud Monitoring metrics
    custom_metrics:
      - "custom.googleapis.com/episteme/pattern_mining/patterns_generated"
      - "custom.googleapis.com/episteme/pattern_mining/gemini_api_calls"
      - "custom.googleapis.com/episteme/query_intelligence/queries_processed"
      - "custom.googleapis.com/episteme/query_intelligence/semantic_cache_hits"
    
    # Prometheus metrics (exposed on port 9090)
    prometheus_metrics:
      - "http_requests_total"
      - "http_request_duration_seconds"
      - "active_connections"
      - "database_connections_active"
      - "redis_connections_active"
      - "gemini_api_requests_total"
      - "gemini_api_request_duration_seconds"
  
  logging:
    # Structured JSON logging
    log_levels:
      production: "INFO"
      staging: "DEBUG"
    
    # Log retention
    retention:
      production: "30 days"
      staging: "7 days"
    
    # Log sampling for high-volume services
    sampling:
      production: 0.1       # Sample 10% of logs
      staging: 1.0          # Keep all logs
  
  tracing:
    # Jaeger distributed tracing
    sampling_rate:
      production: 0.01      # Sample 1% of traces
      staging: 0.1          # Sample 10% of traces
    
    # Trace retention
    retention: "7 days"

# Performance Benchmarks and Targets
performance_targets:
  pattern-mining:
    throughput: 
      production: "1000 requests/minute"
      staging: "200 requests/minute"
    
    latency:
      p50: "2s"
      p95: "10s"
      p99: "30s"
    
    resource_efficiency:
      cpu_utilization_target: "60-70%"
      memory_utilization_target: "70-80%"
  
  query-intelligence:
    throughput:
      production: "5000 requests/minute"
      staging: "500 requests/minute"
    
    latency:
      p50: "500ms"
      p95: "2s"
      p99: "10s"
    
    resource_efficiency:
      cpu_utilization_target: "50-60%"
      memory_utilization_target: "60-70%"

# Disaster Recovery Configuration
disaster_recovery:
  backup_strategy:
    databases:
      frequency: "4 hours"
      retention: "30 days"
      cross_region: true
    
    secrets:
      frequency: "daily"
      retention: "90 days"
      cross_region: true
  
  recovery_targets:
    rto: "30 minutes"       # Recovery Time Objective
    rpo: "4 hours"          # Recovery Point Objective
  
  failover:
    # Multi-region deployment for critical services
    regions:
      primary: "us-central1"
      secondary: "us-east1"
    
    traffic_split:
      normal: "100% primary, 0% secondary"
      failover: "0% primary, 100% secondary"