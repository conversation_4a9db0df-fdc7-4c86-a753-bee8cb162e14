# Cloud Run Deployment Configuration

This directory contains Cloud Run deployment configurations for the Episteme services.

## Services Overview

### Pattern Mining Service
- **Port**: 8003
- **Production**: Highly available with auto-scaling (3-100 instances)
- **Staging**: Development-friendly with debug logging (1-20 instances)

### Query Intelligence Service  
- **Port**: 8002
- **Production**: High-performance with intelligent model routing (5-200 instances)
- **Staging**: Feature testing environment with experimental features enabled (1-30 instances)

## Resource Allocation

### Production Resources
| Service | CPU Request | CPU Limit | Memory Request | Memory Limit | Container Concurrency |
|---------|-------------|-----------|----------------|--------------|----------------------|
| Pattern Mining | 2000m | 4000m | 4Gi | 8Gi | 80 |
| Query Intelligence | 1000m | 2000m | 2Gi | 4Gi | 100 |

### Staging Resources
| Service | CPU Request | CPU Limit | Memory Request | Memory Limit | Container Concurrency |
|---------|-------------|-----------|----------------|--------------|----------------------|
| Pattern Mining | 500m | 1000m | 1Gi | 2Gi | 50 |
| Query Intelligence | 500m | 1000m | 1Gi | 2Gi | 50 |

## Environment Variables

### Common Environment Variables

#### Application Settings
- `ENVIRONMENT`: Environment (development/staging/production)
- `PORT`: Service port
- `LOG_LEVEL`: Logging level (DEBUG/INFO/WARNING/ERROR)
- `LOG_FORMAT`: Log format (json/text)
- `DEBUG`: Debug mode flag
- `WORKERS`: Number of worker processes

#### Security Settings
- `SECRET_KEY`: Application secret key
- `JWT_SECRET`: JWT signing secret
- `SESSION_SECRET`: Session encryption secret  
- `ENCRYPTION_KEY`: Data encryption key
- `API_KEY`: Service API key
- `ADMIN_API_KEY`: Admin API key

#### Database Settings
- `DATABASE_URL`: PostgreSQL connection string
- `POSTGRES_PASSWORD`: PostgreSQL password
- `DATABASE_POOL_SIZE`: Connection pool size
- `DATABASE_MAX_OVERFLOW`: Max overflow connections
- `DATABASE_ECHO`: SQL query logging

#### Redis Settings
- `REDIS_URL`: Redis connection string
- `REDIS_PASSWORD`: Redis password
- `REDIS_SSL`: SSL/TLS encryption
- `REDIS_MAX_CONNECTIONS`: Max connection pool size
- `REDIS_CONNECTION_TIMEOUT`: Connection timeout
- `REDIS_SOCKET_TIMEOUT`: Socket timeout
- `REDIS_HEALTH_CHECK_INTERVAL`: Health check interval
- `REDIS_MAX_RETRIES`: Max retry attempts
- `REDIS_RETRY_BACKOFF_FACTOR`: Retry backoff multiplier

#### GCP Settings
- `GCP_PROJECT_ID`: Google Cloud project ID
- `GCP_LOCATION`/`GCP_REGION`: Google Cloud region
- `GOOGLE_APPLICATION_CREDENTIALS`: Service account key path

### Pattern Mining Specific Variables

#### Gemini API Configuration
- `GEMINI_API_KEY`: Gemini API key
- `GEMINI_DEFAULT_MODEL`: Default model (gemini-2.5-pro/gemini-2.5-flash)
- `GEMINI_TEMPERATURE`: Generation temperature (0.0-1.0)
- `GEMINI_TOP_P`: Top-p nucleus sampling (0.0-1.0)
- `GEMINI_TOP_K`: Top-k sampling (1-100)
- `GEMINI_MAX_OUTPUT_TOKENS`: Max output tokens
- `GEMINI_REQUESTS_PER_MINUTE`: Rate limit per minute
- `GEMINI_TOKENS_PER_MINUTE`: Token rate limit per minute
- `GEMINI_CONCURRENT_REQUESTS`: Max concurrent requests

#### BigQuery Configuration
- `BIGQUERY_DATASET_ID`: BigQuery dataset ID
- `BIGQUERY_LOCATION`: BigQuery location (US/EU)
- `BIGQUERY_TABLE_PREFIX`: Table name prefix
- `BIGQUERY_MAX_CONNECTIONS`: Max connections
- `BIGQUERY_CONNECTION_TIMEOUT`: Connection timeout
- `BIGQUERY_READ_TIMEOUT`: Query timeout
- `BIGQUERY_USE_CACHE`: Enable query cache

#### ML Configuration
- `ML_MODEL_STORAGE_PATH`: Model storage path
- `ML_BATCH_SIZE`: ML batch size
- `ML_MAX_SEQUENCE_LENGTH`: Max sequence length
- `EMBEDDING_MODEL`: Embedding model name
- `EMBEDDING_DIMENSION`: Embedding dimension

#### Performance Settings
- `ENABLE_CACHING`: Enable caching
- `CACHE_TTL_SECONDS`: Cache TTL
- `MAX_REQUEST_SIZE`: Max request size in bytes
- `RESPONSE_TIMEOUT`: Response timeout
- `WORKER_TIMEOUT`: Worker timeout
- `MAX_PATTERNS_PER_REQUEST`: Max patterns per request
- `PATTERN_CONFIDENCE_THRESHOLD`: Pattern confidence threshold
- `ENABLE_STREAMING`: Enable streaming responses

#### Analytics & Monitoring
- `ENABLE_METRICS`: Enable metrics collection
- `ENABLE_TRACING`: Enable distributed tracing
- `METRICS_PORT`: Metrics server port
- `JAEGER_ENDPOINT`: Jaeger collector endpoint
- `ANALYTICS_RETENTION_DAYS`: Analytics data retention
- `ENABLE_REAL_TIME_ANALYTICS`: Real-time analytics

### Query Intelligence Specific Variables

#### Google GenAI Configuration
- `USE_VERTEX_AI`: Use Vertex AI (deprecated, always false)
- `GEMINI_MODEL_NAME`: Primary Gemini model
- `EMBEDDING_MODEL_NAME`: Embedding model
- `USE_MODEL_ROUTING`: Enable model routing
- `SIMPLE_QUERY_MODEL`: Model for simple queries
- `COMPLEX_QUERY_MODEL`: Model for complex queries
- `GOOGLE_API_KEY`: Google API key

#### Pinecone Configuration
- `PINECONE_API_KEY`: Pinecone API key
- `PINECONE_INDEX_NAME`: Pinecone index name
- `PINECONE_CLOUD`: Cloud provider (aws/gcp/azure)
- `PINECONE_REGION`: Pinecone region

#### Cache Configuration
- `CACHE_TTL_HOURS`: Cache TTL in hours
- `CACHE_MAX_SIZE`: Maximum cache size
- `SEMANTIC_CACHE_ENABLED`: Enable semantic caching

#### Performance Configuration
- `MAX_QUERY_LENGTH`: Maximum query length
- `MAX_CODE_CHUNKS`: Maximum code chunks
- `MAX_CONTEXT_TOKENS`: Maximum context tokens
- `MAX_OUTPUT_TOKENS`: Maximum output tokens
- `RESPONSE_TIMEOUT`: Response timeout
- `MAX_CONCURRENT_REQUESTS`: Max concurrent requests

#### Circuit Breaker Configuration
- `CIRCUIT_BREAKER_THRESHOLD`: Failure threshold
- `CIRCUIT_BREAKER_TIMEOUT`: Circuit breaker timeout
- `CIRCUIT_BREAKER_RECOVERY_TIMEOUT`: Recovery timeout

#### Streaming Configuration
- `ENABLE_STREAMING`: Enable streaming
- `STREAM_CHUNK_SIZE`: Stream chunk size
- `STREAM_DELAY_MS`: Stream delay in milliseconds

#### Feature Flags
- `ENABLE_EXPERIMENTAL_FEATURES`: Enable experimental features
- `ENABLE_DEBUG_LOGGING`: Enable debug logging
- `ENABLE_QUERY_OPTIMIZATION`: Enable query optimization
- `ENABLE_SEMANTIC_SEARCH`: Enable semantic search

#### WebSocket Configuration
- `WEBSOCKET_ENABLED`: Enable WebSocket support
- `WEBSOCKET_MAX_CONNECTIONS`: Max WebSocket connections
- `WEBSOCKET_HEARTBEAT_INTERVAL`: Heartbeat interval

#### External Service URLs
- `ANALYSIS_ENGINE_URL`: Analysis Engine service URL
- `PATTERN_MINING_URL`: Pattern Mining service URL
- `COLLABORATION_URL`: Collaboration service URL

## Health Check Endpoints

### Pattern Mining Service
- `/health/startup`: Startup probe endpoint
- `/health/liveness`: Liveness probe endpoint  
- `/health/readiness`: Readiness probe endpoint
- `/health`: General health check

### Query Intelligence Service
- `/health`: General health check
- `/health/ready`: Readiness probe endpoint

## Probe Configuration

### Production Probes
- **Startup Probe**: 30s initial delay, 10s period, 6 retries
- **Liveness Probe**: 60s initial delay, 30s period, 3 retries  
- **Readiness Probe**: 10s initial delay, 10s period, 3 retries

### Staging Probes
- **Startup Probe**: 15s initial delay, 5s period, 6 retries
- **Liveness Probe**: 30s initial delay, 15s period, 3 retries
- **Readiness Probe**: 5s initial delay, 5s period, 3 retries

## Deployment Steps

### 1. Setup Secrets
```bash
# Run the secrets setup script
./cloud-run-secrets-setup.sh

# Update secrets with real values in Google Secret Manager
gcloud secrets versions add [secret-name] --data-file=[file-with-secret]
```

### 2. Build Docker Images
```bash
# Pattern Mining
cd services/pattern-mining
docker build -t gcr.io/vibe-match-463114/pattern-mining:production --target production .
docker push gcr.io/vibe-match-463114/pattern-mining:production

# Query Intelligence  
cd services/query-intelligence
docker build -t gcr.io/vibe-match-463114/query-intelligence:production .
docker push gcr.io/vibe-match-463114/query-intelligence:production
```

### 3. Deploy to Cloud Run
```bash
# Apply Kubernetes secrets (if using GKE)
kubectl apply -f /tmp/pattern-mining-secrets.yaml
kubectl apply -f /tmp/pattern-mining-gcp-key.yaml

# Deploy with gcloud
gcloud run services replace cloudrun.production.yaml --region=us-central1
```

### 4. Verify Deployment
```bash
# Check service status
gcloud run services describe [service-name] --region=us-central1

# Test health endpoints
curl https://[service-url]/health
```

## Security Considerations

1. **Secret Management**: All sensitive data stored in Google Secret Manager
2. **Service Accounts**: Dedicated service accounts with minimal permissions
3. **Network Security**: Private Google Access for internal communication
4. **SSL/TLS**: All external traffic encrypted
5. **CORS**: Restricted origins for production environments
6. **Rate Limiting**: Configured per environment
7. **Input Validation**: Comprehensive validation on all inputs

## Monitoring and Observability

1. **Metrics**: Prometheus metrics on port 9090
2. **Tracing**: Jaeger distributed tracing
3. **Logging**: Structured JSON logging
4. **Health Checks**: Multiple probe types for reliability
5. **Alerts**: Based on health check failures and performance metrics

## Troubleshooting

### Common Issues

1. **Secret Access**: Ensure service accounts have `secretmanager.secretAccessor` role
2. **Image Pull**: Verify image exists and permissions are correct
3. **Health Checks**: Check endpoint paths and response formats
4. **Resource Limits**: Monitor CPU/memory usage and adjust if needed
5. **Cold Starts**: Use min instances > 0 for production to reduce latency

### Debug Commands
```bash
# View service logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=[service-name]" --limit=50

# Check service configuration
gcloud run services describe [service-name] --region=us-central1 --format=yaml

# Test connectivity
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" https://[service-url]/health
```

## Performance Optimization

1. **CPU Boost**: Enabled for production services
2. **Concurrency**: Tuned per service based on resource usage
3. **Min/Max Instances**: Set based on traffic patterns
4. **Keep-Alive**: Extended timeouts for persistent connections
5. **Caching**: Multi-level caching strategy
6. **Connection Pooling**: Optimized database and Redis connections