#!/bin/bash

# Cloud Run Services Secret Creation Script
# This script creates all necessary secrets in Google Secret Manager

set -e

PROJECT_ID="vibe-match-463114"
REGION="us-central1"

echo "🔐 Creating secrets for Cloud Run services..."

# Function to create or update a secret
create_secret() {
    local SECRET_NAME=$1
    local SECRET_VALUE=$2
    
    # Check if secret exists
    if gcloud secrets describe $SECRET_NAME --project=$PROJECT_ID 2>/dev/null; then
        echo "Secret $SECRET_NAME already exists, updating..."
        echo -n "$SECRET_VALUE" | gcloud secrets versions add $SECRET_NAME --data-file=- --project=$PROJECT_ID
    else
        echo "Creating secret $SECRET_NAME..."
        echo -n "$SECRET_VALUE" | gcloud secrets create $SECRET_NAME --data-file=- --project=$PROJECT_ID --replication-policy="automatic"
    fi
}

# Pattern Mining Secrets
echo "📦 Creating Pattern Mining secrets..."
create_secret "pattern-mining-redis-url" "redis://********:6379"
create_secret "pattern-mining-database-url" "*******************************************************************************************************************"

# Query Intelligence Secrets
echo "🔍 Creating Query Intelligence secrets..."
create_secret "query-intelligence-redis-url" "redis://********:6379"
create_secret "query-intelligence-jwt-secret" "$(openssl rand -base64 32)"

# Collaboration Service Secrets
echo "🤝 Creating Collaboration Service secrets..."
create_secret "collaboration-redis-url" "redis://********:6379"
create_secret "collaboration-jwt-secret" "$(openssl rand -base64 32)"

# Shared API Keys (placeholder - update with real values)
echo "🔑 Creating shared API key secrets..."
create_secret "gemini-api-key" "YOUR_GEMINI_API_KEY_HERE"
create_secret "anthropic-api-key" "YOUR_ANTHROPIC_API_KEY_HERE"
create_secret "pinecone-api-key" "YOUR_PINECONE_API_KEY_HERE"

# Service Account Permissions
echo "🛡️ Setting up service account permissions..."

# Create service accounts if they don't exist
for SERVICE in pattern-mining query-intelligence collaboration; do
    SA_NAME="${SERVICE}-sa"
    SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Create service account if it doesn't exist
    if ! gcloud iam service-accounts describe $SA_EMAIL --project=$PROJECT_ID 2>/dev/null; then
        echo "Creating service account $SA_NAME..."
        gcloud iam service-accounts create $SA_NAME \
            --display-name="${SERVICE} Service Account" \
            --project=$PROJECT_ID
    fi
    
    # Grant necessary roles
    echo "Granting roles to $SA_NAME..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/secretmanager.secretAccessor" \
        --quiet
    
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/logging.logWriter" \
        --quiet
    
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/monitoring.metricWriter" \
        --quiet
        
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/cloudtrace.agent" \
        --quiet
done

echo "✅ Secret creation complete!"
echo ""
echo "⚠️  IMPORTANT: Update the following placeholder secrets with actual values:"
echo "   - gemini-api-key"
echo "   - anthropic-api-key"
echo "   - pinecone-api-key"
echo ""
echo "Use: gcloud secrets versions add SECRET_NAME --data-file=- --project=$PROJECT_ID"