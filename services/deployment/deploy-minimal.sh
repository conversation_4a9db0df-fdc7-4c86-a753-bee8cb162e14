#!/bin/bash

# Minimal Cloud Run Deployment - No External Dependencies
# This script deploys services in "degraded" mode without Redis/DB
# Once running, we can add dependencies incrementally

set -e

PROJECT_ID="vibe-match-463114"
REGION="us-central1"

echo "🚀 Minimal Deployment Strategy - Get Services Running First"
echo "==========================================================="

# Function to deploy minimal service
deploy_minimal() {
    local SERVICE=$1
    local DIR=$2
    
    echo "📦 Deploying $SERVICE (minimal, no dependencies)..."
    
    cd "$DIR"
    
    # Create minimal Dockerfile if needed
    cat > Dockerfile.minimal << 'EOF'
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
ENV PORT=8080
ENV ENVIRONMENT=production
# Disable external dependencies
ENV REDIS_URL=""
ENV DATABASE_URL=""
ENV ENABLE_CACHE=false
CMD ["python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8080"]
EOF
    
    # Deploy with minimal config
    gcloud run deploy $SERVICE \
        --source . \
        --region $REGION \
        --platform managed \
        --allow-unauthenticated \
        --port 8080 \
        --min-instances 0 \
        --max-instances 2 \
        --memory 1Gi \
        --cpu 1 \
        --timeout 60 \
        --set-env-vars="PORT=8080,ENVIRONMENT=production,REDIS_URL=,DATABASE_URL=" \
        --quiet
    
    if [ $? -eq 0 ]; then
        echo "✅ $SERVICE deployed successfully!"
        URL=$(gcloud run services describe $SERVICE --region=$REGION --format="value(status.url)")
        echo "URL: $URL"
        
        # Test health endpoint
        sleep 5
        curl -s "$URL/health" || echo "Health check pending..."
    else
        echo "❌ $SERVICE deployment failed"
    fi
    
    cd -
}

# Deploy services
echo "1️⃣ Pattern Mining Service"
deploy_minimal "pattern-mining" "/Users/<USER>/Documents/GitHub/episteme/services/pattern-mining"

echo ""
echo "2️⃣ Query Intelligence Service"  
deploy_minimal "query-intelligence" "/Users/<USER>/Documents/GitHub/episteme/services/query-intelligence"

echo ""
echo "✅ Minimal deployment complete!"
echo "Services are running without external dependencies (degraded mode)"
echo "Next steps:"
echo "  1. Verify health endpoints"
echo "  2. Add Redis incrementally"
echo "  3. Add database connections"
echo "  4. Scale up resources"