#!/bin/bash

# Cloud Run Secrets Setup Script
# Creates Google Secret Manager secrets for both services and environments

set -euo pipefail

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure we're logged in and have the right project
log "Setting up Google Cloud project: $PROJECT_ID"
gcloud config set project $PROJECT_ID

# Enable required APIs
log "Enabling required Google Cloud APIs..."
gcloud services enable secretmanager.googleapis.com \
    run.googleapis.com \
    cloudbuild.googleapis.com \
    container.googleapis.com

# Function to create or update secret
create_or_update_secret() {
    local secret_name=$1
    local secret_value=$2
    local description=$3
    
    if gcloud secrets describe $secret_name --quiet 2>/dev/null; then
        warn "Secret $secret_name already exists, updating..."
        echo -n "$secret_value" | gcloud secrets versions add $secret_name --data-file=-
    else
        log "Creating secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create $secret_name \
            --replication-policy="automatic" \
            --data-file=- \
            --labels="service=episteme,type=configuration" \
            2>/dev/null || {
                warn "Failed to create secret $secret_name, trying to add version..."
                echo -n "$secret_value" | gcloud secrets versions add $secret_name --data-file=-
            }
    fi
}

# Pattern Mining Secrets
log "Creating Pattern Mining secrets..."

# Production secrets
log "Setting up Pattern Mining Production secrets..."
create_or_update_secret "pattern-mining-secret-key" "$(openssl rand -base64 32)" "Pattern Mining production secret key"
create_or_update_secret "pattern-mining-jwt-secret" "$(openssl rand -base64 32)" "Pattern Mining production JWT secret"
create_or_update_secret "pattern-mining-session-secret" "$(openssl rand -base64 32)" "Pattern Mining production session secret"
create_or_update_secret "pattern-mining-encryption-key" "$(openssl rand -base64 32)" "Pattern Mining production encryption key"
create_or_update_secret "pattern-mining-api-key" "pm-prod-$(openssl rand -hex 16)" "Pattern Mining production API key"
create_or_update_secret "pattern-mining-admin-api-key" "pm-admin-prod-$(openssl rand -hex 16)" "Pattern Mining production admin API key"

# Database and Redis URLs (you need to replace these with actual values)
create_or_update_secret "pattern-mining-database-url" "****************************************/pattern_mining_prod" "Pattern Mining production database URL"
create_or_update_secret "pattern-mining-postgres-password" "$(openssl rand -base64 32)" "Pattern Mining production postgres password"
create_or_update_secret "pattern-mining-redis-url" "redis://redis-prod.example.com:6379" "Pattern Mining production Redis URL"
create_or_update_secret "pattern-mining-redis-password" "$(openssl rand -base64 32)" "Pattern Mining production Redis password"

# Gemini API key (replace with actual key)
create_or_update_secret "pattern-mining-gemini-api-key" "YOUR_GEMINI_API_KEY_HERE" "Pattern Mining Gemini API key"

# Staging secrets
log "Setting up Pattern Mining Staging secrets..."
create_or_update_secret "pattern-mining-staging-secret-key" "$(openssl rand -base64 32)" "Pattern Mining staging secret key"
create_or_update_secret "pattern-mining-staging-jwt-secret" "$(openssl rand -base64 32)" "Pattern Mining staging JWT secret"
create_or_update_secret "pattern-mining-staging-session-secret" "$(openssl rand -base64 32)" "Pattern Mining staging session secret"
create_or_update_secret "pattern-mining-staging-encryption-key" "$(openssl rand -base64 32)" "Pattern Mining staging encryption key"
create_or_update_secret "pattern-mining-staging-database-url" "****************************************/pattern_mining_staging" "Pattern Mining staging database URL"
create_or_update_secret "pattern-mining-staging-redis-url" "redis://redis-staging.example.com:6379" "Pattern Mining staging Redis URL"
create_or_update_secret "pattern-mining-staging-gemini-api-key" "YOUR_GEMINI_API_KEY_HERE" "Pattern Mining staging Gemini API key"

# Query Intelligence Secrets
log "Creating Query Intelligence secrets..."

# Production secrets
log "Setting up Query Intelligence Production secrets..."
create_or_update_secret "query-intelligence-api-key" "qi-prod-$(openssl rand -hex 16)" "Query Intelligence production API key"
create_or_update_secret "query-intelligence-admin-api-key" "qi-admin-prod-$(openssl rand -hex 16)" "Query Intelligence production admin API key"
create_or_update_secret "query-intelligence-jwt-secret" "$(openssl rand -base64 32)" "Query Intelligence production JWT secret"
create_or_update_secret "query-intelligence-encryption-key" "$(openssl rand -base64 32)" "Query Intelligence production encryption key"
create_or_update_secret "query-intelligence-redis-url" "redis://redis-prod.example.com:6379" "Query Intelligence production Redis URL"
create_or_update_secret "query-intelligence-database-url" "****************************************/query_intelligence_prod" "Query Intelligence production database URL"
create_or_update_secret "query-intelligence-google-api-key" "YOUR_GEMINI_API_KEY_HERE" "Query Intelligence production Google API key"
create_or_update_secret "query-intelligence-pinecone-api-key" "YOUR_PINECONE_API_KEY_HERE" "Query Intelligence production Pinecone API key"

# Staging secrets
log "Setting up Query Intelligence Staging secrets..."
create_or_update_secret "query-intelligence-staging-api-key" "qi-staging-$(openssl rand -hex 16)" "Query Intelligence staging API key"
create_or_update_secret "query-intelligence-staging-admin-api-key" "qi-admin-staging-$(openssl rand -hex 16)" "Query Intelligence staging admin API key"
create_or_update_secret "query-intelligence-staging-jwt-secret" "$(openssl rand -base64 32)" "Query Intelligence staging JWT secret"
create_or_update_secret "query-intelligence-staging-redis-url" "redis://redis-staging.example.com:6379" "Query Intelligence staging Redis URL"
create_or_update_secret "query-intelligence-staging-database-url" "****************************************/query_intelligence_staging" "Query Intelligence staging database URL"
create_or_update_secret "query-intelligence-staging-google-api-key" "YOUR_GEMINI_API_KEY_HERE" "Query Intelligence staging Google API key"
create_or_update_secret "query-intelligence-staging-pinecone-api-key" "YOUR_PINECONE_API_KEY_HERE" "Query Intelligence staging Pinecone API key"

# Create Kubernetes secrets for the Cloud Run YAML files
log "Creating Kubernetes secrets for Cloud Run deployment..."

# Pattern Mining Production
kubectl create secret generic pattern-mining-secrets \
  --from-literal=secret-key="$(gcloud secrets versions access latest --secret=pattern-mining-secret-key)" \
  --from-literal=jwt-secret="$(gcloud secrets versions access latest --secret=pattern-mining-jwt-secret)" \
  --from-literal=session-secret="$(gcloud secrets versions access latest --secret=pattern-mining-session-secret)" \
  --from-literal=encryption-key="$(gcloud secrets versions access latest --secret=pattern-mining-encryption-key)" \
  --from-literal=api-key="$(gcloud secrets versions access latest --secret=pattern-mining-api-key)" \
  --from-literal=admin-api-key="$(gcloud secrets versions access latest --secret=pattern-mining-admin-api-key)" \
  --from-literal=database-url="$(gcloud secrets versions access latest --secret=pattern-mining-database-url)" \
  --from-literal=postgres-password="$(gcloud secrets versions access latest --secret=pattern-mining-postgres-password)" \
  --from-literal=redis-url="$(gcloud secrets versions access latest --secret=pattern-mining-redis-url)" \
  --from-literal=redis-password="$(gcloud secrets versions access latest --secret=pattern-mining-redis-password)" \
  --from-literal=gemini-api-key="$(gcloud secrets versions access latest --secret=pattern-mining-gemini-api-key)" \
  --dry-run=client -o yaml > /tmp/pattern-mining-secrets.yaml

# Pattern Mining Staging
kubectl create secret generic pattern-mining-staging-secrets \
  --from-literal=secret-key="$(gcloud secrets versions access latest --secret=pattern-mining-staging-secret-key)" \
  --from-literal=jwt-secret="$(gcloud secrets versions access latest --secret=pattern-mining-staging-jwt-secret)" \
  --from-literal=session-secret="$(gcloud secrets versions access latest --secret=pattern-mining-staging-session-secret)" \
  --from-literal=encryption-key="$(gcloud secrets versions access latest --secret=pattern-mining-staging-encryption-key)" \
  --from-literal=database-url="$(gcloud secrets versions access latest --secret=pattern-mining-staging-database-url)" \
  --from-literal=redis-url="$(gcloud secrets versions access latest --secret=pattern-mining-staging-redis-url)" \
  --from-literal=gemini-api-key="$(gcloud secrets versions access latest --secret=pattern-mining-staging-gemini-api-key)" \
  --dry-run=client -o yaml > /tmp/pattern-mining-staging-secrets.yaml

# Query Intelligence Production
kubectl create secret generic query-intelligence-secrets \
  --from-literal=api-key="$(gcloud secrets versions access latest --secret=query-intelligence-api-key)" \
  --from-literal=admin-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-admin-api-key)" \
  --from-literal=jwt-secret="$(gcloud secrets versions access latest --secret=query-intelligence-jwt-secret)" \
  --from-literal=encryption-key="$(gcloud secrets versions access latest --secret=query-intelligence-encryption-key)" \
  --from-literal=redis-url="$(gcloud secrets versions access latest --secret=query-intelligence-redis-url)" \
  --from-literal=database-url="$(gcloud secrets versions access latest --secret=query-intelligence-database-url)" \
  --from-literal=google-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-google-api-key)" \
  --from-literal=pinecone-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-pinecone-api-key)" \
  --dry-run=client -o yaml > /tmp/query-intelligence-secrets.yaml

# Query Intelligence Staging
kubectl create secret generic query-intelligence-staging-secrets \
  --from-literal=api-key="$(gcloud secrets versions access latest --secret=query-intelligence-staging-api-key)" \
  --from-literal=admin-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-staging-admin-api-key)" \
  --from-literal=jwt-secret="$(gcloud secrets versions access latest --secret=query-intelligence-staging-jwt-secret)" \
  --from-literal=redis-url="$(gcloud secrets versions access latest --secret=query-intelligence-staging-redis-url)" \
  --from-literal=database-url="$(gcloud secrets versions access latest --secret=query-intelligence-staging-database-url)" \
  --from-literal=google-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-staging-google-api-key)" \
  --from-literal=pinecone-api-key="$(gcloud secrets versions access latest --secret=query-intelligence-staging-pinecone-api-key)" \
  --dry-run=client -o yaml > /tmp/query-intelligence-staging-secrets.yaml

log "Created Kubernetes secret YAML files in /tmp/ - you can apply them with 'kubectl apply -f /tmp/[secret-name].yaml'"

# Service Account Setup
log "Setting up service accounts..."

# Create service accounts
gcloud iam service-accounts create pattern-mining-sa \
    --display-name="Pattern Mining Service Account" \
    --description="Service account for Pattern Mining service" \
    2>/dev/null || warn "Service account pattern-mining-sa may already exist"

gcloud iam service-accounts create pattern-mining-staging-sa \
    --display-name="Pattern Mining Staging Service Account" \
    --description="Service account for Pattern Mining staging service" \
    2>/dev/null || warn "Service account pattern-mining-staging-sa may already exist"

gcloud iam service-accounts create query-intelligence-sa \
    --display-name="Query Intelligence Service Account" \
    --description="Service account for Query Intelligence service" \
    2>/dev/null || warn "Service account query-intelligence-sa may already exist"

gcloud iam service-accounts create query-intelligence-staging-sa \
    --display-name="Query Intelligence Staging Service Account" \
    --description="Service account for Query Intelligence staging service" \
    2>/dev/null || warn "Service account query-intelligence-staging-sa may already exist"

# Grant permissions to service accounts
log "Granting IAM permissions..."

# Pattern Mining permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/bigquery.jobUser"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

# Query Intelligence permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer"

# Similar permissions for staging service accounts
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:pattern-mining-staging-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:query-intelligence-staging-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

# Create service account keys
log "Creating service account keys..."
gcloud iam service-accounts keys create pattern-mining-sa-key.json \
    --iam-account=pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com

gcloud iam service-accounts keys create query-intelligence-sa-key.json \
    --iam-account=query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com

# Create Kubernetes secrets for service account keys
kubectl create secret generic pattern-mining-gcp-key \
    --from-file=key.json=pattern-mining-sa-key.json \
    --dry-run=client -o yaml > /tmp/pattern-mining-gcp-key.yaml

kubectl create secret generic query-intelligence-gcp-key \
    --from-file=key.json=query-intelligence-sa-key.json \
    --dry-run=client -o yaml > /tmp/query-intelligence-gcp-key.yaml

# Clean up key files
rm -f pattern-mining-sa-key.json query-intelligence-sa-key.json

log "✅ Setup complete!"
log "Next steps:"
log "1. Update the secret values in Google Secret Manager with actual values:"
log "   - Database URLs with real connection strings"
log "   - Redis URLs with real Redis instances" 
log "   - Gemini API keys with your actual keys"
log "   - Pinecone API keys if using Pinecone"
log ""
log "2. Apply the Kubernetes secrets to your cluster:"
log "   kubectl apply -f /tmp/pattern-mining-secrets.yaml"
log "   kubectl apply -f /tmp/pattern-mining-staging-secrets.yaml"
log "   kubectl apply -f /tmp/query-intelligence-secrets.yaml"
log "   kubectl apply -f /tmp/query-intelligence-staging-secrets.yaml"
log "   kubectl apply -f /tmp/pattern-mining-gcp-key.yaml"
log "   kubectl apply -f /tmp/query-intelligence-gcp-key.yaml"
log ""
log "3. Deploy the services using the Cloud Run YAML files"
log ""
warn "Remember to replace placeholder values with actual production values!"