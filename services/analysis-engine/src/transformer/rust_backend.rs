//! Rust transformer backend integration for Analysis Engine

use crate::models::ast::{AstNode as AnalysisAstNode, ParsedAst};
use crate::transformer::{TransformationError, TransformedAst, OptimizedNode, NodeData, TransformMetadata};

// Python integration - feature gated
#[cfg(feature = "python-integration")]
use pyo3::prelude::*;
#[cfg(feature = "python-integration")]
use pyo3::types::PyDict;

// Stub types for compilation when Python integration is disabled
#[cfg(not(feature = "python-integration"))]
type Py<T> = Arc<T>;
#[cfg(not(feature = "python-integration"))]
type PyModule = ();
#[cfg(not(feature = "python-integration"))]
type PyObject = ();
#[cfg(not(feature = "python-integration"))]
#[allow(dead_code)]
type PyList = Vec<()>;
#[cfg(not(feature = "python-integration"))]
#[allow(dead_code)]
type PyDict = std::collections::HashMap<String, String>;
#[cfg(not(feature = "python-integration"))]
type PyErr = String;
#[cfg(not(feature = "python-integration"))]
#[allow(dead_code)]
type PyResult<T> = Result<T, PyErr>;

use serde_json;
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, info};

/// Rust transformer backend configuration
#[derive(Debug, Clone)]
pub struct RustTransformerConfig {
    pub enabled: bool,
    pub thread_count: usize,
    pub memory_pool_mb: usize,
    pub batch_size: usize,
    pub enable_simd: bool,
    pub python_lib_path: Option<String>,
}

impl Default for RustTransformerConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            thread_count: num_cpus::get(),
            memory_pool_mb: 1024,
            batch_size: 1000,
            enable_simd: true,
            python_lib_path: None,
        }
    }
}

/// Rust transformer backend
pub struct RustTransformerBackend {
    config: RustTransformerConfig,
    _py_module: Option<Py<PyModule>>,
    transformer: Option<PyObject>,
}

impl RustTransformerBackend {
    /// Create a new Rust transformer backend
    pub fn new(config: RustTransformerConfig) -> Result<Self, TransformationError> {
        let mut backend = Self {
            config,
            _py_module: None,
            transformer: None,
        };
        
        if backend.config.enabled {
            backend.initialize()?;
        }
        
        Ok(backend)
    }
    
    /// Initialize the Python module and transformer
    #[allow(unused_variables)]
    fn initialize(&mut self) -> Result<(), TransformationError> {
        #[cfg(feature = "python-integration")]
        {
            use pyo3::Python;

            Python::with_gil(|py| {
                // Import the transformer module
                let transformer_module = py.import("ast_transformer")
                    .map_err(|e| TransformationError::InitializationError(
                        format!("Failed to import Python transformer module: {}", e)
                    ))?;

                // Create transformer instance
                let transformer_class = transformer_module.getattr("AstTransformer")
                    .map_err(|e| TransformationError::InitializationError(
                        format!("Failed to get AstTransformer class: {}", e)
                    ))?;

                let transformer_instance = transformer_class.call0()
                    .map_err(|e| TransformationError::InitializationError(
                        format!("Failed to create transformer instance: {}", e)
                    ))?;

                self.transformer = Some(transformer_instance.into());
                Ok(())
            })
        }

        #[cfg(not(feature = "python-integration"))]
        {
            tracing::warn!("Python integration is disabled. To enable, compile with --features python-integration");
            Err(TransformationError::InitializationError(
                "Python integration is disabled. Compile with --features python-integration to enable.".to_string()
            ))
        }
    }
    
    /// Check if the backend is available
    pub fn is_available(&self) -> bool {
        self.transformer.is_some()
    }
    
    /// Transform an AST using the Rust backend
    pub async fn transform(&self, ast: &ParsedAst) -> Result<TransformedAst, TransformationError> {
        if !self.is_available() {
            return Err(TransformationError::BackendUnavailable(
                "Rust transformer not initialized".to_string()
            ));
        }
        
        let start = Instant::now();
        
        // Convert AST to JSON for Python interop
        let _ast_json = self.convert_ast_to_json(ast)?;
        
        // Call transformer based on feature availability
        let result = self.call_transformer(&_ast_json)?;
        
        let duration = start.elapsed();
        info!("Rust transformer completed in {:?}", duration);
        
        // Convert result to TransformedAst
        self.build_transformed_ast(result.0, result.1, result.2, ast.file_path.clone(), duration.as_micros() as u64)
    }
    
    /// Transform with specific parallelism
    pub async fn transform_parallel(&self, ast: &ParsedAst, _num_threads: usize) -> Result<TransformedAst, TransformationError> {
        if !self.is_available() {
            return Err(TransformationError::BackendUnavailable(
                "Rust transformer not initialized".to_string()
            ));
        }
        
        let start = Instant::now();
        let _ast_json = self.convert_ast_to_json(ast)?;

        // Call transformer based on feature availability
        let result = self.call_transformer(&_ast_json)?;
        
        let duration = start.elapsed();
        info!("Rust transformer (parallel) completed in {:?}", duration);
        
        self.build_transformed_ast(result.0, result.1, result.2, ast.file_path.clone(), duration.as_micros() as u64)
    }
    
    /// Get performance statistics
    pub fn get_stats(&self) -> Result<serde_json::Value, TransformationError> {
        if !self.is_available() {
            return Ok(serde_json::json!({
                "available": false
            }));
        }
        
        // Get stats based on feature availability
        self.get_transformer_stats()
    }
    
    /// Convert Analysis Engine AST to JSON
    fn convert_ast_to_json(&self, ast: &ParsedAst) -> Result<serde_json::Value, TransformationError> {
        let json_ast = self.convert_node_to_json(&ast.root_node);
        
        Ok(serde_json::json!({
            "type": "Program",
            "metadata": {
                "file_path": ast.file_path,
                "language": ast.language,
                "start_line": 0,
                "start_column": 0,
                "end_line": ast.metadata.line_count,
                "end_column": 0
            },
            "children": [json_ast],
            "attributes": {}
        }))
    }
    
    /// Convert a single node to JSON
    fn convert_node_to_json(&self, node: &AnalysisAstNode) -> serde_json::Value {
        let mut json_node = serde_json::json!({
            "type": node.node_type,
            "metadata": {
                "start_line": node.start_position.line,
                "start_column": node.start_position.column,
                "end_line": node.end_position.line,
                "end_column": node.end_position.column
            },
            "attributes": {}
        });
        
        // Add text as attribute if present
        if let Some(ref text) = node.text {
            json_node["attributes"]["text"] = serde_json::Value::String(text.clone());
        }
        
        // Convert children
        let children: Vec<serde_json::Value> = node.children
            .iter()
            .map(|child| self.convert_node_to_json(child))
            .collect();
        
        json_node["children"] = serde_json::Value::Array(children);
        
        json_node
    }
    
    /// Build TransformedAst from Rust transformer output
    fn build_transformed_ast(
        &self,
        nodes: Vec<serde_json::Value>,
        metadata: serde_json::Value,
        stats: Option<serde_json::Value>,
        file_path: String,
        duration_us: u64,
    ) -> Result<TransformedAst, TransformationError> {
        let mut optimized_nodes = Vec::new();
        let symbols = Vec::new();
        let mut intern_table = Vec::new();
        
        // Process nodes
        for (idx, node_json) in nodes.iter().enumerate() {
            let node_obj = node_json.as_object()
                .ok_or_else(|| TransformationError::TransformError("Invalid node format".to_string()))?;
            
            let type_str = node_obj.get("type")
                .and_then(|v| v.as_str())
                .ok_or_else(|| TransformationError::TransformError("Missing node type".to_string()))?;
            
            // Simple string interning
            let type_idx = intern_table.iter().position(|s| s == type_str)
                .unwrap_or_else(|| {
                    intern_table.push(type_str.to_string());
                    intern_table.len() - 1
                }) as u16;
            
            let optimized_node = OptimizedNode {
                id: idx as u32,
                parent_id: node_obj.get("parent_idx").and_then(|v| v.as_u64()).map(|v| v as u32),
                type_idx,
                start: 0, // Would need proper position data
                end: 0,
                data: NodeData::Generic,
            };
            
            optimized_nodes.push(optimized_node);
        }
        
        // Extract node count from metadata
        let node_count = metadata.get("node_count")
            .and_then(|v| v.as_u64())
            .unwrap_or(optimized_nodes.len() as u64) as usize;
        
        // Log stats if available
        if let Some(stats) = stats {
            debug!("Rust transformer stats: {:?}", stats);
        }
        
        Ok(TransformedAst {
            node_count,
            nodes: optimized_nodes,
            symbols,
            metadata: TransformMetadata {
                file_path,
                language: metadata.get("language")
                    .and_then(|v| v.as_str())
                    .unwrap_or("unknown")
                    .to_string(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
                duration_us,
                intern_table,
            },
        })
    }
}

/// Determine whether to use Rust transformer based on AST characteristics
pub fn should_use_rust_transformer(ast: &ParsedAst, config: &RustTransformerConfig) -> bool {
    if !config.enabled {
        return false;
    }
    
    // Use Rust for large ASTs
    let node_count = estimate_node_count(&ast.root_node);
    if node_count > 10000 {
        debug!("Using Rust transformer for large AST ({} nodes)", node_count);
        return true;
    }
    
    // Use Rust for complex ASTs (deep nesting)
    let depth = calculate_depth(&ast.root_node);
    if depth > 50 {
        debug!("Using Rust transformer for deep AST (depth: {})", depth);
        return true;
    }
    
    // Use Rust for specific languages that benefit from performance
    let performance_languages = ["rust", "c", "cpp", "go", "java"];
    if performance_languages.contains(&ast.language.as_str()) {
        debug!("Using Rust transformer for performance language: {}", ast.language);
        return true;
    }
    
    false
}

fn estimate_node_count(node: &AnalysisAstNode) -> usize {
    1 + node.children.iter().map(|c| estimate_node_count(c)).sum::<usize>()
}

fn calculate_depth(node: &AnalysisAstNode) -> usize {
    node.children
        .iter()
        .map(|c| calculate_depth(c))
        .max()
        .unwrap_or(0) + 1
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::ast::{Position, FileMetadata};
    
    #[test]
    fn test_rust_config_default() {
        let config = RustTransformerConfig::default();
        assert!(!config.enabled);
        assert_eq!(config.memory_pool_mb, 1024);
        assert!(config.enable_simd);
    }
    
    #[test]
    fn test_should_use_rust_transformer() {
        let mut ast = create_test_ast();
        let config = RustTransformerConfig {
            enabled: true,
            ..Default::default()
        };
        
        // Small AST shouldn't use Rust
        assert!(!should_use_rust_transformer(&ast, &config));
        
        // Large AST should use Rust
        for _ in 0..10000 {
            ast.root_node.children.push(create_test_node());
        }
        assert!(should_use_rust_transformer(&ast, &config));
    }
    
    fn create_test_ast() -> ParsedAst {
        ParsedAst {
            file_path: "test.js".to_string(),
            language: "javascript".to_string(),
            root_node: create_test_node(),
            symbols: vec![],
            imports: vec![],
            exports: vec![],
            metadata: FileMetadata {
                line_count: 100,
                character_count: 1000,
                has_syntax_errors: false,
                complexity_score: 10.0,
                language_version: None,
            },
        }
    }
    
    fn create_test_node() -> AnalysisAstNode {
        AnalysisAstNode {
            node_type: "FunctionDeclaration".to_string(),
            start_position: Position { line: 1, column: 0, byte: 0 },
            end_position: Position { line: 5, column: 1, byte: 50 },
            text: Some("function test() {}".to_string()),
            children: vec![],
            is_named: true,
        }
    }

    /// Call transformer based on feature availability
    fn call_transformer(&self, ast_json: &serde_json::Value) -> Result<(Vec<OptimizedNode>, serde_json::Value, Option<serde_json::Value>), TransformationError> {
        #[cfg(feature = "python-integration")]
        {
            use pyo3::Python;

            Python::with_gil(|py| {
                let transformer = self.transformer.as_ref()
                    .ok_or_else(|| TransformationError::BackendUnavailable("Transformer not initialized".to_string()))?;

                // Call the transform method
                let result = transformer.call_method1(py, "transform", (ast_json.to_string(),))
                    .map_err(|e| TransformationError::TransformationFailed(format!("Python transform failed: {}", e)))?;

                // Parse the result
                let result_str: String = result.extract(py)
                    .map_err(|e| TransformationError::TransformationFailed(format!("Failed to extract result: {}", e)))?;

                let parsed_result: serde_json::Value = serde_json::from_str(&result_str)
                    .map_err(|e| TransformationError::TransformationFailed(format!("Failed to parse result JSON: {}", e)))?;

                // Extract components from result
                let nodes = vec![]; // Would parse from result
                let metadata = parsed_result.get("metadata").cloned().unwrap_or_default();
                let stats = parsed_result.get("stats").cloned();

                Ok((nodes, metadata, stats))
            })
        }

        #[cfg(not(feature = "python-integration"))]
        {
            // Return placeholder result when Python integration is disabled
            tracing::debug!("Python integration disabled, returning placeholder transformation result");
            Ok((vec![], serde_json::json!({"python_integration": false}), None))
        }
    }

    /// Get transformer stats based on feature availability
    fn get_transformer_stats(&self) -> Result<serde_json::Value, TransformationError> {
        #[cfg(feature = "python-integration")]
        {
            use pyo3::Python;

            Python::with_gil(|py| {
                let transformer = self.transformer.as_ref()
                    .ok_or_else(|| TransformationError::BackendUnavailable("Transformer not initialized".to_string()))?;

                let stats = transformer.call_method0(py, "get_stats")
                    .map_err(|e| TransformationError::TransformationFailed(format!("Failed to get stats: {}", e)))?;

                let stats_str: String = stats.extract(py)
                    .map_err(|e| TransformationError::TransformationFailed(format!("Failed to extract stats: {}", e)))?;

                let stats_value: serde_json::Value = serde_json::from_str(&stats_str)
                    .map_err(|e| TransformationError::TransformationFailed(format!("Failed to parse stats JSON: {}", e)))?;

                Ok(stats_value)
            })
        }

        #[cfg(not(feature = "python-integration"))]
        {
            Ok(serde_json::json!({
                "available": false,
                "python_integration": false,
                "message": "Python integration is disabled. Compile with --features python-integration to enable."
            }))
        }
    }
}