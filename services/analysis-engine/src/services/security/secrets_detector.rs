//! Secrets Detection Module for Analysis Engine
//!
//! This module provides comprehensive secrets detection capabilities

use crate::errors::AnalysisResult;
use crate::models::security::{SecuritySeverity, SecurityVulnerability};
use crate::models::FileAnalysis;
use chrono::{DateTime, Utc};
use regex::Regex;
use std::collections::{HashMap, HashSet};
use tracing::{debug, info};
use uuid::Uuid;
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};

/// A detected secret in source code
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectedSecret {
    pub id: String,
    pub analysis_id: String,
    pub file_path: String,
    pub line_number: usize,
    pub column_range: (usize, usize),
    pub secret_type: SecretType,
    pub masked_value: String,
    pub value_hash: String,
    pub confidence: f64,
    pub entropy: f64,
    pub severity: SecuritySeverity,
    pub context: String,
    pub remediation: String,
    pub is_false_positive: bool,
    pub metadata: HashMap<String, String>,
    pub detected_at: DateTime<Utc>,
}

/// Types of secrets that can be detected
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum SecretType {
    AwsAccessKey,
    AwsSecretKey,
    GoogleApiKey,
    GitHubToken,
    ApiKey,
    JwtToken,
    DatabaseUrl,
    PrivateKey,
    Password,
    HighEntropyString,
}

impl SecretType {
    pub fn display_name(&self) -> &str {
        match self {
            SecretType::AwsAccessKey => "AWS Access Key",
            SecretType::AwsSecretKey => "AWS Secret Key",
            SecretType::GoogleApiKey => "Google API Key",
            SecretType::GitHubToken => "GitHub Token",
            SecretType::ApiKey => "API Key",
            SecretType::JwtToken => "JWT Token",
            SecretType::DatabaseUrl => "Database URL",
            SecretType::PrivateKey => "Private Key",
            SecretType::Password => "Password",
            SecretType::HighEntropyString => "High Entropy String",
        }
    }

    pub fn default_severity(&self) -> SecuritySeverity {
        match self {
            SecretType::AwsAccessKey | SecretType::AwsSecretKey => SecuritySeverity::Critical,
            SecretType::GoogleApiKey | SecretType::DatabaseUrl => SecuritySeverity::Critical,
            SecretType::PrivateKey => SecuritySeverity::Critical,
            SecretType::GitHubToken | SecretType::ApiKey => SecuritySeverity::High,
            SecretType::JwtToken | SecretType::Password => SecuritySeverity::Medium,
            SecretType::HighEntropyString => SecuritySeverity::Low,
        }
    }
}

/// Secret detection pattern
#[derive(Debug, Clone)]
pub struct SecretPattern {
    pub secret_type: SecretType,
    pub pattern: Regex,
    pub min_entropy: f64,
    pub check_entropy: bool,
    pub keywords: Vec<String>,
    pub false_positive_keywords: Vec<String>,
}

/// Secrets detector service
pub struct SecretsDetector {
    patterns: Vec<SecretPattern>,
    whitelist: HashSet<String>,
    max_line_length: usize,
    min_confidence: f64,
}

impl Default for SecretsDetector {
    fn default() -> Self {
        Self::new()
    }
}

impl SecretsDetector {
    /// Create a new secrets detector with default patterns
    pub fn new() -> Self {
        let mut detector = Self {
            patterns: Vec::new(),
            whitelist: HashSet::new(),
            max_line_length: 10000,
            min_confidence: 0.5,
        };

        detector.initialize_default_patterns();
        detector.initialize_default_whitelist();
        detector
    }

    /// Initialize default secret detection patterns
    fn initialize_default_patterns(&mut self) {
        // AWS Access Key
        self.add_pattern(SecretPattern {
            secret_type: SecretType::AwsAccessKey,
            pattern: Regex::new(r"AKIA[0-9A-Z]{16}").unwrap(),
            min_entropy: 4.0,
            check_entropy: true,
            keywords: vec!["aws".to_string(), "amazon".to_string(), "access".to_string()],
            false_positive_keywords: vec!["example".to_string(), "test".to_string()],
        });

        // Google API Key
        self.add_pattern(SecretPattern {
            secret_type: SecretType::GoogleApiKey,
            pattern: Regex::new(r"AIza[0-9A-Za-z_-]{35}").unwrap(),
            min_entropy: 4.5,
            check_entropy: true,
            keywords: vec!["google".to_string(), "api".to_string(), "key".to_string()],
            false_positive_keywords: vec!["example".to_string(), "test".to_string()],
        });

        // GitHub Token
        self.add_pattern(SecretPattern {
            secret_type: SecretType::GitHubToken,
            pattern: Regex::new(r"gh[pousr]_[A-Za-z0-9_]{36,255}").unwrap(),
            min_entropy: 4.0,
            check_entropy: true,
            keywords: vec!["github".to_string(), "token".to_string()],
            false_positive_keywords: vec!["example".to_string(), "test".to_string()],
        });

        // JWT Token
        self.add_pattern(SecretPattern {
            secret_type: SecretType::JwtToken,
            pattern: Regex::new(r"eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+").unwrap(),
            min_entropy: 4.0,
            check_entropy: false,
            keywords: vec!["jwt".to_string(), "token".to_string(), "bearer".to_string()],
            false_positive_keywords: vec!["example".to_string(), "test".to_string()],
        });

        // Private Key
        self.add_pattern(SecretPattern {
            secret_type: SecretType::PrivateKey,
            pattern: Regex::new(r"-----BEGIN [A-Z ]+PRIVATE KEY-----").unwrap(),
            min_entropy: 3.0,
            check_entropy: false,
            keywords: vec!["private".to_string(), "key".to_string()],
            false_positive_keywords: vec!["example".to_string(), "test".to_string()],
        });
    }

    /// Initialize default whitelist
    fn initialize_default_whitelist(&mut self) {
        let false_positives = vec![
            "your_api_key_here",
            "example_key",
            "test_key",
            "fake_key",
            "placeholder",
            "xxxxxxxxxxxxxxxx",
        ];

        for fp in false_positives {
            self.whitelist.insert(fp.to_string());
        }
    }

    /// Add a custom detection pattern
    pub fn add_pattern(&mut self, pattern: SecretPattern) {
        self.patterns.push(pattern);
    }

    /// Detect secrets in file analysis
    pub async fn detect_secrets(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
    ) -> AnalysisResult<Vec<DetectedSecret>> {
        let mut detected_secrets = Vec::new();

        debug!(
            analysis_id = %analysis_id,
            file_path = %file_analysis.path,
            "Starting secrets detection"
        );

        // Extract content from chunks if available, otherwise use empty content
        let content = if let Some(chunks) = &file_analysis.chunks {
            chunks.iter().map(|chunk| chunk.content.as_str()).collect::<Vec<_>>().join("\n")
        } else {
            String::new()
        };
        let lines: Vec<&str> = content.lines().collect();

        for (line_number, line) in lines.iter().enumerate() {
            if line.len() > self.max_line_length {
                continue;
            }

            // Skip comments
            let trimmed_line = line.trim();
            if trimmed_line.starts_with("//") || 
               trimmed_line.starts_with("#") ||
               trimmed_line.starts_with("<!--") {
                continue;
            }

            for pattern in &self.patterns {
                if let Some(captures) = pattern.pattern.captures(line) {
                    if let Some(matched) = captures.get(0) {
                        let secret_value = matched.as_str();
                        
                        if self.is_whitelisted(secret_value) {
                            continue;
                        }

                        let entropy = if pattern.check_entropy {
                            self.calculate_entropy(secret_value)
                        } else {
                            pattern.min_entropy + 1.0
                        };

                        if entropy < pattern.min_entropy {
                            continue;
                        }

                        let confidence = self.calculate_confidence(
                            secret_value,
                            line,
                            pattern,
                            entropy,
                        );

                        if confidence < self.min_confidence {
                            continue;
                        }

                        let detected_secret = DetectedSecret {
                            id: Uuid::new_v4().to_string(),
                            analysis_id: analysis_id.to_string(),
                            file_path: file_analysis.path.clone(),
                            line_number: line_number + 1,
                            column_range: (matched.start(), matched.end()),
                            secret_type: pattern.secret_type.clone(),
                            masked_value: self.mask_secret(secret_value),
                            value_hash: self.hash_secret(secret_value),
                            confidence,
                            entropy,
                            severity: pattern.secret_type.default_severity(),
                            context: line.to_string(),
                            remediation: self.get_remediation_advice(&pattern.secret_type),
                            is_false_positive: false,
                            metadata: HashMap::new(),
                            detected_at: Utc::now(),
                        };

                        detected_secrets.push(detected_secret);
                    }
                }
            }
        }

        info!(
            analysis_id = %analysis_id,
            file_path = %file_analysis.path,
            secrets_count = detected_secrets.len(),
            "Secrets detection completed"
        );

        Ok(detected_secrets)
    }

    fn is_whitelisted(&self, value: &str) -> bool {
        self.whitelist.contains(value) ||
        value.to_lowercase().contains("example") ||
        value.to_lowercase().contains("test") ||
        value.to_lowercase().contains("fake")
    }

    fn calculate_entropy(&self, value: &str) -> f64 {
        if value.is_empty() {
            return 0.0;
        }

        let mut char_counts = HashMap::new();
        for ch in value.chars() {
            *char_counts.entry(ch).or_insert(0) += 1;
        }

        let length = value.len() as f64;
        let mut entropy = 0.0;

        for count in char_counts.values() {
            let probability = *count as f64 / length;
            entropy -= probability * probability.log2();
        }

        entropy
    }

    fn calculate_confidence(
        &self,
        secret_value: &str,
        line: &str,
        pattern: &SecretPattern,
        entropy: f64,
    ) -> f64 {
        let mut confidence = 0.5;

        if pattern.check_entropy {
            let entropy_score = (entropy - pattern.min_entropy) / (6.0 - pattern.min_entropy);
            confidence += entropy_score.clamp(0.0, 0.3);
        }

        let line_lower = line.to_lowercase();
        for keyword in &pattern.keywords {
            if line_lower.contains(keyword) {
                confidence += 0.1;
            }
        }

        for fp_keyword in &pattern.false_positive_keywords {
            if line_lower.contains(fp_keyword) || secret_value.to_lowercase().contains(fp_keyword) {
                confidence -= 0.3;
            }
        }

        confidence.clamp(0.0, 1.0)
    }

    fn mask_secret(&self, secret: &str) -> String {
        if secret.len() <= 8 {
            "*".repeat(secret.len())
        } else {
            format!("{}***{}", &secret[..4], &secret[secret.len()-4..])
        }
    }

    fn hash_secret(&self, secret: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(secret.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    fn get_remediation_advice(&self, secret_type: &SecretType) -> String {
        match secret_type {
            SecretType::AwsAccessKey | SecretType::AwsSecretKey => {
                "Rotate AWS credentials immediately and use IAM roles or environment variables.".to_string()
            }
            SecretType::GoogleApiKey => {
                "Rotate the Google API key and use environment variables or Secret Manager.".to_string()
            }
            SecretType::GitHubToken => {
                "Revoke the GitHub token and create a new one. Use GitHub Actions secrets.".to_string()
            }
            SecretType::ApiKey => {
                "Rotate the API key and move it to environment variables.".to_string()
            }
            SecretType::JwtToken => {
                "Remove hardcoded JWT tokens and implement proper token management.".to_string()
            }
            SecretType::DatabaseUrl => {
                "Move database connection strings to environment variables.".to_string()
            }
            SecretType::PrivateKey => {
                "Remove private keys from source code and store them securely.".to_string()
            }
            SecretType::Password => {
                "Remove hardcoded passwords and use secure password storage.".to_string()
            }
            SecretType::HighEntropyString => {
                "Review this high-entropy string for sensitive information.".to_string()
            }
        }
    }
}
