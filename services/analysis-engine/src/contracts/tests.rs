//! Contract compliance tests for AST Output v1 JSON schema validation
//!
//! This module contains comprehensive tests to validate that the AnalysisOutput
//! implementation strictly conforms to the ast-output-v1.json schema.

use super::*;
use chrono::Utc;
use serde_json;
use std::collections::HashMap;

#[cfg(test)]
// SAFETY: Test functions use unwrap() for assertion failures - this is the expected behavior for test code
#[allow(clippy::unwrap_used)]
#[allow(clippy::module_inception)] // Test module shares name with file for organization
mod tests {
    use super::*;

    /// Test that basic AnalysisOutput structure matches schema requirements
    #[test]
    fn test_analysis_output_basic_structure() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        // Check required top-level fields
        assert!(json.get("repository").is_some());
        assert!(json.get("analysis").is_some());
        assert!(json.get("metadata").is_some());

        // Check repository required fields
        let repository = json.get("repository").unwrap();
        assert!(repository.get("id").is_some());
        assert!(repository.get("url").is_some());
        assert!(repository.get("commit").is_some());
        assert!(repository.get("branch").is_some());

        // Check analysis required fields
        let analysis = json.get("analysis").unwrap();
        assert!(analysis.get("files").is_some());
        assert!(analysis.get("metrics").is_some());
        assert!(analysis.get("languages").is_some());
        assert!(analysis.get("embeddings").is_some());

        // Check metadata required fields
        let metadata = json.get("metadata").unwrap();
        assert!(metadata.get("analysis_id").is_some());
        assert!(metadata.get("version").is_some());
        assert!(metadata.get("timestamp").is_some());
        assert!(metadata.get("duration_ms").is_some());
    }

    /// Test repository ID pattern compliance
    #[test]
    fn test_repository_id_pattern() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let repo_id = json
            .get("repository")
            .unwrap()
            .get("id")
            .unwrap()
            .as_str()
            .unwrap();

        // Schema expects: ^repo_[a-zA-Z0-9]{16}$
        // Current implementation might not match this pattern
        if let Some(suffix) = repo_id.strip_prefix("repo_") {
            assert_eq!(
                suffix.len(),
                16,
                "Repository ID suffix should be 16 characters"
            );
            assert!(
                suffix.chars().all(|c| c.is_alphanumeric()),
                "Repository ID suffix should be alphanumeric"
            );
        } else {
            // This is a compliance issue - need to fix
            eprintln!("WARNING: Repository ID '{repo_id}' does not match schema pattern ^repo_[a-zA-Z0-9]{{16}}$");
        }
    }

    /// Test analysis ID pattern compliance
    #[test]
    fn test_analysis_id_pattern() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let analysis_id = json
            .get("metadata")
            .unwrap()
            .get("analysis_id")
            .unwrap()
            .as_str()
            .unwrap();

        // Schema expects: ^analysis_[a-zA-Z0-9]{16}$
        if let Some(suffix) = analysis_id.strip_prefix("analysis_") {
            assert_eq!(
                suffix.len(),
                16,
                "Analysis ID suffix should be 16 characters"
            );
            assert!(
                suffix.chars().all(|c| c.is_alphanumeric()),
                "Analysis ID suffix should be alphanumeric"
            );
        } else {
            // This is a compliance issue - need to fix
            eprintln!("WARNING: Analysis ID '{analysis_id}' does not match schema pattern ^analysis_[a-zA-Z0-9]{{16}}$");
        }
    }

    /// Test commit hash pattern compliance
    #[test]
    fn test_commit_hash_pattern() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let commit = json
            .get("repository")
            .unwrap()
            .get("commit")
            .unwrap()
            .as_str()
            .unwrap();

        // Schema expects: ^[a-f0-9]{40}$ (full SHA)
        if !commit.is_empty() {
            assert_eq!(commit.len(), 40, "Commit hash should be 40 characters");
            assert!(
                commit
                    .chars()
                    .all(|c| c.is_ascii_hexdigit() && (c.is_lowercase() || c.is_ascii_digit())),
                "Commit hash should be lowercase hexadecimal"
            );
        }
    }

    /// Test content hash pattern compliance
    #[test]
    fn test_content_hash_pattern() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            let content_hash = file.get("content_hash").unwrap().as_str().unwrap();

            // Schema expects: ^[a-f0-9]{64}$ (SHA-256)
            if !content_hash.is_empty() {
                assert_eq!(
                    content_hash.len(),
                    64,
                    "Content hash should be 64 characters (SHA-256)"
                );
                assert!(
                    content_hash
                        .chars()
                        .all(|c| c.is_ascii_hexdigit() && (c.is_lowercase() || c.is_ascii_digit())),
                    "Content hash should be lowercase hexadecimal"
                );
            }
        }
    }

    /// Test chunk ID pattern compliance
    #[test]
    fn test_chunk_id_pattern() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            if let Some(chunks) = file.get("chunks") {
                let chunks_array = chunks.as_array().unwrap();
                for chunk in chunks_array {
                    let chunk_id = chunk.get("chunk_id").unwrap().as_str().unwrap();

                    // Schema expects: ^chunk_[a-zA-Z0-9]{16}$
                    if let Some(suffix) = chunk_id.strip_prefix("chunk_") {
                        assert_eq!(suffix.len(), 16, "Chunk ID suffix should be 16 characters");
                        assert!(
                            suffix.chars().all(|c| c.is_alphanumeric()),
                            "Chunk ID suffix should be alphanumeric"
                        );
                    } else {
                        eprintln!("WARNING: Chunk ID '{chunk_id}' does not match schema pattern ^chunk_[a-zA-Z0-9]{{16}}$");
                    }
                }
            }
        }
    }

    /// Test symbol type enum compliance
    #[test]
    fn test_symbol_type_enum() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let valid_types = vec![
            "function",
            "method",
            "class",
            "interface",
            "variable",
            "constant",
            "type",
            "namespace",
        ];

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            if let Some(symbols) = file.get("symbols") {
                let symbols_array = symbols.as_array().unwrap();
                for symbol in symbols_array {
                    let symbol_type = symbol.get("type").unwrap().as_str().unwrap();

                    // Verify that symbol type matches schema enum values
                    assert!(!symbol_type.is_empty(), "Symbol type should not be empty");

                    // Check that the symbol type is one of the valid schema enum values
                    assert!(
                        valid_types.contains(&symbol_type),
                        "Symbol type '{}' must be one of the valid schema enum values: {:?}",
                        symbol_type,
                        valid_types
                    );
                }
            }
        }
    }

    /// Test chunk type enum compliance
    #[test]
    fn test_chunk_type_enum() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let valid_types = vec!["function", "class", "method", "block", "comment", "import"];

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            if let Some(chunks) = file.get("chunks") {
                let chunks_array = chunks.as_array().unwrap();
                for chunk in chunks_array {
                    let chunk_type = chunk.get("type").unwrap().as_str().unwrap();

                    // Verify that chunk type matches schema enum values
                    assert!(!chunk_type.is_empty(), "Chunk type should not be empty");

                    // Check that the chunk type is one of the valid schema enum values
                    assert!(
                        valid_types.contains(&chunk_type),
                        "Chunk type '{}' must be one of the valid schema enum values: {:?}",
                        chunk_type,
                        valid_types
                    );
                }
            }
        }
    }

    /// Test embedding model enum compliance
    #[test]
    fn test_embedding_model_enum() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let valid_models = vec![
            "gemini-embedding-001",
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large",
        ];

        let embeddings = json
            .get("analysis")
            .unwrap()
            .get("embeddings")
            .unwrap()
            .as_array()
            .unwrap();

        for embedding in embeddings {
            let model = embedding.get("model").unwrap().as_str().unwrap();

            assert!(
                valid_models.contains(&model),
                "Embedding model '{model}' not in schema enum: {valid_models:?}"
            );
        }
    }

    /// Test embedding vector dimensions compliance
    #[test]
    fn test_embedding_vector_dimensions() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let embeddings = json
            .get("analysis")
            .unwrap()
            .get("embeddings")
            .unwrap()
            .as_array()
            .unwrap();

        for embedding in embeddings {
            let vector = embedding.get("vector").unwrap().as_array().unwrap();
            let dimensions = vector.len();

            // Schema expects: minItems: 768, maxItems: 1536
            assert!(
                dimensions >= 768,
                "Embedding vector should have at least 768 dimensions, got {dimensions}"
            );
            assert!(
                dimensions <= 1536,
                "Embedding vector should have at most 1536 dimensions, got {dimensions}"
            );
        }
    }

    /// Test maintainability index range compliance
    #[test]
    fn test_maintainability_index_range() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            let metrics = file.get("metrics").unwrap();
            let maintainability_index = metrics
                .get("maintainability_index")
                .unwrap()
                .as_f64()
                .unwrap();

            // Schema expects: minimum: 0, maximum: 100
            assert!(
                maintainability_index >= 0.0,
                "Maintainability index should be >= 0"
            );
            assert!(
                maintainability_index <= 100.0,
                "Maintainability index should be <= 100"
            );
        }
    }

    /// Test comment ratio range compliance
    #[test]
    fn test_comment_ratio_range() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let files = json
            .get("analysis")
            .unwrap()
            .get("files")
            .unwrap()
            .as_array()
            .unwrap();

        for file in files {
            let metrics = file.get("metrics").unwrap();
            let comment_ratio = metrics.get("comment_ratio").unwrap().as_f64().unwrap();

            // Schema expects: minimum: 0, maximum: 1
            assert!(comment_ratio >= 0.0, "Comment ratio should be >= 0");
            assert!(comment_ratio <= 1.0, "Comment ratio should be <= 1");
        }
    }

    /// Test language percentage range compliance
    #[test]
    fn test_language_percentage_range() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        let languages = json
            .get("analysis")
            .unwrap()
            .get("languages")
            .unwrap()
            .get("languages")
            .unwrap()
            .as_object()
            .unwrap();

        for (_lang, stats) in languages {
            let percentage = stats.get("percentage").unwrap().as_f64().unwrap();

            // Schema expects: minimum: 0, maximum: 100
            assert!(percentage >= 0.0, "Language percentage should be >= 0");
            assert!(percentage <= 100.0, "Language percentage should be <= 100");
        }
    }

    /// Test pattern confidence range compliance
    #[test]
    fn test_pattern_confidence_range() {
        let analysis_output = create_test_analysis_output();
        let json = serde_json::to_value(&analysis_output).unwrap();

        if let Some(patterns) = json.get("analysis").unwrap().get("patterns") {
            let patterns_array = patterns.as_array().unwrap();

            for pattern in patterns_array {
                let confidence = pattern.get("confidence").unwrap().as_f64().unwrap();

                // Schema expects: minimum: 0, maximum: 1
                assert!(confidence >= 0.0, "Pattern confidence should be >= 0");
                assert!(confidence <= 1.0, "Pattern confidence should be <= 1");
            }
        }
    }

    /// Test JSON schema compliance with real serialization
    #[test]
    fn test_json_schema_compliance() {
        let analysis_output = create_test_analysis_output();

        // Serialize to JSON
        let json_result = serde_json::to_string_pretty(&analysis_output);
        assert!(
            json_result.is_ok(),
            "Should be able to serialize AnalysisOutput to JSON"
        );

        let json_string = json_result.unwrap();

        // Parse back from JSON
        let parsed_result: Result<serde_json::Value, _> = serde_json::from_str(&json_string);
        assert!(
            parsed_result.is_ok(),
            "Should be able to parse serialized JSON"
        );

        let parsed_json = parsed_result.unwrap();

        // Basic validation that required fields are present
        assert!(parsed_json.get("repository").is_some());
        assert!(parsed_json.get("analysis").is_some());
        assert!(parsed_json.get("metadata").is_some());

        println!("JSON schema compliance test passed");
    }

    /// Helper function to create test AnalysisOutput
    fn create_test_analysis_output() -> AnalysisOutput {
        AnalysisOutput {
            repository: Repository {
                id: "repo_1234567890123456".to_string(), // Fixed to match schema pattern
                url: "https://github.com/test/repo".to_string(),
                commit: "a1b2c3d4e5f6012345678901234567890abcdef1".to_string(),
                branch: "main".to_string(),
                size_bytes: 1024,
                clone_time_ms: 1000,
            },
            analysis: Analysis {
                files: vec![create_test_file_analysis()],
                metrics: create_test_repository_metrics(),
                languages: create_test_language_breakdown(),
                embeddings: vec![create_test_code_embedding()],
                patterns: vec![create_test_pre_detected_pattern()],
            },
            metadata: Metadata {
                analysis_id: "analysis_1234567890abcdef".to_string(), // Fixed to match schema pattern (16 chars)
                version: "1.0.0".to_string(),
                timestamp: Utc::now(),
                duration_ms: 5000,
                performance: create_test_performance_metrics(),
                warnings: vec![],
            },
        }
    }

    fn create_test_file_analysis() -> FileAnalysis {
        FileAnalysis {
            path: "src/main.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "a1b2c3d4e5f6012345678901234567890abcdef12345678901234567890abcde"
                .to_string(), // Fixed to 64 chars
            size_bytes: 256,
            ast: create_test_ast_node(),
            metrics: create_test_file_metrics(),
            chunks: vec![create_test_code_chunk()],
            symbols: vec![create_test_symbol()],
        }
    }

    fn create_test_ast_node() -> ASTNode {
        ASTNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position {
                    line: 0,
                    column: 0,
                    byte: 0,
                },
                end: Position {
                    line: 10,
                    column: 0,
                    byte: 256,
                },
            },
            children: vec![],
            properties: None,
            text: Some("fn main() { println!(\"Hello\"); }".to_string()),
        }
    }

    fn create_test_file_metrics() -> FileMetrics {
        FileMetrics {
            lines_of_code: 10,
            total_lines: 15,
            complexity: 2,
            maintainability_index: 85.5,
            function_count: 1,
            class_count: 0,
            comment_ratio: 0.2,
        }
    }

    fn create_test_repository_metrics() -> RepositoryMetrics {
        RepositoryMetrics {
            total_files: 1,
            total_lines: 15,
            total_complexity: 2,
            average_complexity: 2.0,
            maintainability_score: 85.5,
            technical_debt_minutes: 10,
            test_coverage_estimate: Some(0.8),
        }
    }

    fn create_test_language_breakdown() -> LanguageBreakdown {
        let mut languages = HashMap::new();
        languages.insert(
            "rust".to_string(),
            LanguageStats {
                lines: 15,
                files: 1,
                percentage: 100.0,
                bytes: 256,
            },
        );

        LanguageBreakdown {
            primary_language: "rust".to_string(),
            languages,
        }
    }

    fn create_test_code_chunk() -> CodeChunk {
        CodeChunk {
            chunk_id: "chunk_1234567890123456".to_string(), // Fixed to match schema pattern
            content: "fn main() { println!(\"Hello\"); }".to_string(),
            range: Range {
                start: Position {
                    line: 0,
                    column: 0,
                    byte: 0,
                },
                end: Position {
                    line: 0,
                    column: 32,
                    byte: 32,
                },
            },
            chunk_type: "function".to_string(),
            language: "rust".to_string(),
            context: Some(ChunkContext {
                parent_symbol: Some("main".to_string()),
                imports: vec![],
                dependencies: vec![],
            }),
        }
    }

    fn create_test_code_embedding() -> CodeEmbedding {
        CodeEmbedding {
            chunk_id: "chunk_1234567890123456".to_string(), // Fixed to match schema pattern
            vector: vec![0.1; 768], // 768 dimensions for gemini-embedding-001
            model: "gemini-embedding-001".to_string(),
            metadata: Some(EmbeddingMetadata {
                tokens_used: 10,
                created_at: Utc::now(),
            }),
        }
    }

    fn create_test_symbol() -> Symbol {
        Symbol {
            name: "main".to_string(),
            symbol_type: "function".to_string(),
            range: Range {
                start: Position {
                    line: 0,
                    column: 0,
                    byte: 0,
                },
                end: Position {
                    line: 0,
                    column: 32,
                    byte: 32,
                },
            },
            visibility: Some("public".to_string()),
            signature: Some("fn main()".to_string()),
            documentation: None,
        }
    }

    fn create_test_pre_detected_pattern() -> PreDetectedPattern {
        PreDetectedPattern {
            pattern_id: "test_pattern_123".to_string(),
            pattern_type: "design_pattern".to_string(),
            confidence: 0.85,
            location: PatternLocation {
                file_path: "src/main.rs".to_string(),
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 0,
                        column: 32,
                        byte: 32,
                    },
                },
            },
            description: Some("Test pattern".to_string()),
        }
    }

    fn create_test_performance_metrics() -> PerformanceMetrics {
        PerformanceMetrics {
            parsing_time_ms: 100,
            embedding_time_ms: 500,
            total_memory_mb: 64.0,
            files_per_second: Some(10.0),
            cache_hit_ratio: Some(0.8),
        }
    }
}
