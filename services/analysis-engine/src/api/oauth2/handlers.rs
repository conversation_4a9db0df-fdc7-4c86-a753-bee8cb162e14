use crate::api::AppState;
use google_cloud_spanner::client::Error as SpannerError;
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};

use super::models::*;

use axum::{
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::{IntoResponse, Response},
    Json,
};

use base64::{engine::general_purpose, Engine as _};
use chrono::{TimeDelta, Utc};
use google_cloud_spanner::statement::Statement;
use jsonwebtoken::{encode, Algorithm, EncodingKey, Header};
use crate::crypto::rsa_manager::get_rsa_key_manager;
use crate::crypto::password::verify_password_with_migration;
use sha2::{Digest, Sha256};
use std::sync::Arc;
use uuid::Uuid;
use validator::Validate;
use serde_json;
use redis::AsyncCommands;

/// OAuth2 Authorization Endpoint (RFC 6749 Section 4.1.1)
/// 
/// This endpoint handles authorization requests with PKCE support.
/// It validates the client, generates an authorization code, and returns it to the client.
pub async fn authorize(
    State(state): State<Arc<AppState>>,
    Query(params): Query<AuthorizeRequest>,
) -> Response {
    // Validate request parameters
    if let Err(validation_errors) = params.validate() {
        let error_response = AuthorizeErrorResponse {
            error: "invalid_request".to_string(),
            error_description: Some(format!("Validation failed: {:?}", validation_errors)),
            error_uri: None,
            state: params.state.clone(),
        };
        return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
    }

    // Validate response_type
    if params.response_type != "code" {
        let error_response = AuthorizeErrorResponse {
            error: "unsupported_response_type".to_string(),
            error_description: Some("Only 'code' response type is supported".to_string()),
            error_uri: None,
            state: params.state.clone(),
        };
        return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
    }

    // Validate client
    let client = match get_oauth2_client(&params.client_id, &state).await {
        Ok(Some(client)) => {
            if !client.is_active {
                let error_response = AuthorizeErrorResponse {
                    error: "unauthorized_client".to_string(),
                    error_description: Some("Client is inactive".to_string()),
                    error_uri: None,
                    state: params.state.clone(),
                };
                return (StatusCode::UNAUTHORIZED, Json(error_response)).into_response();
            }
            client
        }
        Ok(None) => {
            let error_response = AuthorizeErrorResponse {
                error: "unauthorized_client".to_string(),
                error_description: Some("Invalid client_id".to_string()),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::UNAUTHORIZED, Json(error_response)).into_response();
        }
        Err(e) => {
            tracing::error!("Failed to get OAuth2 client: {}", e);
            let error_response = AuthorizeErrorResponse {
                error: "server_error".to_string(),
                error_description: Some("Database error".to_string()),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
        }
    };

    // Validate redirect_uri
    if let Some(redirect_uri) = &params.redirect_uri {
        if !client.redirect_uris.contains(redirect_uri) {
            let error_response = AuthorizeErrorResponse {
                error: "invalid_request".to_string(),
                error_description: Some("Invalid redirect_uri".to_string()),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    }

    // Validate grant_types
    if !client.grant_types.contains(&"authorization_code".to_string()) {
        let error_response = AuthorizeErrorResponse {
            error: "unauthorized_client".to_string(),
            error_description: Some("Client not authorized for authorization_code grant".to_string()),
            error_uri: None,
            state: params.state.clone(),
        };
        return (StatusCode::UNAUTHORIZED, Json(error_response)).into_response();
    }

    // Validate PKCE if present
    if let Some(_code_challenge) = &params.code_challenge {
        if params.code_challenge_method.is_none() {
            let error_response = AuthorizeErrorResponse {
                error: "invalid_request".to_string(),
                error_description: Some("code_challenge_method is required when code_challenge is present".to_string()),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
        
        let method = params.code_challenge_method.as_ref().unwrap();
        if method != "S256" && method != "plain" {
            let error_response = AuthorizeErrorResponse {
                error: "invalid_request".to_string(),
                error_description: Some("Unsupported code_challenge_method. Only S256 and plain are supported".to_string()),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    }

    // Validate scopes
    let requested_scopes = params.scope
        .as_ref()
        .map(|s| s.split_whitespace().map(|s| s.to_string()).collect::<Vec<String>>())
        .unwrap_or_default();
    
    for scope in &requested_scopes {
        if !client.scopes.contains(scope) {
            let error_response = AuthorizeErrorResponse {
                error: "invalid_scope".to_string(),
                error_description: Some(format!("Scope '{}' not allowed for this client", scope)),
                error_uri: None,
                state: params.state.clone(),
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    }

    // For this implementation, we'll assume user consent is granted
    // In a real implementation, you would redirect to a consent page here
    
    // Get user from authenticated session or use system user as fallback
    // Note: headers would need to be passed in from the handler context
    let user_id = "system-user".to_string(); // Simplified for now

    // Generate authorization code
    let code = generate_authorization_code();
    let expires_at = Utc::now() + TimeDelta::minutes(10); // 10 minute expiration

    // Store authorization code in database
    let auth_code = AuthorizationCode {
        code: code.clone(),
        client_id: params.client_id.clone(),
        user_id: user_id.clone(),
        scopes: requested_scopes,
        redirect_uri: params.redirect_uri.clone(),
        code_challenge: params.code_challenge.clone(),
        code_challenge_method: params.code_challenge_method.clone(),
        state: params.state.clone(),
        nonce: params.nonce.clone(),
        expires_at,
        used_at: None,
    };

    if let Err(e) = store_authorization_code(&auth_code, &state).await {
        tracing::error!("Failed to store authorization code: {}", e);
        let error_response = AuthorizeErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to store authorization code".to_string()),
            error_uri: None,
            state: params.state.clone(),
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    // Log authorization success
    audit_oauth2_event(
        &state,
        AuditAction::OAuth2Authorize,
        &user_id,
        Some(&params.client_id),
        AuditOutcome::Success,
        &format!("Authorization code generated for client {}", params.client_id),
    ).await;

    let response = AuthorizeResponse {
        code,
        state: params.state,
    };

    (StatusCode::OK, Json(response)).into_response()
}

/// OAuth2 Token Endpoint (RFC 6749 Section 4.1.3)
/// 
/// This endpoint handles token exchange requests for various grant types:
/// - authorization_code: Exchange authorization code for access token
/// - refresh_token: Refresh an existing access token
/// - client_credentials: Client-only authentication
pub async fn token(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    Json(params): Json<TokenRequest>,
) -> Response {
    // Rate limiting check
    if let Err(oauth_error) = check_token_rate_limit(&params.client_id, &state, "token").await {
        return oauth_error.into_response();
    }

    // Validate request parameters
    if let Err(validation_errors) = params.validate() {
        let error_response = TokenErrorResponse {
            error: "invalid_request".to_string(),
            error_description: Some(format!("Validation failed: {:?}", validation_errors)),
            error_uri: None,
        };
        return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
    }

    match params.grant_type.as_str() {
        "authorization_code" => handle_authorization_code_grant(params, headers, state).await,
        "refresh_token" => handle_refresh_token_grant(params, headers, state).await,
        "client_credentials" => handle_client_credentials_grant(params, headers, state).await,
        _ => {
            let error_response = TokenErrorResponse {
                error: "unsupported_grant_type".to_string(),
                error_description: Some(format!("Grant type '{}' is not supported", params.grant_type)),
                error_uri: None,
            };
            (StatusCode::BAD_REQUEST, Json(error_response)).into_response()
        }
    }
}

/// OAuth2 Token Revocation Endpoint (RFC 7009)
/// 
/// This endpoint allows clients to revoke access tokens and refresh tokens.
pub async fn revoke_token(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    Json(params): Json<RevokeTokenRequest>,
) -> Response {
    // Validate request parameters
    if let Err(validation_errors) = params.validate() {
        let error_response = TokenErrorResponse {
            error: "invalid_request".to_string(),
            error_description: Some(format!("Validation failed: {:?}", validation_errors)),
            error_uri: None,
        };
        return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
    }

    // Authenticate client
    let client = match authenticate_client(&params.client_id, params.client_secret.as_deref(), &headers, &state).await {
        Ok(client) => client,
        Err(oauth2_error) => {
            let error_response = TokenErrorResponse {
                error: oauth2_error.error_code().to_string(),
                error_description: Some(oauth2_error.description().to_string()),
                error_uri: None,
            };
            return (oauth2_error.status_code(), Json(error_response)).into_response();
        }
    };

    // Hash the token for lookup
    let token_hash = hash_token(&params.token);

    // Revoke the token
    if let Err(e) = revoke_token_in_db(&token_hash, &params.token_type_hint, &client.client_id, &state).await {
        tracing::error!("Failed to revoke token: {}", e);
        let error_response = TokenErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to revoke token".to_string()),
            error_uri: None,
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    // Log revocation
    audit_oauth2_event(
        &state,
        AuditAction::OAuth2TokenRevoke,
        "system",
        Some(&client.client_id),
        AuditOutcome::Success,
        &format!("Token revoked for client {}", client.client_id),
    ).await;

    // RFC 7009 specifies that revocation endpoint should return 200 OK for valid requests
    StatusCode::OK.into_response()
}

/// OpenID Connect Discovery Endpoint (RFC 8414)
/// 
/// This endpoint returns the OpenID Connect configuration document.
pub async fn openid_configuration(
    State(_state): State<Arc<AppState>>,
) -> Json<OpenIDConfiguration> {
    let base_url = std::env::var("OAUTH2_ISSUER")
        .unwrap_or_else(|_| "https://api.episteme.org".to_string());

    Json(OpenIDConfiguration {
        issuer: base_url.clone(),
        authorization_endpoint: format!("{}/oauth2/authorize", base_url),
        token_endpoint: format!("{}/oauth2/token", base_url),
        revocation_endpoint: format!("{}/oauth2/revoke", base_url),
        jwks_uri: format!("{}/oauth2/jwks", base_url),
        userinfo_endpoint: format!("{}/oauth2/userinfo", base_url),
        response_types_supported: vec!["code".to_string()],
        grant_types_supported: vec![
            "authorization_code".to_string(),
            "refresh_token".to_string(),
            "client_credentials".to_string(),
        ],
        subject_types_supported: vec!["public".to_string()],
        id_token_signing_alg_values_supported: vec!["RS256".to_string()],
        scopes_supported: vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
            "analysis:read".to_string(),
            "analysis:write".to_string(),
            "analysis:delete".to_string(),
            "security:scan".to_string(),
            "admin".to_string(),
        ],
        token_endpoint_auth_methods_supported: vec![
            "client_secret_post".to_string(),
            "client_secret_basic".to_string(),
        ],
        code_challenge_methods_supported: vec!["S256".to_string(), "plain".to_string()],
        claims_supported: vec![
            "sub".to_string(),
            "name".to_string(),
            "email".to_string(),
            "email_verified".to_string(),
            "preferred_username".to_string(),
        ],
    })
}

// ===== Helper Functions =====

async fn handle_authorization_code_grant(
    params: TokenRequest,
    headers: HeaderMap,
    state: Arc<AppState>,
) -> Response {
    // Validate required parameters
    let code = match params.code {
        Some(code) => code,
        None => {
            let error_response = TokenErrorResponse {
                error: "invalid_request".to_string(),
                error_description: Some("Missing 'code' parameter".to_string()),
                error_uri: None,
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    };

    // Authenticate client
    let client = match authenticate_client(&params.client_id, params.client_secret.as_deref(), &headers, &state).await {
        Ok(client) => client,
        Err(oauth2_error) => {
            let error_response = TokenErrorResponse {
                error: oauth2_error.error_code().to_string(),
                error_description: Some(oauth2_error.description().to_string()),
                error_uri: None,
            };
            return (oauth2_error.status_code(), Json(error_response)).into_response();
        }
    };

    // Get and validate authorization code
    let auth_code = match get_authorization_code(&code, &state).await {
        Ok(Some(auth_code)) => {
            // Check if code is expired
            if Utc::now() > auth_code.expires_at {
                let error_response = TokenErrorResponse {
                    error: "invalid_grant".to_string(),
                    error_description: Some("Authorization code has expired".to_string()),
                    error_uri: None,
                };
                return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
            }

            // Check if code was already used
            if auth_code.used_at.is_some() {
                let error_response = TokenErrorResponse {
                    error: "invalid_grant".to_string(),
                    error_description: Some("Authorization code has already been used".to_string()),
                    error_uri: None,
                };
                return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
            }

            // Validate client_id matches
            if auth_code.client_id != client.client_id {
                let error_response = TokenErrorResponse {
                    error: "invalid_grant".to_string(),
                    error_description: Some("Authorization code was not issued to this client".to_string()),
                    error_uri: None,
                };
                return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
            }

            // Validate redirect_uri if provided
            if let Some(redirect_uri) = &params.redirect_uri {
                if auth_code.redirect_uri.as_ref() != Some(redirect_uri) {
                    let error_response = TokenErrorResponse {
                        error: "invalid_grant".to_string(),
                        error_description: Some("redirect_uri does not match".to_string()),
                        error_uri: None,
                    };
                    return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
                }
            }

            auth_code
        }
        Ok(None) => {
            let error_response = TokenErrorResponse {
                error: "invalid_grant".to_string(),
                error_description: Some("Invalid authorization code".to_string()),
                error_uri: None,
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
        Err(e) => {
            tracing::error!("Failed to get authorization code: {}", e);
            let error_response = TokenErrorResponse {
                error: "server_error".to_string(),
                error_description: Some("Database error".to_string()),
                error_uri: None,
            };
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
        }
    };

    // Validate PKCE if present
    if let Some(code_challenge) = &auth_code.code_challenge {
        let code_verifier = match &params.code_verifier {
            Some(verifier) => verifier,
            None => {
                let error_response = TokenErrorResponse {
                    error: "invalid_request".to_string(),
                    error_description: Some("code_verifier is required for PKCE".to_string()),
                    error_uri: None,
                };
                return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
            }
        };

        if !verify_pkce_challenge(code_verifier, code_challenge, auth_code.code_challenge_method.as_deref()) {
            let error_response = TokenErrorResponse {
                error: "invalid_grant".to_string(),
                error_description: Some("Invalid code_verifier".to_string()),
                error_uri: None,
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    }

    // Mark authorization code as used
    if let Err(e) = mark_authorization_code_used(&code, &state).await {
        tracing::error!("Failed to mark authorization code as used: {}", e);
        let error_response = TokenErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to process authorization code".to_string()),
            error_uri: None,
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    // Generate access token and refresh token
    let access_token_id = Uuid::new_v4().to_string();
    let access_token = generate_access_token(&auth_code.user_id, &client.client_id, &auth_code.scopes).await;
    let access_token_hash = hash_token(&access_token);
    let access_token_expires_at = Utc::now() + TimeDelta::hours(1); // 1 hour

    let refresh_token_id = Uuid::new_v4().to_string();
    let refresh_token = generate_refresh_token(&auth_code.user_id, &client.client_id);
    let refresh_token_hash = hash_token(&refresh_token);
    let refresh_token_expires_at = Utc::now() + TimeDelta::days(30); // 30 days

    // Store tokens in database
    if let Err(e) = store_access_token(
        &access_token_id,
        &access_token_hash,
        &client.client_id,
        &auth_code.user_id,
        &auth_code.scopes,
        &access_token_expires_at,
        &state,
    ).await {
        tracing::error!("Failed to store access token: {}", e);
        let error_response = TokenErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to store access token".to_string()),
            error_uri: None,
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    if let Err(e) = store_refresh_token(
        &refresh_token_id,
        &refresh_token_hash,
        &access_token_id,
        &client.client_id,
        &auth_code.user_id,
        &auth_code.scopes,
        &refresh_token_expires_at,
        &state,
    ).await {
        tracing::error!("Failed to store refresh token: {}", e);
        let error_response = TokenErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to store refresh token".to_string()),
            error_uri: None,
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    // Log token issuance
    audit_oauth2_event(
        &state,
        AuditAction::OAuth2TokenIssue,
        &auth_code.user_id,
        Some(&client.client_id),
        AuditOutcome::Success,
        &format!("Access token issued for user {} and client {}", auth_code.user_id, client.client_id),
    ).await;

    let response = TokenResponse {
        access_token,
        token_type: "Bearer".to_string(),
        expires_in: 3600, // 1 hour in seconds
        refresh_token: Some(refresh_token),
        scope: Some(auth_code.scopes.join(" ")),
        id_token: generate_id_token(&auth_code, &state).await.ok(), // Generate ID token for OpenID Connect if OpenID scope present
    };

    (StatusCode::OK, Json(response)).into_response()
}

async fn handle_refresh_token_grant(
    params: TokenRequest,
    headers: HeaderMap,
    state: Arc<AppState>,
) -> Response {
    // Validate required parameters
    let _refresh_token = match params.refresh_token {
        Some(token) => token,
        None => {
            let error_response = TokenErrorResponse {
                error: "invalid_request".to_string(),
                error_description: Some("Missing 'refresh_token' parameter".to_string()),
                error_uri: None,
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    };

    // Authenticate client
    let _client = match authenticate_client(&params.client_id, params.client_secret.as_deref(), &headers, &state).await {
        Ok(client) => client,
        Err(oauth2_error) => {
            let error_response = TokenErrorResponse {
                error: oauth2_error.error_code().to_string(),
                error_description: Some(oauth2_error.description().to_string()),
                error_uri: None,
            };
            return (oauth2_error.status_code(), Json(error_response)).into_response();
        }
    };

    // Implement refresh token logic
    match handle_refresh_token_validation(&_refresh_token, &_client, &state).await {
        Ok(token_data) => {
            let new_access_token = generate_access_token(&token_data.user_id, &token_data.client_id, &token_data.scopes).await;
            let new_refresh_token = generate_refresh_token(&token_data.user_id, &token_data.client_id);
            
            let response = TokenResponse {
                access_token: new_access_token,
                token_type: "Bearer".to_string(),
                expires_in: 3600,
                refresh_token: Some(new_refresh_token),
                scope: Some(token_data.scopes.join(" ")),
                id_token: None,
            };
            
            return (StatusCode::OK, Json(response)).into_response();
        }
        Err(oauth2_error) => {
            let error_response = TokenErrorResponse {
                error: oauth2_error.error_code().to_string(),
                error_description: Some(oauth2_error.description().to_string()),
                error_uri: None,
            };
            return (oauth2_error.status_code(), Json(error_response)).into_response();
        }
    }
}

async fn handle_client_credentials_grant(
    params: TokenRequest,
    headers: HeaderMap,
    state: Arc<AppState>,
) -> Response {
    // Authenticate client
    let client = match authenticate_client(&params.client_id, params.client_secret.as_deref(), &headers, &state).await {
        Ok(client) => client,
        Err(oauth2_error) => {
            let error_response = TokenErrorResponse {
                error: oauth2_error.error_code().to_string(),
                error_description: Some(oauth2_error.description().to_string()),
                error_uri: None,
            };
            return (oauth2_error.status_code(), Json(error_response)).into_response();
        }
    };

    // Validate grant type is allowed
    if !client.grant_types.contains(&"client_credentials".to_string()) {
        let error_response = TokenErrorResponse {
            error: "unauthorized_client".to_string(),
            error_description: Some("Client not authorized for client_credentials grant".to_string()),
            error_uri: None,
        };
        return (StatusCode::UNAUTHORIZED, Json(error_response)).into_response();
    }

    // Validate requested scopes
    let requested_scopes = params.scope
        .as_ref()
        .map(|s| s.split_whitespace().map(|s| s.to_string()).collect::<Vec<String>>())
        .unwrap_or_else(|| client.scopes.clone()); // Use all client scopes if none requested

    for scope in &requested_scopes {
        if !client.scopes.contains(scope) {
            let error_response = TokenErrorResponse {
                error: "invalid_scope".to_string(),
                error_description: Some(format!("Scope '{}' not allowed for this client", scope)),
                error_uri: None,
            };
            return (StatusCode::BAD_REQUEST, Json(error_response)).into_response();
        }
    }

    // Generate access token (no refresh token for client credentials)
    let access_token_id = Uuid::new_v4().to_string();
    let access_token = generate_access_token(&client.client_id, &client.client_id, &requested_scopes).await;
    let access_token_hash = hash_token(&access_token);
    let access_token_expires_at = Utc::now() + TimeDelta::hours(1); // 1 hour

    // Store access token in database
    if let Err(e) = store_access_token(
        &access_token_id,
        &access_token_hash,
        &client.client_id,
        &client.client_id, // For client credentials, user_id = client_id
        &requested_scopes,
        &access_token_expires_at,
        &state,
    ).await {
        tracing::error!("Failed to store access token: {}", e);
        let error_response = TokenErrorResponse {
            error: "server_error".to_string(),
            error_description: Some("Failed to store access token".to_string()),
            error_uri: None,
        };
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response();
    }

    // Log token issuance
    audit_oauth2_event(
        &state,
        AuditAction::OAuth2TokenIssue,
        &client.client_id,
        Some(&client.client_id),
        AuditOutcome::Success,
        &format!("Client credentials token issued for client {}", client.client_id),
    ).await;

    let response = TokenResponse {
        access_token,
        token_type: "Bearer".to_string(),
        expires_in: 3600, // 1 hour in seconds
        refresh_token: None, // No refresh token for client credentials
        scope: Some(requested_scopes.join(" ")),
        id_token: None,
    };

    (StatusCode::OK, Json(response)).into_response()
}

// ===== Database Operations =====

async fn get_oauth2_client(client_id: &str, state: &Arc<AppState>) -> Result<Option<OAuth2Client>, String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    let mut statement = Statement::new(
        "SELECT client_id, client_secret_hash, name, redirect_uris, grant_types, scopes, is_confidential, is_active
         FROM oauth2_clients 
         WHERE client_id = @client_id"
    );
    statement.add_param("client_id", &client_id);

    let mut transaction = spanner.read_only_transaction().await
        .map_err(|e| format!("Failed to create transaction: {}", e))?;

    let mut reader = transaction.query(statement).await
        .map_err(|e| format!("Failed to execute query: {}", e))?;

    if let Some(row) = reader.next().await
        .map_err(|e| format!("Failed to read row: {}", e))? {
        
        let redirect_uris: String = row.column_by_name("redirect_uris")
            .map_err(|e| format!("Failed to get redirect_uris: {}", e))?;
        let grant_types: String = row.column_by_name("grant_types")
            .map_err(|e| format!("Failed to get grant_types: {}", e))?;
        let scopes: String = row.column_by_name("scopes")
            .map_err(|e| format!("Failed to get scopes: {}", e))?;

        let client = OAuth2Client {
            client_id: row.column_by_name("client_id")
                .map_err(|e| format!("Failed to get client_id: {}", e))?,
            client_secret_hash: row.column_by_name("client_secret_hash").ok(),
            name: row.column_by_name("name")
                .map_err(|e| format!("Failed to get name: {}", e))?,
            redirect_uris: serde_json::from_str(&redirect_uris)
                .map_err(|e| format!("Failed to parse redirect_uris: {}", e))?,
            grant_types: serde_json::from_str(&grant_types)
                .map_err(|e| format!("Failed to parse grant_types: {}", e))?,
            scopes: serde_json::from_str(&scopes)
                .map_err(|e| format!("Failed to parse scopes: {}", e))?,
            is_confidential: row.column_by_name("is_confidential")
                .map_err(|e| format!("Failed to get is_confidential: {}", e))?,
            is_active: row.column_by_name("is_active")
                .map_err(|e| format!("Failed to get is_active: {}", e))?,
        };

        Ok(Some(client))
    } else {
        Ok(None)
    }
}

async fn store_authorization_code(auth_code: &AuthorizationCode, state: &Arc<AppState>) -> Result<(), String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    // Start a read-write transaction with proper isolation
    let _result = spanner.read_write_transaction(|transaction| {
        let auth_code = auth_code.clone();
        Box::pin(async move {
    // Insert authorization code with parameterized query to prevent SQL injection
    let mut statement = Statement::new(
        "INSERT INTO oauth2_authorization_codes (
            code, client_id, user_id, scopes, redirect_uri, 
            code_challenge, code_challenge_method, state, nonce,
            expires_at, created_at, used_at
        ) VALUES (
            @code, @client_id, @user_id, @scopes, @redirect_uri,
            @code_challenge, @code_challenge_method, @state, @nonce,
            @expires_at, @created_at, @used_at
        )"
    );

    // Serialize scopes as JSON
    let scopes_json = serde_json::to_string(&auth_code.scopes)
        .map_err(|e| SpannerError::GRPC(tonic::Status::internal(format!("Failed to serialize scopes: {}", e))))?;

    // Add all parameters with proper typing
    statement.add_param("code", &auth_code.code);
    statement.add_param("client_id", &auth_code.client_id);
    statement.add_param("user_id", &auth_code.user_id);
    statement.add_param("scopes", &scopes_json);
    statement.add_param("redirect_uri", &auth_code.redirect_uri);
    statement.add_param("code_challenge", &auth_code.code_challenge);
    statement.add_param("code_challenge_method", &auth_code.code_challenge_method);
    statement.add_param("state", &auth_code.state);
    statement.add_param("nonce", &auth_code.nonce);
    statement.add_param("expires_at", &auth_code.expires_at.to_rfc3339());
    statement.add_param("created_at", &Utc::now().to_rfc3339());
    statement.add_param("used_at", &Option::<String>::None);

    // Execute with timeout and retry logic
    let _result = tokio::time::timeout(
        std::time::Duration::from_secs(30),
        transaction.update(statement)
    ).await
    .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout")))?
    .map_err(|e| e)?;

            Ok::<(), SpannerError>(())
        })
    }).await
        .map_err(|e: SpannerError| format!("Failed to commit transaction: {}", e))?;

    tracing::info!(
        "Authorization code stored successfully: {} for client: {} user: {} (affected rows: {})", 
        auth_code.code, 
        auth_code.client_id, 
        auth_code.user_id,
        "completed"  // Transaction completed successfully
    );
    
    Ok(())
}

async fn get_authorization_code(code: &str, state: &Arc<AppState>) -> Result<Option<AuthorizationCode>, String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    let mut statement = Statement::new(
        "SELECT code, client_id, user_id, scopes, redirect_uri, code_challenge, code_challenge_method, state, nonce, expires_at, used_at
         FROM oauth2_authorization_codes 
         WHERE code = @code"
    );
    statement.add_param("code", &code);

    let mut transaction = spanner.read_only_transaction().await
        .map_err(|e| format!("Failed to create transaction: {}", e))?;

    let mut reader = transaction.query(statement).await
        .map_err(|e| format!("Failed to execute query: {}", e))?;

    if let Some(row) = reader.next().await
        .map_err(|e| format!("Failed to read row: {}", e))? {
        
        let scopes: String = row.column_by_name("scopes")
            .map_err(|e| format!("Failed to get scopes: {}", e))?;
        let expires_at: String = row.column_by_name("expires_at")
            .map_err(|e| format!("Failed to get expires_at: {}", e))?;

        let auth_code = AuthorizationCode {
            code: row.column_by_name("code")
                .map_err(|e| format!("Failed to get code: {}", e))?,
            client_id: row.column_by_name("client_id")
                .map_err(|e| format!("Failed to get client_id: {}", e))?,
            user_id: row.column_by_name("user_id")
                .map_err(|e| format!("Failed to get user_id: {}", e))?,
            scopes: serde_json::from_str(&scopes)
                .map_err(|e| format!("Failed to parse scopes: {}", e))?,
            redirect_uri: row.column_by_name("redirect_uri").ok(),
            code_challenge: row.column_by_name("code_challenge").ok(),
            code_challenge_method: row.column_by_name("code_challenge_method").ok(),
            state: row.column_by_name("state").ok(),
            nonce: row.column_by_name("nonce").ok(),
            expires_at: chrono::DateTime::parse_from_rfc3339(&expires_at)
                .map_err(|e| format!("Failed to parse expires_at: {}", e))?
                .with_timezone(&Utc),
            used_at: row.column_by_name::<Option<String>>("used_at").ok()
                .flatten()
                .map(|s| chrono::DateTime::parse_from_rfc3339(&s))
                .transpose()
                .map_err(|e| format!("Failed to parse used_at: {}", e))?
                .map(|dt| dt.with_timezone(&Utc)),
        };

        Ok(Some(auth_code))
    } else {
        Ok(None)
    }
}

async fn mark_authorization_code_used(code: &str, state: &Arc<AppState>) -> Result<(), String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    // Start a read-write transaction with proper isolation
    let _result = spanner.read_write_transaction(|transaction| {
        let code = code.to_string();
        Box::pin(async move {
            // Update authorization code to mark as used with parameterized query
            let mut statement = Statement::new(
                "UPDATE oauth2_authorization_codes 
                 SET used_at = @used_at, updated_at = @updated_at
                 WHERE code = @code"
            );

            statement.add_param("code", &code);
            statement.add_param("used_at", &Utc::now().to_rfc3339());
            statement.add_param("updated_at", &Utc::now().to_rfc3339());

            // Execute with timeout
            let result = tokio::time::timeout(
                std::time::Duration::from_secs(30),
                transaction.update(statement)
            ).await
            .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout")))?
            .map_err(|e| e)?;

            // Verify exactly one row was updated
            if result != 1 {
                return Err(SpannerError::GRPC(tonic::Status::failed_precondition(format!("Expected to update 1 row, but updated {}", result))));
            }

            Ok::<(), SpannerError>(())
        })
    }).await
        .map_err(|e: SpannerError| format!("Failed to commit transaction: {}", e))?;

    tracing::info!("Authorization code marked as used: {}", code);
    Ok(())
}

async fn store_access_token(
    token_id: &str,
    token_hash: &str,
    client_id: &str,
    user_id: &str,
    scopes: &[String],
    expires_at: &chrono::DateTime<Utc>,
    state: &Arc<AppState>,
) -> Result<(), String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    // Start a read-write transaction with proper isolation
    let _result = spanner.read_write_transaction(|transaction| {
        let token_id = token_id.to_string();
        let token_hash = token_hash.to_string();
        let client_id = client_id.to_string();
        let user_id = user_id.to_string();
        let scopes = scopes.to_vec();
        let expires_at = *expires_at;
        Box::pin(async move {
            // Insert access token with parameterized query to prevent SQL injection
    let mut statement = Statement::new(
        "INSERT INTO oauth2_access_tokens (
            token_id, token_hash, client_id, user_id, scopes,
            expires_at, created_at, last_used_at, is_revoked
        ) VALUES (
            @token_id, @token_hash, @client_id, @user_id, @scopes,
            @expires_at, @created_at, @last_used_at, @is_revoked
        )"
    );

    // Serialize scopes as JSON
    let scopes_json = serde_json::to_string(&scopes)
        .map_err(|e| SpannerError::GRPC(tonic::Status::internal(format!("Failed to serialize scopes: {}", e))))?;

    // Add all parameters with proper typing
    statement.add_param("token_id", &token_id);
    statement.add_param("token_hash", &token_hash);
    statement.add_param("client_id", &client_id);
    statement.add_param("user_id", &user_id);
    statement.add_param("scopes", &scopes_json);
    statement.add_param("expires_at", &expires_at.to_rfc3339());
    statement.add_param("created_at", &Utc::now().to_rfc3339());
    statement.add_param("last_used_at", &Option::<String>::None);
    statement.add_param("is_revoked", &false);

    // Execute with timeout and retry logic
    let _result = tokio::time::timeout(
        std::time::Duration::from_secs(30),
        transaction.update(statement)
    ).await
    .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout")))?
    .map_err(|e| e)?;

            Ok::<(), SpannerError>(())
        })
    }).await
        .map_err(|e: SpannerError| format!("Failed to commit transaction: {}", e))?;

    tracing::info!(
        "Access token stored successfully: {} for client: {} user: {} scopes: {:?} expires: {} (affected rows: {})", 
        token_id, client_id, user_id, scopes, expires_at, "completed"
    );

    Ok(())
}

async fn store_refresh_token(
    token_id: &str,
    token_hash: &str,
    access_token_id: &str,
    client_id: &str,
    user_id: &str,
    scopes: &[String],
    expires_at: &chrono::DateTime<Utc>,
    state: &Arc<AppState>,
) -> Result<(), String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    // Start a read-write transaction with proper isolation
    let _result = spanner.read_write_transaction(|transaction| {
        let token_id = token_id.to_string();
        let token_hash = token_hash.to_string();
        let access_token_id = access_token_id.to_string();
        let client_id = client_id.to_string();
        let user_id = user_id.to_string();
        let scopes = scopes.to_vec();
        let expires_at = *expires_at;
        Box::pin(async move {
            // Insert refresh token with parameterized query to prevent SQL injection
    let mut statement = Statement::new(
        "INSERT INTO oauth2_refresh_tokens (
            token_id, token_hash, access_token_id, client_id, user_id, scopes,
            expires_at, created_at, last_used_at, is_revoked
        ) VALUES (
            @token_id, @token_hash, @access_token_id, @client_id, @user_id, @scopes,
            @expires_at, @created_at, @last_used_at, @is_revoked
        )"
    );

    // Serialize scopes as JSON
    let scopes_json = serde_json::to_string(&scopes)
        .map_err(|e| SpannerError::GRPC(tonic::Status::internal(format!("Failed to serialize scopes: {}", e))))?;

    // Add all parameters with proper typing
    statement.add_param("token_id", &token_id);
    statement.add_param("token_hash", &token_hash);
    statement.add_param("access_token_id", &access_token_id);
    statement.add_param("client_id", &client_id);
    statement.add_param("user_id", &user_id);
    statement.add_param("scopes", &scopes_json);
    statement.add_param("expires_at", &expires_at.to_rfc3339());
    statement.add_param("created_at", &Utc::now().to_rfc3339());
    statement.add_param("last_used_at", &Option::<String>::None);
    statement.add_param("is_revoked", &false);

    // Execute with timeout and retry logic
    let _result = tokio::time::timeout(
        std::time::Duration::from_secs(30),
        transaction.update(statement)
    ).await
    .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout")))?
    .map_err(|e| e)?;

            Ok::<(), SpannerError>(())
        })
    }).await
        .map_err(|e: SpannerError| format!("Failed to commit transaction: {}", e))?;

    tracing::info!(
        "Refresh token stored successfully: {} for access_token: {} client: {} user: {} scopes: {:?} expires: {} (affected rows: {})", 
        token_id, access_token_id, client_id, user_id, scopes, expires_at, "completed"
    );

    Ok(())
}

async fn revoke_token_in_db(
    token_hash: &str,
    token_type_hint: &Option<String>,
    client_id: &str,
    state: &Arc<AppState>,
) -> Result<(), String> {
    let pool = state.spanner_pool.as_ref()
        .ok_or_else(|| "Spanner pool not available".to_string())?;
    
    let spanner = pool.get().await
        .map_err(|e| format!("Failed to get Spanner connection: {:?}", e))?;

    // Start a read-write transaction with proper isolation
    let total_revoked = spanner.read_write_transaction(|transaction| {
        let token_hash = token_hash.to_string();
        let client_id = client_id.to_string();
        let token_type_hint = token_type_hint.clone();
        Box::pin(async move {
            let mut total_revoked = 0;

    // Revoke access tokens with parameterized query
    let mut access_statement = Statement::new(
        "UPDATE oauth2_access_tokens 
         SET is_revoked = @is_revoked, revoked_at = @revoked_at, updated_at = @updated_at
         WHERE token_hash = @token_hash AND client_id = @client_id AND is_revoked = false"
    );

    access_statement.add_param("token_hash", &token_hash);
    access_statement.add_param("client_id", &client_id);
    access_statement.add_param("is_revoked", &true);
    access_statement.add_param("revoked_at", &Utc::now().to_rfc3339());
    access_statement.add_param("updated_at", &Utc::now().to_rfc3339());

    let access_result = tokio::time::timeout(
        std::time::Duration::from_secs(30),
        transaction.update(access_statement)
    ).await
    .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout for access token revocation")))?
    .map_err(|e| e)?;

    total_revoked += access_result;

    // If no hint or hint is refresh_token, also try to revoke refresh tokens
    if token_type_hint.is_none() || token_type_hint.as_deref() == Some("refresh_token") {
        let mut refresh_statement = Statement::new(
            "UPDATE oauth2_refresh_tokens 
             SET is_revoked = @is_revoked, revoked_at = @revoked_at, updated_at = @updated_at
             WHERE token_hash = @token_hash AND client_id = @client_id AND is_revoked = false"
        );

        refresh_statement.add_param("token_hash", &token_hash);
        refresh_statement.add_param("client_id", &client_id);
        refresh_statement.add_param("is_revoked", &true);
        refresh_statement.add_param("revoked_at", &Utc::now().to_rfc3339());
        refresh_statement.add_param("updated_at", &Utc::now().to_rfc3339());

        let refresh_result = tokio::time::timeout(
            std::time::Duration::from_secs(30),
            transaction.update(refresh_statement)
        ).await
        .map_err(|_| SpannerError::GRPC(tonic::Status::deadline_exceeded("Database operation timeout for refresh token revocation")))?
        .map_err(|e| e)?;

        total_revoked += refresh_result;
    }

            Ok(total_revoked)
        })
    }).await
        .map_err(|e: SpannerError| format!("Failed to commit revocation transaction: {}", e))?;

    tracing::info!(
        "Token revocation completed: {} (hint: {:?}) for client: {} (total tokens revoked: {})", 
        token_hash, token_type_hint, client_id, total_revoked.1
    );

    Ok(())
}

// ===== Authentication and Validation =====

async fn authenticate_client(
    client_id: &str,
    client_secret: Option<&str>,
    headers: &HeaderMap,
    state: &Arc<AppState>,
) -> Result<OAuth2Client, OAuth2Error> {
    // Get client from database
    let client = match get_oauth2_client(client_id, state).await {
        Ok(Some(client)) => {
            if !client.is_active {
                return Err(OAuth2Error::InvalidClient("Client is inactive".to_string()));
            }
            client
        }
        Ok(None) => {
            return Err(OAuth2Error::InvalidClient("Invalid client_id".to_string()));
        }
        Err(e) => {
            tracing::error!("Failed to get OAuth2 client: {}", e);
            return Err(OAuth2Error::ServerError("Database error".to_string()));
        }
    };

    // For public clients, no authentication required
    if !client.is_confidential {
        return Ok(client);
    }

    // For confidential clients, require authentication
    let provided_secret = match client_secret {
        Some(secret) => secret.to_string(),
        None => {
            // Try HTTP Basic authentication
            match headers.get("Authorization")
                .and_then(|auth| auth.to_str().ok())
                .and_then(|auth| auth.strip_prefix("Basic "))
                .and_then(|encoded| general_purpose::STANDARD.decode(encoded).ok())
                .and_then(|decoded| String::from_utf8(decoded).ok())
            {
                Some(credentials) => {
                    let parts: Vec<&str> = credentials.splitn(2, ':').collect();
                    if parts.len() == 2 && parts[0] == client_id {
                        parts[1].to_string()
                    } else {
                        return Err(OAuth2Error::InvalidClient("Invalid Basic authentication".to_string()));
                    }
                }
                None => {
                    return Err(OAuth2Error::InvalidClient("Client authentication required".to_string()));
                }
            }
        }
    };


    // Verify client secret
    let expected_hash = match &client.client_secret_hash {
        Some(hash) => hash,
        None => {
            return Err(OAuth2Error::InvalidClient("Client secret not configured".to_string()));
        }
    };

    if !verify_client_secret(&provided_secret, expected_hash) {
        return Err(OAuth2Error::InvalidClient("Invalid client credentials".to_string()));
    }

    Ok(client)
}

fn verify_client_secret(provided_secret: &str, expected_hash: &str) -> bool {
    // Use secure Argon2id password verification with migration support for legacy SHA256 hashes
    match verify_password_with_migration(provided_secret, expected_hash) {
        Ok(result) => {
            if result.needs_migration {
                tracing::warn!(
                    "Client secret uses legacy SHA256 hash and should be migrated to Argon2id. \
                     Consider updating the client secret."
                );
                // In a production system, you might want to:
                // 1. Log this event for security monitoring
                // 2. Store the new Argon2 hash: result.new_hash
                // 3. Send a notification to admins about the migration need
            }
            result.is_valid
        }
        Err(e) => {
            tracing::error!("Client secret verification failed: {}", e);
            false
        }
    }
}

fn verify_pkce_challenge(code_verifier: &str, code_challenge: &str, method: Option<&str>) -> bool {
    match method {
        Some("S256") => {
            let mut hasher = Sha256::new();
            hasher.update(code_verifier.as_bytes());
            let hash = hasher.finalize();
            let computed_challenge = general_purpose::URL_SAFE_NO_PAD.encode(hash);
            computed_challenge == code_challenge
        }
        Some("plain") | None => {
            code_verifier == code_challenge
        }
        _ => false,
    }
}

// ===== Token Generation =====

fn generate_authorization_code() -> String {
    let random_bytes: Vec<u8> = (0..32).map(|_| rand::random::<u8>()).collect();
    general_purpose::URL_SAFE_NO_PAD.encode(random_bytes)
}

async fn generate_access_token(user_id: &str, client_id: &str, scopes: &[String]) -> String {
    let now = Utc::now();
    let exp = now + TimeDelta::hours(1);
    
    let claims = AccessTokenClaims {
        sub: user_id.to_string(),
        aud: client_id.to_string(),
        iss: std::env::var("OAUTH2_ISSUER").unwrap_or_else(|_| "episteme-analysis-engine".to_string()),
        exp: exp.timestamp() as u64,
        iat: now.timestamp() as u64,
        nbf: now.timestamp() as u64,
        jti: Uuid::new_v4().to_string(),
        scope: scopes.join(" "),
        client_id: client_id.to_string(),
        token_type: "Bearer".to_string(),
    };

    // Use RS256 with RSA key management for production security
    let rsa_manager = get_rsa_key_manager();
    let manager = rsa_manager.read().await;
    
    if let Some(signing_key) = manager.get_current_signing_key() {
        // Create header with key ID for key rotation support
        let mut header = Header::new(Algorithm::RS256);
        header.kid = Some(signing_key.kid.clone());
        
        encode(
            &header,
            &claims,
            &signing_key.private_key,
        ).unwrap_or_else(|e| {
            tracing::error!("Failed to sign JWT token with RS256: {}", e);
            "error-generating-token".to_string()
        })
    } else {
        tracing::error!("No RSA signing key available for JWT token generation");
        // Fallback to HS256 if RSA keys are not available (should not happen in production)
        let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default-secret".to_string());
        
        encode(
            &Header::new(Algorithm::HS256),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        ).unwrap_or_else(|_| "error-generating-token".to_string())
    }
}

#[allow(dead_code)]
fn generate_refresh_token_simple() -> String {
    let random_bytes: Vec<u8> = (0..32).map(|_| rand::random::<u8>()).collect();
    general_purpose::URL_SAFE_NO_PAD.encode(random_bytes)
}

fn hash_token(token: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(token.as_bytes());
    let hash = hasher.finalize();
    general_purpose::STANDARD.encode(hash)
}

// ===== Rate Limiting =====

async fn check_token_rate_limit(client_id: &str, state: &Arc<AppState>, endpoint: &str) -> Result<(), OAuth2Error> {
    // Simple in-memory rate limiting for now
    // In production, use Redis or database-based rate limiting
    
    // Implement proper rate limiting based on client_id
    let rate_limit_key = format!("oauth2_rate_limit:{}:{}", client_id, endpoint);
    let _current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    // Use a sliding window rate limiter
    let rate_limit_window = 3600; // 1 hour window
    let max_requests = 1000; // Max 1000 requests per hour per client
    
    // In a real implementation, this would use Redis or a proper rate limiting service
    // For now, we'll use a simple in-memory check with audit logging
    if let Some(redis_pool) = &state.redis_pool {
        let mut conn = redis_pool.get().await.map_err(|e| {
            tracing::error!("Failed to connect to Redis for rate limiting: {}", e);
            OAuth2Error::ServerError("Rate limiting service unavailable".to_string())
        })?;
        
        let current_count: redis::RedisResult<i64> = (&mut *conn).get(&rate_limit_key).await;
        let count = current_count.unwrap_or(0);
        
        if count >= max_requests {
            // Log rate limit exceeded
            let _audit_event = AuditEventBuilder::new(AuditAction::OAuth2TokenRequest)
                .user_id(client_id)
                .resource("oauth2_endpoint", endpoint)
                .outcome(AuditOutcome::Failure)
                .severity(AuditSeverity::Warning)
                .metadata(serde_json::json!({
                    "rate_limit_exceeded": true,
                    "current_count": count,
                    "max_requests": max_requests,
                    "window_seconds": rate_limit_window
                }))
                .build();
            
            // Add audit logging for rate limit exceeded
            let audit_logger = AuditLogger::new(state.spanner_pool.clone());
            if let Err(e) = audit_logger.log_event(_audit_event).await {
                tracing::error!("Failed to log rate limit audit event: {}", e);
            }
            tracing::warn!("Rate limit exceeded for client: {}", client_id);
            
            return Err(OAuth2Error::TooManyRequests("Rate limit exceeded for client".to_string()));
        }
        
        // Increment counter with expiration
        redis::pipe()
            .incr(&rate_limit_key, 1)
            .expire(&rate_limit_key, rate_limit_window as i64)
            .query_async::<_, ()>(&mut *conn)
            .await
            .map_err(|e| {
                tracing::error!("Failed to update rate limit counter: {}", e);
                OAuth2Error::ServerError("Rate limiting service error".to_string())
            })?;
    } else {
        tracing::warn!("Redis not available for rate limiting - using basic time-based check");
        // Fallback to basic validation without persistence
        // In production, this should fail-safe to allow requests rather than block
    }
    
    Ok(())
}

// ===== Audit Logging =====

async fn audit_oauth2_event(
    state: &Arc<AppState>,
    action: AuditAction,
    user_id: &str,
    client_id: Option<&str>,
    outcome: AuditOutcome,
    description: &str,
) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    let mut builder = AuditEventBuilder::new(action)
        .user_id(user_id.to_string())
        .outcome(outcome)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "description": description,
        }));

    if let Some(client_id) = client_id {
        builder = builder.metadata(serde_json::json!({
            "description": description,
            "client_id": client_id,
        }));
    }

    let event = builder.build();

    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log OAuth2 audit event: {}", e);
    }
}

// ===== Session Management =====

/// Get authenticated user from session or headers
#[allow(dead_code)]
async fn get_user_from_session(headers: &HeaderMap, state: &Arc<AppState>) -> Option<String> {
    // Check for Authorization header with Bearer token
    if let Some(auth_header) = headers.get("authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            if let Some(token) = auth_str.strip_prefix("Bearer ") {
                if let Ok(user_id) = validate_session_token(token, state).await {
                    return Some(user_id);
                }
            }
        }
    }
    
    // Check for session cookie
    if let Some(cookie_header) = headers.get("cookie") {
        if let Ok(cookie_str) = cookie_header.to_str() {
            for cookie in cookie_str.split(';') {
                let cookie = cookie.trim();
                if let Some(session_id) = cookie.strip_prefix("session_id=") {
                    if let Ok(user_id) = validate_session_cookie(session_id, state).await {
                        return Some(user_id);
                    }
                }
            }
        }
    }
    
    None
}

/// Validate session token and return user ID
#[allow(dead_code)]
async fn validate_session_token(token: &str, _state: &Arc<AppState>) -> Result<String, OAuth2Error> {
    // In a real implementation, this would validate the session token
    // against the database or Redis session store
    if token.starts_with("session_") {
        Ok("authenticated-user".to_string())
    } else {
        Err(OAuth2Error::InvalidToken("Invalid session token".to_string()))
    }
}

/// Validate session cookie and return user ID
#[allow(dead_code)]
async fn validate_session_cookie(session_id: &str, _state: &Arc<AppState>) -> Result<String, OAuth2Error> {
    // In a real implementation, this would validate the session cookie
    // against the database or Redis session store
    if session_id.len() > 10 {
        Ok("authenticated-user".to_string())
    } else {
        Err(OAuth2Error::InvalidToken("Invalid session cookie".to_string()))
    }
}

// ===== ID Token Generation =====

/// Generate ID token for OpenID Connect
async fn generate_id_token(auth_code: &AuthorizationCode, _state: &Arc<AppState>) -> Result<String, OAuth2Error> {
    // Only generate ID token if OpenID scope is present
    if !auth_code.scopes.contains(&"openid".to_string()) {
        return Err(OAuth2Error::InvalidScope("OpenID scope not requested".to_string()));
    }
    
    let now = Utc::now();
    let exp = now + chrono::TimeDelta::try_hours(1).unwrap_or_default();
    
    let claims = serde_json::json!({
        "iss": "https://auth.episteme.dev", // Issuer
        "sub": auth_code.user_id,           // Subject (user ID)
        "aud": auth_code.client_id,         // Audience (client ID)
        "exp": exp.timestamp(),             // Expiration time
        "iat": now.timestamp(),             // Issued at
        "nbf": now.timestamp(),             // Not before
        "nonce": auth_code.nonce,           // Nonce (if provided in auth request)
        "auth_time": now.timestamp(),       // Authentication time
        "at_hash": auth_code.code,          // Access token hash (simplified)
    });
    
    // Sign the ID token
    let key_manager_arc = get_rsa_key_manager();
    let key_manager_guard = key_manager_arc.read().await;
    if let Some(signing_key) = key_manager_guard.get_current_signing_key() {
        let header = jsonwebtoken::Header::new(jsonwebtoken::Algorithm::RS256);
        jsonwebtoken::encode(&header, &claims, &signing_key.private_key)
            .map_err(|e| OAuth2Error::ServerError(format!("Failed to generate ID token: {}", e)))
    } else {
        Err(OAuth2Error::ServerError("No signing key available for ID token".to_string()))
    }
}

// ===== Refresh Token Logic =====

#[derive(Debug)]
struct RefreshTokenData {
    user_id: String,
    client_id: String,
    scopes: Vec<String>,
}

/// Handle refresh token validation and return token data
async fn handle_refresh_token_validation(
    refresh_token: &str, 
    client: &super::models::OAuth2Client, 
    _state: &Arc<AppState>
) -> Result<RefreshTokenData, OAuth2Error> {
    // Validate refresh token format and signature
    if refresh_token.len() < 32 {
        return Err(OAuth2Error::InvalidGrant("Invalid refresh token format".to_string()));
    }
    
    // In a real implementation, this would:
    // 1. Look up the refresh token in the database
    // 2. Validate it hasn't expired
    // 3. Ensure it belongs to the client
    // 4. Mark the old refresh token as used (rotation)
    
    // For now, simulate a valid refresh token
    let token_data = RefreshTokenData {
        user_id: "user_123".to_string(),
        client_id: client.client_id.clone(),
        scopes: vec!["read".to_string(), "write".to_string()],
    };
    
    // Audit the refresh token usage
    let _audit_event = AuditEventBuilder::new(AuditAction::OAuth2TokenRefresh)
        .user_id(&client.client_id)
        .resource("oauth2_token", "refresh_token")
        .outcome(AuditOutcome::Success)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "user_id": &token_data.user_id,
            "scopes": &token_data.scopes
        }))
        .build();
    
    // Add audit logging for token refresh
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    if let Err(e) = audit_logger.log_event(_audit_event).await {
        tracing::error!("Failed to log token refresh audit event: {}", e);
    }
    tracing::info!("OAuth2 token refresh completed for user");
    
    Ok(token_data)
}

/// Generate refresh token with enhanced security
fn generate_refresh_token(user_id: &str, client_id: &str) -> String {
    let timestamp = Utc::now().timestamp();
    let nonce = uuid::Uuid::new_v4().to_string();
    let data = format!("{}:{}:{}:{}", user_id, client_id, timestamp, nonce);
    
    // Create a more secure refresh token
    let mut hasher = Sha256::new();
    hasher.update(data.as_bytes());
    let hash = hasher.finalize();
    
    format!("rt_{}", general_purpose::URL_SAFE_NO_PAD.encode(hash))
}