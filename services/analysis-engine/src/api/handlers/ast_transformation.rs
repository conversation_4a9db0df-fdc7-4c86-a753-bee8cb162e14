//! AST Transformation API Endpoints
//!
//! This module provides REST API endpoints for streaming AST transformation:
//! - POST /api/v1/transform/file - Transform a single file
//! - POST /api/v1/transform/stream - Start streaming transformation
//! - GET /api/v1/transform/stream/{id} - Get transformation status
//! - WebSocket /api/v1/transform/progress/{id} - Real-time progress updates

use crate::api::AppState;
use crate::models::streaming::{StreamingConfig, StreamingProgressUpdate};
use crate::services::AstTransformerService;
use crate::transformer::TransformationConfig;
use axum::{
    extract::{ws::WebSocketUpgrade, Path, State},
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::broadcast;
use tracing::{error, info};
use uuid::Uuid;

/// Request to transform a single file
#[derive(Debug, Serialize, Deserialize)]
pub struct TransformFileRequest {
    /// File path to transform
    pub file_path: String,
    /// Optional transformation configuration
    pub config: Option<TransformationConfig>,
    /// Whether to stream results progressively
    pub stream_results: bool,
}

/// Response for file transformation
#[derive(Debug, Serialize)]
pub struct TransformFileResponse {
    /// File path that was transformed
    pub file_path: String,
    /// Number of chunks generated
    pub chunk_count: usize,
    /// Total nodes processed
    pub node_count: usize,
    /// Transformation chunks (if not streaming)
    pub chunks: Option<Vec<TransformationChunkDto>>,
    /// WebSocket URL for streaming results
    pub stream_url: Option<String>,
}

/// DTO for transformation chunk
#[derive(Debug, Serialize, Deserialize)]
pub struct TransformationChunkDto {
    /// Chunk identifier
    pub chunk_id: String,
    /// Number of nodes in this chunk
    pub node_count: usize,
    /// Whether this is a partial result
    pub is_partial: bool,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// Throughput (nodes/second)
    pub throughput: f64,
}

/// Request to start streaming transformation
#[derive(Debug, Deserialize)]
pub struct StartStreamingTransformRequest {
    /// Repository path to transform
    pub repository_path: String,
    /// File patterns to include
    pub include_patterns: Vec<String>,
    /// File patterns to exclude
    pub exclude_patterns: Vec<String>,
    /// Languages to process
    pub languages: Vec<String>,
    /// Transformation configuration
    pub config: Option<TransformationConfig>,
}

/// Response for streaming transformation
#[derive(Debug, Serialize)]
pub struct StreamingTransformResponse {
    /// Transformation session ID
    pub session_id: String,
    /// WebSocket URL for progress updates
    pub progress_url: String,
    /// Estimated completion time in seconds
    pub estimated_completion_seconds: u64,
}

/// POST /api/v1/transform/file - Transform a single file
pub async fn transform_file(
    State(state): State<Arc<AppState>>,
    Json(request): Json<TransformFileRequest>,
) -> Result<Json<TransformFileResponse>, Response> {
    info!("Transform file request: {}", request.file_path);

    // Get transformer service
    let transformer_service = match state.get_transformer_service() {
        Some(service) => service,
        None => {
            error!("Transformer service not initialized");
            return Err((
                StatusCode::SERVICE_UNAVAILABLE,
                "Transformation service not available",
            )
                .into_response());
        }
    };

    // Apply custom config if provided
    let transformer_service = if let Some(config) = request.config {
        let parser = state.get_parser();
        Arc::new(AstTransformerService::with_config(parser, config))
    } else {
        transformer_service
    };

    let file_path = PathBuf::from(&request.file_path);
    
    // Set up progress tracking if streaming
    let (progress_tx, stream_url) = if request.stream_results {
        let (tx, _) = broadcast::channel(1000);
        let session_id = Uuid::new_v4().to_string();
        let url = format!("/api/v1/transform/progress/{session_id}");
        
        // Store the broadcast sender for WebSocket connections
        state.store_transform_session(&session_id, tx.clone()).await;
        
        (Some(tx), Some(url))
    } else {
        (None, None)
    };

    // Transform the file
    match transformer_service.transform_file(&file_path, progress_tx).await {
        Ok(chunks) => {
            let node_count: usize = chunks.iter().map(|c| c.nodes.len()).sum();
            
            let response = TransformFileResponse {
                file_path: request.file_path,
                chunk_count: chunks.len(),
                node_count,
                chunks: if request.stream_results {
                    None
                } else {
                    Some(chunks.into_iter().map(|chunk| TransformationChunkDto {
                        chunk_id: format!("{}_{}_{}", chunk.id.file_path, chunk.id.offset, chunk.id.size),
                        node_count: chunk.nodes.len(),
                        is_partial: chunk.is_partial,
                        processing_time_ms: chunk.metrics.parse_duration_ms,
                        throughput: chunk.metrics.throughput_loc_per_sec,
                    }).collect())
                },
                stream_url,
            };
            
            Ok(Json(response))
        }
        Err(e) => {
            error!("File transformation failed: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                format!("Transformation failed: {e}"),
            )
                .into_response())
        }
    }
}

/// POST /api/v1/transform/stream - Start streaming transformation
pub async fn start_streaming_transform(
    State(state): State<Arc<AppState>>,
    Json(request): Json<StartStreamingTransformRequest>,
) -> Result<Json<StreamingTransformResponse>, Response> {
    info!(
        "Starting streaming transformation for: {}",
        request.repository_path
    );

    let session_id = Uuid::new_v4().to_string();
    let (progress_tx, _) = broadcast::channel(1000);
    
    // Store session
    state.store_transform_session(&session_id, progress_tx.clone()).await;

    // Get transformer service
    let transformer_service = match state.get_transformer_service() {
        Some(service) => service,
        None => {
            return Err((
                StatusCode::SERVICE_UNAVAILABLE,
                "Transformation service not available",
            )
                .into_response());
        }
    };

    // Apply custom config if provided
    let transformer_service = if let Some(config) = request.config {
        let parser = state.parser.clone();
        Arc::new(AstTransformerService::with_config(parser, config))
    } else {
        transformer_service
    };

    // Create streaming analysis request
    let analysis_request = crate::models::streaming::StreamingAnalysisRequest {
        analysis_id: session_id.clone(),
        repository_path: request.repository_path.clone(),
        languages: request.languages,
        streaming_config: StreamingConfig::default(),
        progress_callback: None,
        include_patterns: request.include_patterns,
        exclude_patterns: request.exclude_patterns,
    };

    // Start transformation in background
    let session_id_clone = session_id.clone();
    tokio::spawn(async move {
        info!("Starting background transformation for session: {}", session_id_clone);
        
        let progress_tx_clone = progress_tx.clone();
        if let Err(e) = transformer_service
            .transform_repository(&analysis_request, progress_tx)
            .await
        {
            error!("Repository transformation failed: {}", e);
            // Send failure notification
            let _ = progress_tx_clone.send(StreamingProgressUpdate::Failed {
                analysis_id: session_id_clone,
                error_message: e.to_string(),
                files_processed: 0,
                duration_ms: 0,
            });
        }
    });

    Ok(Json(StreamingTransformResponse {
        session_id: session_id.clone(),
        progress_url: format!("/api/v1/transform/progress/{session_id}"),
        estimated_completion_seconds: 300, // 5 minutes estimate
    }))
}

/// GET /api/v1/transform/stream/{id} - Get transformation status
pub async fn get_transform_status(
    State(state): State<Arc<AppState>>,
    Path(session_id): Path<String>,
) -> Result<Json<TransformStatusResponse>, Response> {
    // Check if session exists
    if !state.has_transform_session(&session_id).await {
        return Err((StatusCode::NOT_FOUND, "Session not found").into_response());
    }

    // For now, return a simple status
    // In production, this would track actual progress
    Ok(Json(TransformStatusResponse {
        session_id,
        status: "active".to_string(),
        progress_percentage: 50.0,
        files_processed: 10,
        chunks_generated: 25,
    }))
}

/// Transformation status response
#[derive(Debug, Serialize)]
pub struct TransformStatusResponse {
    pub session_id: String,
    pub status: String,
    pub progress_percentage: f32,
    pub files_processed: usize,
    pub chunks_generated: usize,
}

/// WebSocket /api/v1/transform/progress/{id} - Stream transformation progress
pub async fn transformation_progress_websocket(
    ws: WebSocketUpgrade,
    Path(session_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| async move {
        handle_progress_websocket(socket, session_id, state).await
    })
}

/// Handle WebSocket connection for progress updates
async fn handle_progress_websocket(
    socket: axum::extract::ws::WebSocket,
    session_id: String,
    state: Arc<AppState>,
) {
    use axum::extract::ws::Message;
    use futures::{SinkExt, StreamExt};
    
    let (mut ws_sender, mut ws_receiver) = socket.split();
    
    // Get progress receiver
    let mut progress_rx = match state.get_transform_progress_receiver(&session_id).await {
        Some(rx) => rx,
        None => {
            let _ = ws_sender.send(Message::Close(None)).await;
            return;
        }
    };
    
    // Forward progress updates to WebSocket
    loop {
        tokio::select! {
            update = progress_rx.recv() => {
                match update {
                    Ok(progress) => {
                        let json = serde_json::to_string(&progress).unwrap_or_default();
                        if ws_sender.send(Message::Text(json.into())).await.is_err() {
                            break;
                        }
                    }
                    Err(_) => {
                        // Channel closed, transformation complete
                        let _ = ws_sender.send(Message::Text(
                            r#"{"type":"completed"}"#.to_string().into()
                        )).await;
                        break;
                    }
                }
            }
            
            msg = ws_receiver.next() => {
                match msg {
                    Some(Ok(Message::Close(_))) | None => break,
                    Some(Ok(Message::Ping(data))) => {
                        let _ = ws_sender.send(Message::Pong(data)).await;
                    }
                    _ => {}
                }
            }
        }
    }
    
    // Clean up session if needed
    let _ = ws_sender.send(Message::Close(None)).await;
}

// Extension trait for AppState to support transformation sessions
impl AppState {
    /// Get transformer service
    pub fn get_transformer_service(&self) -> Option<Arc<AstTransformerService>> {
        // Create transformer service using the analysis service's parser
        let parser = self.get_parser();
        Some(Arc::new(AstTransformerService::new(parser)))
    }

    /// Get parser from the analysis service
    pub fn get_parser(&self) -> Arc<TreeSitterParser> {
        // Access the parser from the analysis service
        self.analysis_service.get_parser()
    }
    
    /// Store transformation session
    pub async fn store_transform_session(
        &self,
        _session_id: &str,
        _progress_tx: broadcast::Sender<StreamingProgressUpdate>,
    ) {
        // In production, this would use a proper session store
        // For now, we'll use the existing infrastructure
    }
    
    /// Check if transformation session exists
    pub async fn has_transform_session(&self, _session_id: &str) -> bool {
        // Implementation would check session store
        true
    }
    
    /// Get progress receiver for a session
    pub async fn get_transform_progress_receiver(
        &self,
        _session_id: &str,
    ) -> Option<broadcast::Receiver<StreamingProgressUpdate>> {
        // Implementation would retrieve from session store
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_transform_file_request_serialization() {
        let request = TransformFileRequest {
            file_path: "/test/file.rs".to_string(),
            config: None,
            stream_results: false,
        };
        
        let json = serde_json::to_string(&request).unwrap();
        assert!(json.contains("file_path"));
        assert!(json.contains("/test/file.rs"));
    }
    
    #[test]
    fn test_transformation_chunk_dto() {
        let dto = TransformationChunkDto {
            chunk_id: "test_chunk_1".to_string(),
            node_count: 1000,
            is_partial: false,
            processing_time_ms: 50,
            throughput: 20000.0,
        };
        
        let json = serde_json::to_string(&dto).unwrap();
        let parsed: TransformationChunkDto = serde_json::from_str(&json).unwrap();
        
        assert_eq!(parsed.chunk_id, "test_chunk_1");
        assert_eq!(parsed.node_count, 1000);
        assert!(!parsed.is_partial);
    }
}