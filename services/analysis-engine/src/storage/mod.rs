pub mod cache;
pub mod connection_pool;
pub mod gcp_clients;
pub mod operations;
pub mod pubsub;
pub mod redis_client;
pub mod spanner;
// pub mod sql_validation; // Temporarily disabled

// Security storage modules (optional feature)
#[cfg(feature = "security-storage")]
pub mod encryption;

#[cfg(feature = "security-storage")]
pub mod audit;

#[cfg(feature = "security-storage")]
pub mod access_control;

// Test modules
#[cfg(test)]
pub mod tests;

// Re-export the wrapper types with clear names
pub use cache::CacheManager;
pub use operations::StorageOperations;
pub use pubsub::PubSubOperations;
pub use redis_client::RedisClient;
pub use spanner::SpannerOperations;

// Re-export the actual GCP clients from gcp_clients module
