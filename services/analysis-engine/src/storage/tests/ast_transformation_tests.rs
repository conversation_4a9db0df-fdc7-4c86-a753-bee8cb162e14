//! Unit tests for AST transformation functionality

#[cfg(test)]
mod tests {
    use crate::api::AppState;
    use crate::services::analyzer::AnalysisService;
    use crate::services::ast_transformer_service::AstTransformerService;
    use crate::config::ServiceConfig;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_get_parser_method() {
        // Test that the get_parser method works correctly
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        
        // Create analysis service
        let analysis_service = AnalysisService::new(
            None, // spanner_pool
            Arc::new(crate::storage::StorageOperations::new(config.clone()).await.expect("Failed to create storage")),
            Arc::new(crate::storage::PubSubOperations::new(config.clone()).await.expect("Failed to create pubsub")),
            Arc::new(crate::storage::CacheManager::new(config.clone()).await.expect("Failed to create cache")),
            config.clone(),
        ).await.expect("Failed to create analysis service");

        // Test get_parser method
        let parser = analysis_service.get_parser();
        assert!(!Arc::ptr_eq(&parser, &parser)); // Just ensure we get a valid Arc

        // Test get_parser_for_language method
        let rust_parser = analysis_service.get_parser_for_language("rust");
        assert!(rust_parser.is_ok(), "Should be able to get Rust parser");
    }

    #[tokio::test]
    async fn test_app_state_get_transformer_service() {
        // Test that AppState can create transformer service
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        
        // Create minimal AppState for testing
        let analysis_service = AnalysisService::new(
            None, // spanner_pool
            Arc::new(crate::storage::StorageOperations::new(config.clone()).await.expect("Failed to create storage")),
            Arc::new(crate::storage::PubSubOperations::new(config.clone()).await.expect("Failed to create pubsub")),
            Arc::new(crate::storage::CacheManager::new(config.clone()).await.expect("Failed to create cache")),
            config.clone(),
        ).await.expect("Failed to create analysis service");

        // Create a mock AppState with just the analysis service
        struct MockAppState {
            analysis_service: Arc<AnalysisService>,
        }

        impl MockAppState {
            fn get_transformer_service(&self) -> Option<Arc<AstTransformerService>> {
                let parser = self.analysis_service.get_parser();
                Some(Arc::new(AstTransformerService::new(parser)))
            }

            fn get_parser(&self) -> Arc<crate::parser::TreeSitterParser> {
                self.analysis_service.get_parser()
            }
        }

        let mock_state = MockAppState {
            analysis_service: Arc::new(analysis_service),
        };

        // Test get_transformer_service
        let transformer_service = mock_state.get_transformer_service();
        assert!(transformer_service.is_some(), "Should be able to create transformer service");

        // Test get_parser
        let parser = mock_state.get_parser();
        assert!(!Arc::ptr_eq(&parser, &parser)); // Just ensure we get a valid Arc
    }

    #[test]
    fn test_ast_transformer_service_creation() {
        // Test that AstTransformerService can be created with a parser
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        let parser = Arc::new(
            crate::parser::TreeSitterParser::new(config)
                .expect("Failed to create parser")
        );

        let transformer_service = AstTransformerService::new(parser);
        
        // Test that the service was created successfully
        // We can't test much without actual transformation, but we can verify creation
        assert_eq!(
            transformer_service.get_strategy(),
            crate::transformer::streaming::StreamingStrategy::Automatic
        );
    }

    #[test]
    fn test_parser_language_support() {
        // Test that the parser supports expected languages
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        let parser = Arc::new(
            crate::parser::TreeSitterParser::new(config)
                .expect("Failed to create parser")
        );

        // Test some common languages
        let languages = vec!["rust", "python", "javascript", "typescript"];
        
        for language in languages {
            let result = parser.get_parser_for_language(language);
            assert!(
                result.is_ok(),
                "Should support language: {}",
                language
            );
        }
    }

    #[tokio::test]
    async fn test_analysis_service_parser_integration() {
        // Test that AnalysisService properly integrates with parser
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        
        let analysis_service = AnalysisService::new(
            None, // spanner_pool
            Arc::new(crate::storage::StorageOperations::new(config.clone()).await.expect("Failed to create storage")),
            Arc::new(crate::storage::PubSubOperations::new(config.clone()).await.expect("Failed to create pubsub")),
            Arc::new(crate::storage::CacheManager::new(config.clone()).await.expect("Failed to create cache")),
            config.clone(),
        ).await.expect("Failed to create analysis service");

        // Test that we can get parsers for different languages
        let rust_parser = analysis_service.get_parser_for_language("rust");
        assert!(rust_parser.is_ok(), "Should get Rust parser");

        let python_parser = analysis_service.get_parser_for_language("python");
        assert!(python_parser.is_ok(), "Should get Python parser");

        // Test that we can get the main parser
        let main_parser = analysis_service.get_parser();
        assert!(!Arc::ptr_eq(&main_parser, &main_parser)); // Just ensure we get a valid Arc

        // Test that the parser can be used to create transformer service
        let transformer_service = AstTransformerService::new(main_parser);
        assert_eq!(
            transformer_service.get_strategy(),
            crate::transformer::streaming::StreamingStrategy::Automatic
        );
    }

    // Note: Integration tests with actual file transformation would require
    // test files and more complex setup. These unit tests focus on the
    // structural correctness of the parser integration.
}
