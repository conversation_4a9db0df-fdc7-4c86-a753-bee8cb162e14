//! Unit tests for streaming processor cache integration

#[cfg(test)]
mod tests {
    use crate::services::analyzer::streaming_processor::StreamingProcessor;
    use crate::config::ServiceConfig;
    use crate::storage::CacheManager;
    use crate::parser::TreeSitterParser;
    use crate::models::FileAnalysis;
    use std::sync::Arc;
    use std::path::Path;
    use tempfile::TempDir;
    use tokio::fs;

    async fn create_test_streaming_processor() -> StreamingProcessor {
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load config"));
        
        let parser = Arc::new(
            TreeSitterParser::new(config.clone())
                .expect("Failed to create parser")
        );
        
        let cache_manager = Arc::new(
            CacheManager::new(config.clone())
                .await
                .expect("Failed to create cache manager")
        );

        StreamingProcessor::new(parser, None, cache_manager)
    }

    async fn create_test_file(dir: &TempDir, name: &str, content: &str) -> std::path::PathBuf {
        let file_path = dir.path().join(name);
        fs::write(&file_path, content).await.expect("Failed to write test file");
        file_path
    }

    #[tokio::test]
    async fn test_file_hash_calculation() {
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let test_file = create_test_file(&temp_dir, "test.rs", "fn main() {}").await;

        // Test hash calculation
        let hash1 = StreamingProcessor::calculate_file_hash(&test_file).await;
        assert!(hash1.is_ok(), "Should be able to calculate file hash");

        let hash2 = StreamingProcessor::calculate_file_hash(&test_file).await;
        assert!(hash2.is_ok(), "Should be able to calculate file hash again");

        // Same file should produce same hash
        assert_eq!(hash1.unwrap(), hash2.unwrap(), "Same file should produce same hash");

        // Different content should produce different hash
        let test_file2 = create_test_file(&temp_dir, "test2.rs", "fn different() {}").await;
        let hash3 = StreamingProcessor::calculate_file_hash(&test_file2).await;
        assert!(hash3.is_ok(), "Should be able to calculate hash for different file");
        
        let hash1_again = StreamingProcessor::calculate_file_hash(&test_file).await.unwrap();
        assert_ne!(hash1_again, hash3.unwrap(), "Different files should produce different hashes");
    }

    #[tokio::test]
    async fn test_cache_integration() {
        let processor = create_test_streaming_processor().await;
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let test_file = create_test_file(&temp_dir, "test.rs", "fn main() {}").await;

        // Calculate hash for cache key
        let file_hash = StreamingProcessor::calculate_file_hash(&test_file).await
            .expect("Should be able to calculate file hash");

        // Initially, cache should be empty
        let cached_result = processor.check_cache(&test_file, &file_hash).await;
        assert!(cached_result.is_none(), "Cache should be empty initially");

        // Create a mock file analysis
        let mock_analysis = FileAnalysis {
            path: test_file.to_string_lossy().to_string(),
            language: "rust".to_string(),
            size_bytes: Some(12),
            lines_of_code: Some(1),
            symbols: vec![],
            chunks: vec![],
            imports: vec![],
            exports: vec![],
            dependencies: vec![],
            metrics: None,
            ast: None,
            embeddings: None,
            error: None,
            processing_time_ms: Some(10),
            parser_version: Some("1.0.0".to_string()),
        };

        // Cache the analysis
        processor.cache_analysis(&test_file, &file_hash, &mock_analysis).await;

        // Now cache should contain the analysis
        let cached_result = processor.check_cache(&test_file, &file_hash).await;
        assert!(cached_result.is_some(), "Cache should contain the analysis");

        let cached_analysis = cached_result.unwrap();
        assert_eq!(cached_analysis.path, mock_analysis.path);
        assert_eq!(cached_analysis.language, mock_analysis.language);
        assert_eq!(cached_analysis.size_bytes, mock_analysis.size_bytes);
    }

    #[tokio::test]
    async fn test_cache_stats() {
        let processor = create_test_streaming_processor().await;

        // Test getting cache stats
        let stats_result = processor.get_cache_stats().await;
        assert!(stats_result.is_ok(), "Should be able to get cache stats");

        let stats = stats_result.unwrap();
        // Just verify we get some stats structure back
        assert!(stats.hits >= 0, "Cache hits should be non-negative");
        assert!(stats.misses >= 0, "Cache misses should be non-negative");
    }

    #[tokio::test]
    async fn test_cache_pattern_clearing() {
        let processor = create_test_streaming_processor().await;
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let test_file = create_test_file(&temp_dir, "test.rs", "fn main() {}").await;

        // Calculate hash and cache something
        let file_hash = StreamingProcessor::calculate_file_hash(&test_file).await
            .expect("Should be able to calculate file hash");

        let mock_analysis = FileAnalysis {
            path: test_file.to_string_lossy().to_string(),
            language: "rust".to_string(),
            size_bytes: Some(12),
            lines_of_code: Some(1),
            symbols: vec![],
            chunks: vec![],
            imports: vec![],
            exports: vec![],
            dependencies: vec![],
            metrics: None,
            ast: None,
            embeddings: None,
            error: None,
            processing_time_ms: Some(10),
            parser_version: Some("1.0.0".to_string()),
        };

        processor.cache_analysis(&test_file, &file_hash, &mock_analysis).await;

        // Verify it's cached
        let cached_result = processor.check_cache(&test_file, &file_hash).await;
        assert!(cached_result.is_some(), "Analysis should be cached");

        // Clear cache with pattern
        let clear_result = processor.clear_cache_pattern("file_analysis:*").await;
        assert!(clear_result.is_ok(), "Should be able to clear cache pattern");

        // Verify it's no longer cached
        let cached_result_after = processor.check_cache(&test_file, &file_hash).await;
        assert!(cached_result_after.is_none(), "Analysis should no longer be cached");
    }

    #[test]
    fn test_cache_key_format() {
        // Test that cache key format is consistent
        let file_path = Path::new("/test/path/file.rs");
        let file_hash = "abcd1234";
        
        let expected_key = format!("file_analysis:{}:{}", file_path.display(), file_hash);
        assert_eq!(expected_key, "file_analysis:/test/path/file.rs:abcd1234");
    }

    #[tokio::test]
    async fn test_hash_consistency() {
        let temp_dir = TempDir::new().expect("Failed to create temp dir");
        let test_file = create_test_file(&temp_dir, "test.rs", "fn main() { println!(\"Hello\"); }").await;

        // Calculate hash multiple times
        let hash1 = StreamingProcessor::calculate_file_hash(&test_file).await.unwrap();
        let hash2 = StreamingProcessor::calculate_file_hash(&test_file).await.unwrap();
        let hash3 = StreamingProcessor::calculate_file_hash(&test_file).await.unwrap();

        assert_eq!(hash1, hash2, "Hash should be consistent");
        assert_eq!(hash2, hash3, "Hash should be consistent");
        assert!(!hash1.is_empty(), "Hash should not be empty");
        assert_eq!(hash1.len(), 64, "SHA256 hash should be 64 characters");
    }
}
