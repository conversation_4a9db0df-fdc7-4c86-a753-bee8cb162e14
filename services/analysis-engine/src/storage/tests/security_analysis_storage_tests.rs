//! Unit tests for security analysis storage functionality

#[cfg(test)]
mod tests {
    use super::super::SpannerStorage;
    use crate::models::security::*;
    use chrono::Utc;
    use std::sync::Arc;
    use uuid::Uuid;

    /// Create a mock security analysis result for testing
    fn create_mock_security_analysis_result() -> SecurityAnalysisResult {
        let analysis_id = Uuid::new_v4().to_string();
        
        SecurityAnalysisResult {
            analysis_id: analysis_id.clone(),
            vulnerabilities: vec![
                SecurityVulnerability {
                    vulnerability_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.clone(),
                    cve_id: Some("CVE-2023-1234".to_string()),
                    cwe_id: Some("CWE-79".to_string()),
                    vulnerability_type: VulnerabilityType::CrossSiteScripting,
                    severity: SecuritySeverity::High,
                    confidence_score: 0.95,
                    file_path: "src/main.rs".to_string(),
                    line_start: Some(42),
                    line_end: Some(45),
                    code_snippet: Some("let user_input = request.get_param(\"input\");".to_string()),
                    description: "Potential XSS vulnerability detected".to_string(),
                    remediation_advice: Some("Sanitize user input before rendering".to_string()),
                    owasp_category: Some("A03:2021 – Injection".to_string()),
                    attack_vector: Some("Network".to_string()),
                    exploitability_score: Some(0.8),
                    impact_score: Some(0.7),
                    false_positive_probability: Some(0.05),
                    created_at: Utc::now(),
                    updated_at: None,
                }
            ],
            dependency_vulnerabilities: vec![
                DependencyVulnerability {
                    dependency_vuln_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.clone(),
                    dependency_name: "serde".to_string(),
                    dependency_version: "1.0.0".to_string(),
                    package_manager: PackageManager::Cargo,
                    cve_id: Some("CVE-2023-5678".to_string()),
                    vulnerability_source: VulnerabilitySource::Nvd,
                    severity: SecuritySeverity::Medium,
                    cvss_score: Some(6.5),
                    cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N".to_string()),
                    description: Some("Deserialization vulnerability".to_string()),
                    published_date: Some(Utc::now()),
                    last_modified_date: Some(Utc::now()),
                    affected_versions: vec!["1.0.0".to_string(), "1.0.1".to_string()],
                    patched_versions: vec!["1.0.2".to_string()],
                    workaround: Some("Update to version 1.0.2 or later".to_string()),
                    exploit_available: false,
                    proof_of_concept_available: false,
                    created_at: Utc::now(),
                    updated_at: None,
                }
            ],
            detected_secrets: vec![
                DetectedSecret {
                    secret_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.clone(),
                    secret_type: SecretType::ApiKey,
                    file_path: "config/settings.toml".to_string(),
                    line_number: Some(15),
                    secret_hash: Some("sha256:abcd1234...".to_string()),
                    entropy_score: Some(4.2),
                    pattern_name: "Generic API Key".to_string(),
                    confidence_score: 0.9,
                    is_false_positive: false,
                    is_test_data: false,
                    severity: SecuritySeverity::High,
                    context: Some("api_key = \"sk-1234567890abcdef\"".to_string()),
                    masked_value: Some("sk-****...cdef".to_string()),
                    created_at: Utc::now(),
                    updated_at: None,
                }
            ],
            compliance_violations: vec![
                ComplianceViolation {
                    violation_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.clone(),
                    compliance_framework: ComplianceFramework::Gdpr,
                    rule_id: "GDPR-7.1".to_string(),
                    rule_name: "Data Processing Consent".to_string(),
                    violation_type: "Missing consent mechanism".to_string(),
                    severity: SecuritySeverity::Medium,
                    file_path: Some("src/user_data.rs".to_string()),
                    line_number: Some(100),
                    description: "Personal data processing without explicit consent".to_string(),
                    remediation_guidance: Some("Implement consent collection mechanism".to_string()),
                    compliance_category: Some("Data Protection".to_string()),
                    risk_rating: RiskRating::Medium,
                    business_impact: Some("Potential regulatory fines".to_string()),
                    technical_debt_hours: Some(8.0),
                    created_at: Utc::now(),
                    updated_at: None,
                }
            ],
            security_assessment: SecurityAssessment {
                assessment_id: Uuid::new_v4().to_string(),
                analysis_id: analysis_id.clone(),
                overall_security_score: 75.5,
                vulnerability_score: 70.0,
                dependency_score: 80.0,
                secrets_score: 60.0,
                compliance_score: 85.0,
                risk_level: RiskLevel::Medium,
                total_vulnerabilities: 1,
                critical_vulnerabilities: 0,
                high_vulnerabilities: 1,
                medium_vulnerabilities: 0,
                low_vulnerabilities: 0,
                total_secrets_found: 1,
                high_entropy_secrets: 1,
                compliance_violations_count: 1,
                security_debt_score: Some(25.0),
                improvement_recommendations: vec![
                    "Fix XSS vulnerability in main.rs".to_string(),
                    "Update serde dependency".to_string(),
                ],
                recommendations: vec![],
                detailed_findings: vec![],
                risk_matrix: None,
                trending_direction: TrendingDirection::Improving,
                created_at: Utc::now(),
                updated_at: None,
            },
            threat_models: vec![
                ThreatModel {
                    threat_model_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.clone(),
                    threat_category: ThreatCategory::InformationDisclosure,
                    threat_name: "Data Exposure via XSS".to_string(),
                    threat_description: "Attacker could steal user data via XSS".to_string(),
                    threat_actor: Some(ThreatActor::External),
                    attack_vector: Some("Web Application".to_string()),
                    asset_affected: Some("User Data".to_string()),
                    likelihood: Likelihood::Medium,
                    impact: Impact::High,
                    risk_score: 7.5,
                    mitigation_status: MitigationStatus::NotMitigated,
                    mitigation_measures: vec!["Input sanitization".to_string()],
                    mitigation_strategy: Some("Implement CSP and input validation".to_string()),
                    exploit_likelihood: Some(0.6),
                    business_impact: Some("Data breach, reputation damage".to_string()),
                    residual_risk_score: Some(3.0),
                    associated_vulnerabilities: vec!["vuln-123".to_string()],
                    created_at: Utc::now(),
                    updated_at: None,
                }
            ],
            metadata: SecurityIntelligenceMetadata {
                metadata_id: Uuid::new_v4().to_string(),
                analysis_id: analysis_id.clone(),
                threat_intel_sources: vec!["MITRE ATT&CK".to_string(), "CVE Database".to_string()],
                last_threat_intel_update: Some(Utc::now()),
                vulnerability_databases_used: vec!["NVD".to_string(), "GitHub Advisory".to_string()],
                ml_models_used: vec!["XSS Detector v1.0".to_string()],
                detection_rules_version: Some("2023.12.1".to_string()),
                false_positive_rate: Some(0.05),
                detection_accuracy: Some(0.95),
                scan_duration_ms: Some(5000),
                total_files_scanned: Some(150),
                total_dependencies_scanned: Some(25),
                created_at: Utc::now(),
                updated_at: None,
            },
        }
    }

    #[tokio::test]
    async fn test_security_analysis_storage_validation() {
        // Test that our mock data is valid
        let result = create_mock_security_analysis_result();
        
        // Validate structure
        assert!(!result.analysis_id.is_empty());
        assert_eq!(result.vulnerabilities.len(), 1);
        assert_eq!(result.dependency_vulnerabilities.len(), 1);
        assert_eq!(result.detected_secrets.len(), 1);
        assert_eq!(result.compliance_violations.len(), 1);
        assert_eq!(result.threat_models.len(), 1);
        
        // Validate vulnerability data
        let vuln = &result.vulnerabilities[0];
        assert_eq!(vuln.severity, SecuritySeverity::High);
        assert_eq!(vuln.vulnerability_type, VulnerabilityType::CrossSiteScripting);
        assert!(vuln.confidence_score > 0.0 && vuln.confidence_score <= 1.0);
        
        // Validate assessment data
        let assessment = &result.security_assessment;
        assert!(assessment.overall_security_score >= 0.0 && assessment.overall_security_score <= 100.0);
        assert_eq!(assessment.risk_level, RiskLevel::Medium);
    }

    #[tokio::test]
    async fn test_security_analysis_serialization() {
        let result = create_mock_security_analysis_result();
        
        // Test that all components can be serialized to JSON (for storage)
        let vuln_json = serde_json::to_string(&result.vulnerabilities[0]).unwrap();
        assert!(vuln_json.contains("vulnerability_id"));
        
        let dep_vuln_json = serde_json::to_string(&result.dependency_vulnerabilities[0]).unwrap();
        assert!(dep_vuln_json.contains("dependency_name"));
        
        let secret_json = serde_json::to_string(&result.detected_secrets[0]).unwrap();
        assert!(secret_json.contains("secret_type"));
        
        let assessment_json = serde_json::to_string(&result.security_assessment).unwrap();
        assert!(assessment_json.contains("overall_security_score"));
    }

    // Note: Integration tests with actual Spanner would require test database setup
    // For now, we focus on unit tests that validate data structure and serialization
}
