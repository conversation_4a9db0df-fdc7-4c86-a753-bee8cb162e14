//! Unit tests for access control functionality

#[cfg(test)]
mod tests {
    use super::super::super::access_control::*;
    use crate::models::security::{Permission, PermissionEffect};
    use chrono::Utc;

    #[test]
    fn test_permission_struct_initialization() {
        // Test that Permission struct can be properly initialized with all required fields
        let permission = Permission {
            id: "test_permission".to_string(),
            name: "Test Permission".to_string(),
            action: "read".to_string(),
            resource: "analysis:*".to_string(),
            effect: PermissionEffect::Allow,
            conditions: Some("user.role == 'admin'".to_string()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert_eq!(permission.id, "test_permission");
        assert_eq!(permission.name, "Test Permission");
        assert_eq!(permission.action, "read");
        assert_eq!(permission.resource, "analysis:*");
        assert_eq!(permission.effect, PermissionEffect::Allow);
        assert!(permission.conditions.is_some());
    }

    #[test]
    fn test_permission_default() {
        // Test that Permission::default() works correctly
        let permission = Permission::default();
        
        assert!(permission.id.is_empty());
        assert!(permission.name.is_empty());
        assert!(permission.action.is_empty());
        assert!(permission.resource.is_empty());
        assert_eq!(permission.effect, PermissionEffect::Allow);
        assert!(permission.conditions.is_none());
    }

    #[test]
    fn test_permission_serialization() {
        // Test that Permission can be serialized and deserialized
        let permission = Permission {
            id: "test_permission".to_string(),
            name: "Test Permission".to_string(),
            action: "read".to_string(),
            resource: "analysis:*".to_string(),
            effect: PermissionEffect::Allow,
            conditions: Some("user.role == 'admin'".to_string()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // Test JSON serialization
        let json = serde_json::to_string(&permission).unwrap();
        assert!(json.contains("test_permission"));
        assert!(json.contains("Test Permission"));
        assert!(json.contains("read"));
        assert!(json.contains("analysis:*"));

        // Test JSON deserialization
        let deserialized: Permission = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.id, permission.id);
        assert_eq!(deserialized.name, permission.name);
        assert_eq!(deserialized.action, permission.action);
        assert_eq!(deserialized.resource, permission.resource);
        assert_eq!(deserialized.effect, permission.effect);
    }

    #[test]
    fn test_permission_effect_enum() {
        // Test PermissionEffect enum values
        assert_eq!(PermissionEffect::Allow, PermissionEffect::Allow);
        assert_eq!(PermissionEffect::Deny, PermissionEffect::Deny);
        assert_ne!(PermissionEffect::Allow, PermissionEffect::Deny);
    }

    #[tokio::test]
    async fn test_access_context_creation() {
        // Test AccessContext creation
        let context = AccessContext {
            user_id: "user123".to_string(),
            user_roles: vec!["admin".to_string(), "user".to_string()],
            resource_type: "analysis".to_string(),
            resource_id: Some("analysis123".to_string()),
            action: "read".to_string(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-agent".to_string()),
            timestamp: Utc::now(),
            additional_context: std::collections::HashMap::new(),
        };

        assert_eq!(context.user_id, "user123");
        assert_eq!(context.user_roles.len(), 2);
        assert_eq!(context.resource_type, "analysis");
        assert_eq!(context.action, "read");
        assert!(context.resource_id.is_some());
    }

    #[test]
    fn test_access_decision_enum() {
        // Test AccessDecision enum values
        let allow = AccessDecision::Allow;
        let deny = AccessDecision::Deny("Access denied".to_string());
        let conditional = AccessDecision::Conditional(vec!["condition1".to_string()]);

        match allow {
            AccessDecision::Allow => assert!(true),
            _ => assert!(false, "Expected Allow variant"),
        }

        match deny {
            AccessDecision::Deny(reason) => assert_eq!(reason, "Access denied"),
            _ => assert!(false, "Expected Deny variant"),
        }

        match conditional {
            AccessDecision::Conditional(conditions) => {
                assert_eq!(conditions.len(), 1);
                assert_eq!(conditions[0], "condition1");
            }
            _ => assert!(false, "Expected Conditional variant"),
        }
    }

    #[test]
    fn test_security_policy_creation() {
        // Test SecurityPolicy creation with proper Permission initialization
        let permission = Permission {
            id: "test_permission".to_string(),
            name: "Test Permission".to_string(),
            action: "read".to_string(),
            resource: "analysis:*".to_string(),
            effect: PermissionEffect::Allow,
            conditions: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let policy = SecurityPolicy {
            policy_id: "test_policy".to_string(),
            resource_type: "analysis".to_string(),
            permissions: vec![permission],
            conditions: vec![],
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert_eq!(policy.policy_id, "test_policy");
        assert_eq!(policy.resource_type, "analysis");
        assert_eq!(policy.permissions.len(), 1);
        assert_eq!(policy.permissions[0].id, "test_permission");
    }

    // Note: Integration tests with actual storage would require test database setup
    // For now, we focus on unit tests that validate data structure and initialization
}
