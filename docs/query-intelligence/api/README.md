# 🔌 Query Intelligence API Documentation

**🔗 Navigation**: [↑ Query Intelligence](../README.md) | [↗ WebSocket API](websocket-api.md) | [↗ Integration Guide](../guides/integration-guide.md)

---

## ✅ **Live Service Status**
**Service**: FULLY OPERATIONAL since August 2025  
**Local Development**: `http://127.0.0.1:8002` (production-validated)
**Production URL**: `https://query-intelligence-************.us-central1.run.app`  
**Health Check**: `{"status":"healthy","timestamp":"2025-08-26T12:00:00Z"}`
**Features**: ✅ LLM Integration ✅ Secret Manager ✅ Multi-level Caching ✅ JWT Auth

## Table of Contents
- [🚀 Quick API Test](#quick-api-test)
- [✅ Working Endpoints](#working-endpoints)
- [Authentication](#authentication)
- [REST API](#rest-api)
- [WebSocket API](#websocket-api)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Request/Response Examples](#requestresponse-examples)
- [Programming Examples](#programming-examples)
- [Best Practices](#best-practices)

## 🚀 Quick API Test

### **Test Live Service**
```bash
# Verify service is running (local development)
curl http://127.0.0.1:8002/health
# ✅ Response: {"status":"healthy","timestamp":"2025-08-26T12:00:00Z"}

# Verify service is running (production)
curl https://query-intelligence-************.us-central1.run.app/health
# ✅ Response: {"status":"healthy","timestamp":"2025-08-26T12:00:00Z"}

# Test query endpoint (requires auth)
curl -X POST http://127.0.0.1:8002/api/v1/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "How does authentication work in this codebase?",
    "repository_id": "example-repo"
  }'
# ✅ Response: {"response":"...","confidence":0.95,"query_id":"..."}

# Test streaming WebSocket
wscat -c ws://127.0.0.1:8002/api/v1/ws/query \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## ✅ Working Endpoints

### **Operational API Endpoints** (Verified)
- `GET /health` - **✅ WORKING** - Service health check
- `GET /ready` - **✅ WORKING** - Readiness check with dependencies
- `GET /health/live` - **✅ WORKING** - Kubernetes liveness probe
- `POST /api/v1/query` - **✅ WORKING** - Natural language query processing
- `GET /api/v1/queries` - **✅ WORKING** - Query history with pagination
- `GET /api/v1/queries/{query_id}` - **✅ WORKING** - Retrieve specific query result
- `POST /api/v1/query/{query_id}/feedback` - **✅ WORKING** - Submit query feedback
- `GET /api/v1/languages` - **✅ WORKING** - Supported programming languages
- `GET /api/v1/version` - **✅ WORKING** - Service version information
- `GET /api/v1/sla/status` - **✅ WORKING** - SLA compliance monitoring
- `GET /metrics` - **✅ WORKING** - Prometheus metrics endpoint

### Base URLs
- **Local Development**: `http://127.0.0.1:8002/api/v1` ✅ **OPERATIONAL**
- **Production**: `https://query-intelligence-************.us-central1.run.app/api/v1` ✅ **OPERATIONAL**
- **Health Endpoints**: Direct service URL (no /api/v1 prefix)
- **WebSocket**: `ws://127.0.0.1:8002/api/v1/ws/` (local) or `wss://...` (production)

### Content Types
- Request: `application/json`
- Response: `application/json`
- WebSocket: `text` (JSON-encoded messages)

## Authentication

The API uses JWT-based authentication with multiple options:

### 1. JWT Bearer Token (Recommended)
```http
Authorization: Bearer <jwt-token>
```

JWT tokens include comprehensive validation:
- HS256/RS256 algorithm support
- Token expiration and refresh validation
- Clock skew protection (5-minute tolerance)
- Comprehensive claim validation
- Session tracking and audit logging

Required JWT claims:
```json
{
  "sub": "user-id",
  "exp": **********,
  "iat": **********,
  "aud": "query-intelligence",
  "iss": "https://securetoken.google.com/your-project",
  "email": "<EMAIL>",
  "email_verified": true
}
```

### 2. Service Account Authentication
For service-to-service communication:
```http
Authorization: Bearer <service-account-token>
```

## REST API

### Health Endpoints

#### GET /health
Basic health check endpoint.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

#### GET /ready
Readiness probe that checks all dependencies.

**Response**
```json
{
  "status": "ready",
  "checks": {
    "redis": "ok",
    "analysis_engine": "ok",
    "pattern_mining": "ok",
    "genai_service": "ok"
  },
  "timestamp": "2025-07-14T12:00:00Z"
}
```

#### GET /health/live
Liveness probe for Kubernetes deployments.

**Response**
```json
{
  "status": "alive",
  "uptime_seconds": 3600,
  "timestamp": "2025-07-14T12:00:00Z"
}
```

### Core Query Endpoints

#### POST /api/v1/query
Process a natural language query about code.

**Request Body**
```json
{
  "query": "How does authentication work in this codebase?",
  "repository_id": "example-repo",
  "include_context": true,
  "include_references": true,
  "max_results": 10,
  "language_filter": ["python", "typescript"],
  "options": {
    "use_semantic_cache": true,
    "include_confidence_score": true,
    "stream_response": false
  }
}
```

**Parameters**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `query` | string | Yes | Natural language query (max 10,000 chars) |
| `repository_id` | string | Yes | Repository identifier |
| `include_context` | boolean | No | Include code context (default: true) |
| `include_references` | boolean | No | Include code references (default: true) |
| `max_results` | integer | No | Maximum results to return (default: 10) |
| `language_filter` | string[] | No | Filter by programming languages |
| `options.use_semantic_cache` | boolean | No | Use semantic caching (default: true) |
| `options.include_confidence_score` | boolean | No | Include confidence score (default: true) |
| `options.stream_response` | boolean | No | Stream response via WebSocket (default: false) |

**Response** (200 OK)
```json
{
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "response": "Authentication in this codebase is handled through JWT tokens...",
  "confidence": 0.95,
  "intent": "authentication_inquiry",
  "language": "python",
  "references": [
    {
      "file_path": "src/auth/middleware.py",
      "line_number": 42,
      "code_snippet": "def authenticate_user(token: str) -> User:",
      "relevance_score": 0.92
    }
  ],
  "follow_up_questions": [
    "How are JWT tokens generated?",
    "What happens when authentication fails?"
  ],
  "processing_time_ms": 85,
  "cached": true,
  "timestamp": "2025-07-14T12:00:00Z"
}
```

#### GET /api/v1/queries/{query_id}
Retrieve a previous query result.

**Response**
```json
{
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "original_query": "How does authentication work?",
  "response": "Authentication in this codebase...",
  "confidence": 0.95,
  "created_at": "2025-07-14T12:00:00Z",
  "status": "completed"
}
```

#### GET /api/v1/queries
Get query history with pagination.

**Query Parameters**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `limit` | integer | No | Number of results per page (default: 20) |
| `offset` | integer | No | Pagination offset (default: 0) |
| `repository_id` | string | No | Filter by repository |
| `start_date` | string | No | Filter by date range (ISO 8601) |
| `end_date` | string | No | Filter by date range (ISO 8601) |

**Response**
```json
{
  "queries": [
    {
      "query_id": "550e8400-e29b-41d4-a716-446655440000",
      "query": "How does authentication work?",
      "repository_id": "example-repo",
      "timestamp": "2025-07-14T12:00:00Z",
      "processing_time_ms": 187,
      "result_count": 15
    }
  ],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 0,
    "has_next": true
  }
}
```

#### GET /api/v1/languages
List supported programming languages.

**Response**
```json
{
  "languages": [
    {
      "name": "python",
      "display_name": "Python",
      "file_extensions": [".py"],
      "supported_features": ["syntax_analysis", "semantic_search"]
    },
    {
      "name": "typescript",
      "display_name": "TypeScript",
      "file_extensions": [".ts", ".tsx"],
      "supported_features": ["syntax_analysis", "semantic_search", "type_analysis"]
    }
  ],
  "total_count": 15
}
```

#### GET /api/v1/version
Get service version information.

**Response**
```json
{
  "version": "2.0.0",
  "build_date": "2025-07-14",
  "commit_hash": "abc123def456",
  "environment": "production",
  "features": {
    "semantic_cache": true,
    "websocket_streaming": true,
    "multi_language_support": true
  }
}
```

### Feedback & Improvement

#### POST /api/v1/query/{query_id}/feedback
Submit feedback for a query result to improve the service.

**Request Body**
```json
{
  "rating": 5,
  "helpful": true,
  "comment": "Very accurate results",
  "result_feedback": [
    {
      "result_index": 0,
      "relevant": true,
      "helpful": true
    }
  ]
}
```

**Parameters**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `rating` | integer | Yes | Rating from 1-5 stars |
| `helpful` | boolean | Yes | Whether the overall response was helpful |
| `comment` | string | No | Optional feedback comment |
| `result_feedback` | array | No | Feedback for individual results |

**Response**
```json
{
  "feedback_id": "f_550e8400-e29b-41d4-a716-446655440000",
  "status": "recorded",
  "message": "Thank you for your feedback",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

### Monitoring & Operations

#### GET /metrics
Prometheus metrics endpoint for monitoring.

**Response** (Prometheus format)
```
# HELP query_intelligence_requests_total Total number of requests
# TYPE query_intelligence_requests_total counter
query_intelligence_requests_total{method="POST",endpoint="/api/v1/query",status="200"} 1234

# HELP query_intelligence_response_time_seconds Response time in seconds
# TYPE query_intelligence_response_time_seconds histogram
query_intelligence_response_time_seconds_bucket{le="0.1"} 456
query_intelligence_response_time_seconds_bucket{le="0.5"} 890
query_intelligence_response_time_seconds_bucket{le="1.0"} 950

# HELP query_intelligence_cache_hits_total Total cache hits
# TYPE query_intelligence_cache_hits_total counter
query_intelligence_cache_hits_total 2345
```

#### GET /api/v1/sla/status
Get current SLA compliance status.

**Response**
```json
{
  "sla_status": {
    "availability": {
      "current_value": 99.95,
      "target": 99.9,
      "status": "compliant",
      "unit": "percent"
    },
    "response_time_p95": {
      "current_value": 187,
      "target": 500,
      "status": "compliant",
      "unit": "ms"
    },
    "error_rate": {
      "current_value": 0.12,
      "target": 1.0,
      "status": "compliant",
      "unit": "percent"
    }
  },
  "overall_status": "compliant",
  "measurement_period": "last_24_hours",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

## WebSocket API

### Connection
```javascript
const ws = new WebSocket('wss://query-intelligence-************.us-central1.run.app/ws/query/{query_id}');
```

### Authentication
WebSocket connections require JWT authentication:
```javascript
const ws = new WebSocket('wss://query-intelligence-************.us-central1.run.app/ws/query/{query_id}', {
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
});
```

### Message Format

**Query Processing Update**
```json
{
  "type": "processing",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "analyzing",
  "progress": 45.5,
  "message": "Analyzing code patterns...",
  "timestamp": "2025-07-14T12:00:30Z"
}
```

**Partial Response**
```json
{
  "type": "partial_response",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "content": "Authentication in this codebase is handled...",
  "confidence": 0.85,
  "timestamp": "2025-07-14T12:00:45Z"
}
```

**Complete Response**
```json
{
  "type": "complete",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "response": "Complete response here...",
  "confidence": 0.95,
  "references": [...],
  "processing_time_ms": 1250,
  "timestamp": "2025-07-14T12:01:00Z"
}
```

**Error**
```json
{
  "type": "error",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "error": "Query processing failed",
  "error_code": "PROCESSING_ERROR",
  "timestamp": "2025-07-14T12:00:30Z"
}
```

### Client Example
```javascript
const queryId = "550e8400-e29b-41d4-a716-446655440000";
const ws = new WebSocket(`wss://query-intelligence-************.us-central1.run.app/ws/query/${queryId}`);

ws.onopen = () => {
  console.log('Connected to query stream');
};

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  
  switch (update.type) {
    case 'processing':
      updateProgress(update.progress);
      updateStatus(update.message);
      break;
    
    case 'partial_response':
      appendToResponse(update.content);
      break;
    
    case 'complete':
      showFinalResponse(update.response);
      showReferences(update.references);
      break;
    
    case 'error':
      handleError(update.error);
      break;
  }
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

ws.onclose = () => {
  console.log('Disconnected from query stream');
};
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please retry after 3600 seconds.",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "reset_at": "2025-07-14T13:00:00Z"
    }
  },
  "request_id": "req_123456",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `QUERY_TOO_LONG` | 400 | Query exceeds maximum length |
| `PROCESSING_ERROR` | 500 | Query processing failed |
| `SERVICE_UNAVAILABLE` | 503 | Temporary service issue |
| `TIMEOUT` | 504 | Request timeout |

## Rate Limiting

Rate limits are enforced per user/token:

| Tier | Requests/Hour | Concurrent Queries | Max Query Length |
|------|---------------|-------------------|------------------|
| Free | 100 | 1 | 1,000 chars |
| Pro | 1,000 | 5 | 5,000 chars |
| Team | 10,000 | 20 | 10,000 chars |
| Enterprise | Unlimited | Unlimited | 50,000 chars |

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 950
X-RateLimit-Reset: **********
```

## Request/Response Examples

### Example 1: Simple Query
```bash
curl -X POST https://query-intelligence-************.us-central1.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Show me all authentication functions",
    "repository_id": "my-repo"
  }'
```

### Example 2: Advanced Query with Filters
```bash
curl -X POST https://query-intelligence-************.us-central1.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How do I handle database connections?",
    "repository_id": "my-repo",
    "language_filter": ["python"],
    "max_results": 5,
    "include_context": true,
    "options": {
      "use_semantic_cache": true,
      "include_confidence_score": true
    }
  }'
```

### Example 3: Streaming Query
```bash
curl -X POST https://query-intelligence-************.us-central1.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Explain the entire authentication flow",
    "repository_id": "my-repo",
    "options": {
      "stream_response": true
    }
  }'
# Response includes websocket_url for streaming
```

## Programming Examples

### Python Client
```python
import requests
import json
from typing import Dict, List, Optional

class QueryIntelligenceClient:
    def __init__(self, base_url: str, jwt_token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {jwt_token}",
            "Content-Type": "application/json"
        }
    
    def query_code(self, query: str, repository_id: str, **options) -> Dict:
        """Execute a code query with proper error handling."""
        payload = {
            "query": query,
            "repository_id": repository_id,
            **options
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/query",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Query failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Error details: {e.response.text}")
            raise
    
    def get_query_history(self, limit: int = 20, offset: int = 0) -> Dict:
        """Get query history with pagination."""
        params = {"limit": limit, "offset": offset}
        response = requests.get(
            f"{self.base_url}/api/v1/queries",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def submit_feedback(self, query_id: str, rating: int, helpful: bool, comment: str = None) -> Dict:
        """Submit feedback for a query result."""
        payload = {
            "rating": rating,
            "helpful": helpful
        }
        if comment:
            payload["comment"] = comment
        
        response = requests.post(
            f"{self.base_url}/api/v1/query/{query_id}/feedback",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()

# Usage example
client = QueryIntelligenceClient(
    base_url="https://query-intelligence-************.us-central1.run.app",
    jwt_token="your-jwt-token"
)

# Simple query
result = client.query_code(
    query="How does authentication work?",
    repository_id="example-repo"
)

print(f"Found {len(result['references'])} references")
print(f"Summary: {result['response']}")
print(f"Confidence: {result['confidence']}")

# Advanced query with options
result = client.query_code(
    query="Find performance bottlenecks in database queries",
    repository_id="example-repo",
    include_context=True,
    max_results=30,
    language_filter=["python", "typescript"]
)
```

### TypeScript/JavaScript Client
```typescript
interface QueryOptions {
  include_context?: boolean;
  include_references?: boolean;
  max_results?: number;
  language_filter?: string[];
  options?: {
    use_semantic_cache?: boolean;
    include_confidence_score?: boolean;
    stream_response?: boolean;
  };
}

interface QueryResult {
  query_id: string;
  response: string;
  confidence: number;
  references: Array<{
    file_path: string;
    line_number: number;
    code_snippet: string;
    relevance_score: number;
  }>;
  follow_up_questions: string[];
  processing_time_ms: number;
  cached: boolean;
  timestamp: string;
}

class QueryIntelligenceClient {
  private baseUrl: string;
  private headers: Record<string, string>;
  
  constructor(baseUrl: string, jwtToken: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
    this.headers = {
      'Authorization': `Bearer ${jwtToken}`,
      'Content-Type': 'application/json'
    };
  }
  
  async queryCode(query: string, repositoryId: string, options?: QueryOptions): Promise<QueryResult> {
    const payload = {
      query,
      repository_id: repositoryId,
      ...options
    };
    
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/query`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(`API Error ${response.status}: ${error.error?.message || 'Unknown error'}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Query failed:', error);
      throw error;
    }
  }
  
  async getQueryHistory(limit: number = 20, offset: number = 0): Promise<any> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString()
    });
    
    const response = await fetch(`${this.baseUrl}/api/v1/queries?${params}`, {
      headers: this.headers
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch query history: ${response.status}`);
    }
    
    return await response.json();
  }
  
  async submitFeedback(queryId: string, rating: number, helpful: boolean, comment?: string): Promise<any> {
    const payload = {
      rating,
      helpful,
      ...(comment && { comment })
    };
    
    const response = await fetch(`${this.baseUrl}/api/v1/query/${queryId}/feedback`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to submit feedback: ${response.status}`);
    }
    
    return await response.json();
  }
}

// WebSocket streaming client
class QueryStreamingClient {
  private ws: WebSocket;
  private authenticated = false;
  
  constructor(baseUrl: string, token: string) {
    const wsUrl = baseUrl.replace(/^https?:\/\//, 'wss://');
    this.ws = new WebSocket(`${wsUrl}/ws/query/stream`);
    
    this.ws.onopen = () => {
      this.authenticate(token);
    };
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }
  
  private authenticate(token: string) {
    this.ws.send(JSON.stringify({
      type: 'auth',
      token
    }));
  }
  
  private handleMessage(message: any) {
    switch (message.type) {
      case 'auth_success':
        this.authenticated = true;
        break;
      case 'processing':
        this.onProcessing?.(message);
        break;
      case 'partial_response':
        this.onPartialResponse?.(message);
        break;
      case 'complete':
        this.onComplete?.(message);
        break;
      case 'error':
        this.onError?.(message);
        break;
    }
  }
  
  queryStream(query: string, repositoryId: string, options?: QueryOptions) {
    if (!this.authenticated) {
      throw new Error('Not authenticated');
    }
    
    this.ws.send(JSON.stringify({
      type: 'query',
      data: {
        query,
        repository_id: repositoryId,
        stream: true,
        ...options
      }
    }));
  }
  
  // Event handlers (set these to handle streaming events)
  onProcessing?: (message: any) => void;
  onPartialResponse?: (message: any) => void;
  onComplete?: (message: any) => void;
  onError?: (message: any) => void;
}

// Usage example
const client = new QueryIntelligenceClient(
  'https://query-intelligence-************.us-central1.run.app',
  'your-jwt-token'
);

// Simple query
const result = await client.queryCode(
  'How does authentication work?',
  'example-repo'
);

console.log(`Found ${result.references.length} references`);
console.log(`Summary: ${result.response}`);
console.log(`Confidence: ${result.confidence}`);
```

## Best Practices

### 1. Authentication & Security
- **Store JWT tokens securely** using encrypted storage or environment variables
- **Refresh tokens proactively** before expiration to avoid service interruption
- **Use HTTPS only** in production environments
- **Validate SSL certificates** and never skip certificate verification
- **Never log authentication tokens** in application logs or error messages
- **Use API keys for service accounts** only, not for user-facing applications

### 2. Error Handling & Resilience
- **Always check response status** before processing response data
- **Parse error responses** for detailed error information and retry guidance
- **Implement exponential backoff** for retries on 429 (rate limited) and 5xx errors
- **Set appropriate timeouts** (recommended: 30s for queries, 60s for large operations)
- **Handle network failures gracefully** with proper fallback mechanisms
- **Log errors with context** including request IDs for debugging

### 3. Performance Optimization
- **Use streaming for large result sets** to reduce perceived latency
- **Enable result caching** when appropriate to reduce API calls
- **Implement client-side caching** for frequently accessed data
- **Batch related queries** when possible to reduce round trips
- **Monitor rate limit headers** and adjust request frequency accordingly
- **Use connection pooling** for high-volume applications

### 4. Rate Limiting & Quotas
- **Monitor X-RateLimit headers** in responses to track usage
- **Implement exponential backoff** with jitter for 429 responses
- **Cache results locally** to reduce API calls for repeated queries
- **Use semantic caching** options to improve cache hit rates
- **Plan for rate limit tiers** based on expected usage patterns

### 5. Query Optimization
- **Write specific, focused queries** for better results and performance
- **Use language filters** to narrow search scope when possible
- **Set appropriate max_results** limits based on UI requirements
- **Enable confidence scoring** to filter low-quality results
- **Provide feedback** to help improve query accuracy over time

### 6. Monitoring & Observability
- **Track query performance** and success rates in your application
- **Monitor SLA compliance** using the `/api/v1/sla/status` endpoint
- **Set up alerts** for service availability and performance degradation
- **Use request IDs** for tracking and debugging issues
- **Implement health checks** that test actual query functionality

### 7. Development & Testing
- **Use staging environments** for development and testing
- **Implement comprehensive error handling** in your test suite
- **Test with realistic data** and query patterns
- **Validate against actual service responses** not just mock data
- **Use version endpoint** to track API changes and compatibility

---

## Related Documentation

- **[WebSocket API Documentation](websocket-api.md)** - Real-time streaming API
- **[Integration Guide](../guides/integration-guide.md)** - Complete integration patterns and best practices
- **[Developer Guide](../guides/developer-guide.md)** - Local development and testing setup
- **[Architecture Documentation](../architecture/README.md)** - System design and API architecture
- **[Performance Testing](../testing/performance-testing.md)** - API performance validation
- **[Security Testing](../testing/security-testing.md)** - API security validation

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**