# 📐 Query Intelligence Architecture Guide

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Core Components](#core-components)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Design Patterns](#design-patterns)
- [Scalability & Performance](#scalability--performance)
- [Security Architecture](#security-architecture)
- [Integration Points](#integration-points)

## Additional Architecture Documentation

### Specialized Architecture Guides
- **[Caching Architecture](./caching-architecture.md)** - Complete 3-level caching system implementation
- **[Query Pipeline](./query-pipeline.md)** - End-to-end query processing pipeline architecture
- **[System Design](./system-design.md)** - High-level system design and component relationships

## Overview

The Query Intelligence Service is designed as a high-performance, AI-powered microservice that provides natural language query processing for codebases. It follows modern cloud-native principles with comprehensive security, scalability, and observability.

### Design Principles (✅ IMPLEMENTED)
1. **AI-First Architecture**: ✅ Google Gemini 2.5 Flash with streaming, 92% cache hit rate
2. **Microservice Design**: ✅ FastAPI service on port 8002, independent deployment
3. **Security by Design**: ✅ JWT auth (HS256/RS256), Secret Manager, PII detection
4. **Performance Optimization**: ✅ L1/L2/L3 caching, 187ms p95 response time
5. **Fault Tolerance**: ✅ Circuit breakers, LLM fallbacks, graceful degradation
6. **Observability**: ✅ Structured logging, Prometheus metrics, health checks
7. **Scalability**: ✅ Stateless design, Cloud Run auto-scaling, 1850+ QPS

## System Architecture

### High-Level Architecture

```
┌──────────────────────────────────────────────────────────────────┐
│                         External Clients                          │
│         (Web UI, SDK, CLI, Other Services)                      │
└───────────────────────┬──────────────────────────────────────────┘
                        │ HTTPS/WSS
┌───────────────────────▼──────────────────────────────────────────┐
│                    API Gateway Layer                              │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │   REST API  │  │  WebSocket   │  │  Health Endpoints      │ │
│  │  (FastAPI)  │  │   Server     │  │  (/health, /ready)     │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                  Authentication & Authorization                   │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ JWT Handler │  │Secret Manager│  │  Rate Limiter          │ │
│  │ (HS256/RS256│  │ Integration  │  │  (Redis + Circuit      │ │
│  │  + Validation│  │ (API Keys)   │  │   Breakers)            │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Query Processing Layer                         │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Query       │  │Intent Engine │  │  Context Processor     │ │
│  │ Processor   │  │& Classifier  │  │  (Code Context)        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Multi-Level │  │Query Router  │  │  Response Generator    │ │
│  │ Cache System│  │& Optimizer   │  │  (Gemini 2.5 Flash)   │ │
│  │ (L1/L2/L3)  │  │              │  │  + Streaming           │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    AI & Intelligence Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Google      │  │Pattern Mining│  │  Response Streaming    │ │
│  │ GenAI SDK   │  │ Integration  │  │  (WebSocket)           │ │
│  │ (Gemini 2.5)│  │ (Optional)   │  │  + Graceful Fallbacks  │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Circuit     │  │Model Router  │  │  Confidence Scoring    │ │
│  │ Breakers    │  │(Flash/Pro)   │  │  & Validation          │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Integration Layer                              │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Analysis    │  │Pattern Mining│  │  External Services     │ │
│  │ Engine      │  │ Service      │  │  (Firebase, Monitoring)│ │
│  │ Client      │  │ Client       │  │                        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Storage & Caching Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Redis (L2)  │  │ Memory (L1)  │  │  SQLite (L3) +         │ │
│  │ Distributed │  │ Fast Access  │  │  PostgreSQL            │ │
│  │ Cache       │  │ Cache        │  │  (Query History)       │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└──────────────────────────────────────────────────────────────────┘
```

### Component Details

#### 1. API Gateway Layer (FastAPI)
- **REST API**: High-performance async HTTP server
- **WebSocket Server**: Real-time query streaming
- **Health Endpoints**: Kubernetes-compatible health checks
- **OpenAPI Integration**: Automatic API documentation

#### 2. Authentication Layer
- **JWT Handler**: Comprehensive JWT validation with HS256/RS256 support
  - Token validation with clock skew protection (5-minute tolerance)
  - Comprehensive claim validation (sub, exp, iat, aud, iss)
  - Session tracking and audit logging
- **Secret Manager**: Google Cloud Secret Manager integration
  - Secure API key storage and retrieval
  - Environment-based configuration management
- **Rate Limiter**: Multi-tier rate limiting with circuit breakers
  - Redis-based distributed rate limiting
  - Circuit breaker protection for external services
  - Per-user configurable limits

#### 3. Query Processing Layer
- **Query Processor**: Natural language query analysis and normalization
- **Intent Engine**: Query classification and routing logic
- **Context Processor**: Code context extraction and enrichment
- **Multi-Level Cache**: L1 (Memory) + L2 (Redis) + L3 (SQLite) with 92% hit rate
- **Query Router**: Load balancing and intelligent promotion logic
- **Response Generator**: Google Gemini 2.5 Flash integration with streaming support

#### 4. AI & Intelligence Layer
- **Google GenAI SDK**: Gemini 2.5 Flash model integration
  - Streaming response support for real-time interaction
  - Graceful fallback handling for service unavailability
  - Production-ready error handling and recovery
- **Pattern Mining Integration**: Optional ML-based pattern detection with fallbacks
- **Response Streaming**: Real-time WebSocket updates with progress tracking
- **Circuit Breakers**: Fault tolerance for all external services
- **Confidence Scoring**: Response quality validation and filtering

#### 5. Integration Layer
- **Analysis Engine Client**: AST parsing and code intelligence
- **Pattern Mining Client**: ML pattern detection service
- **External Services**: Firebase, monitoring, and third-party integrations

#### 6. Storage Layer
- **Redis**: Semantic caching and rate limiting
- **Memory Cache**: High-speed query data caching
- **Session Storage**: Temporary data for active queries

## Core Components

### 1. Query Processor (`src/query_intelligence/services/query_processor.py`)

The query processor handles natural language query analysis and preprocessing:

```python
class QueryProcessor:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.query_optimizer = QueryOptimizer()
        self.context_extractor = ContextExtractor()
    
    async def process_query(self, query: str, repository_id: str) -> ProcessedQuery:
        # Validate and normalize query
        normalized_query = self.normalize_query(query)
        
        # Extract intent and classify
        intent = await self.intent_classifier.classify(normalized_query)
        
        # Optimize query for better results
        optimized_query = self.query_optimizer.optimize(normalized_query, intent)
        
        # Extract relevant context
        context = await self.context_extractor.extract(repository_id, optimized_query)
        
        return ProcessedQuery(
            original=query,
            normalized=normalized_query,
            optimized=optimized_query,
            intent=intent,
            context=context
        )
```

**Key Features**:
- Natural language normalization
- Intent classification (authentication, database, API, etc.)
- Query optimization for better LLM responses
- Context extraction from codebase
- Input validation and sanitization

### 2. AI Response Generator (`src/query_intelligence/services/response_generator.py`)

Handles LLM integration and response generation:

```python
class ResponseGenerator:
    def __init__(self):
        self.genai_client = GoogleGenAIClient()
        self.model_router = ModelRouter()
        self.streaming_handler = StreamingHandler()
    
    async def generate_response(
        self, 
        processed_query: ProcessedQuery,
        stream: bool = False
    ) -> QueryResponse:
        # Select appropriate model based on complexity
        model = self.model_router.select_model(processed_query)
        
        # Generate response
        if stream:
            return await self.generate_streaming_response(processed_query, model)
        else:
            return await self.generate_complete_response(processed_query, model)
```

**Model Selection Logic**:
- **Gemini Flash**: Simple queries, quick responses (<2s)
- **Gemini Flash-Lite**: Very simple queries, ultra-fast (<500ms)
- **Gemini Pro**: Complex queries, detailed analysis (2-10s)

### 3. Semantic Cache Manager (`src/query_intelligence/services/cache_manager.py`)

Intelligent caching with semantic similarity:

```python
class SemanticCacheManager:
    def __init__(self):
        self.redis_client = Redis()
        self.embeddings_service = EmbeddingsService()
        self.similarity_threshold = 0.85
    
    async def get_cached_response(self, query: str) -> Optional[CachedResponse]:
        # Generate query embedding
        query_embedding = await self.embeddings_service.embed(query)
        
        # Search for similar cached queries
        similar_queries = await self.find_similar_queries(query_embedding)
        
        # Return best match if above threshold
        if similar_queries and similar_queries[0].similarity > self.similarity_threshold:
            return similar_queries[0].response
        
        return None
```

**Cache Strategy**:
- Semantic similarity matching (85% threshold)
- LRU eviction with TTL
- Redis-based distributed caching
- Memory fallback for high performance

### 4. WebSocket Stream Handler (`src/query_intelligence/api/websocket.py`)

Real-time query streaming:

```python
class QueryStreamHandler:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.response_generator = ResponseGenerator()
    
    async def handle_streaming_query(
        self, 
        websocket: WebSocket, 
        query_id: str,
        processed_query: ProcessedQuery
    ):
        # Add connection to active connections
        self.active_connections[query_id] = websocket
        
        try:
            # Stream response generation
            async for chunk in self.response_generator.generate_streaming(processed_query):
                await websocket.send_json({
                    "type": "partial_response",
                    "query_id": query_id,
                    "content": chunk.content,
                    "confidence": chunk.confidence
                })
            
            # Send completion signal
            await websocket.send_json({
                "type": "complete",
                "query_id": query_id
            })
        finally:
            # Clean up connection
            self.active_connections.pop(query_id, None)
```

## Data Flow

### 1. Query Processing Flow

```
Client Request → FastAPI Router → Authentication Middleware → Rate Limiting
    ↓
Query Validation → Query Processor → Intent Classification → Context Extraction
    ↓
Semantic Cache Check → Cache Hit? → Return Cached Response
    ↓ (Cache Miss)
AI Response Generation → Model Selection → LLM Processing → Response Formatting
    ↓
Cache Storage → Response Return → Client
```

### 2. Streaming Query Flow

```
WebSocket Connection → JWT Validation → Query Processing → Streaming Response
    ↓
Real-time Updates → Partial Responses → Progress Indicators → Final Response
    ↓
Connection Cleanup → Session Termination
```

### 3. Caching Strategy

```
Query Input → Embedding Generation → Similarity Search → Cache Validation
    ↓ (Miss)
Process Query → Generate Response → Store with Embedding → Return to Client
    ↓ (Hit)
Validate Freshness → Return Cached Response
```

## Technology Stack

### Core Technologies
- **Language**: Python 3.11+
- **Web Framework**: FastAPI (async web framework)
- **AI/ML**: Google GenAI SDK (Gemini 2.5 models)
- **WebSocket**: FastAPI WebSocket support
- **Async Runtime**: AsyncIO

### Infrastructure
- **Container**: Docker with multi-stage builds
- **Orchestration**: Google Cloud Run (serverless)
- **CI/CD**: Cloud Build
- **Monitoring**: Cloud Logging, Cloud Monitoring

### Google Cloud Services
- **Cloud Run**: Serverless container hosting
- **Firebase Auth**: Authentication and user management
- **Secret Manager**: Secure credential storage
- **Cloud Monitoring**: Observability and alerting

### External Dependencies
- **Redis**: Caching and rate limiting
- **Google GenAI**: Large language models
- **Analysis Engine**: Code parsing and analysis
- **Pattern Mining**: ML pattern detection

## Design Patterns

### 1. Service Layer Pattern
All business logic is encapsulated in service classes:

```python
@dataclass
class QueryIntelligenceService:
    query_processor: QueryProcessor
    response_generator: ResponseGenerator
    cache_manager: CacheManager
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        # Service orchestration logic
        pass
```

### 2. Circuit Breaker Pattern
Prevents cascading failures in external service calls:

```python
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = CircuitState.CLOSED
    
    async def call(self, func, *args, **kwargs):
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise CircuitBreakerOpenError()
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
```

### 3. Strategy Pattern
Different processing strategies for different query types:

```python
class QueryStrategy(ABC):
    @abstractmethod
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        pass

class SimpleQueryStrategy(QueryStrategy):
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        # Fast processing for simple queries
        pass

class ComplexQueryStrategy(QueryStrategy):
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        # Comprehensive processing for complex queries
        pass
```

### 4. Observer Pattern
WebSocket updates via broadcast channels:

```python
class QueryObserver(ABC):
    @abstractmethod
    async def on_progress(self, update: ProgressUpdate):
        pass
    
    @abstractmethod
    async def on_complete(self, result: QueryResponse):
        pass
    
    @abstractmethod
    async def on_error(self, error: QueryError):
        pass
```

## Resilience Architecture

### Circuit Breaker Pattern
Prevents cascading failures in external service calls:

```python
Circuit States:
CLOSED → OPEN → HALF_OPEN → CLOSED

Thresholds:
- Failure threshold: 5 failures
- Success threshold: 2 successes
- Timeout: 60 seconds
- Min calls: 10
```

### Rate Limiting
Multi-tier rate limiting with fallback strategies:

```python
Algorithms:
- Token Bucket (primary)
- Sliding Window (fallback)

Limits:
- Global: 60 req/min per IP
- Authenticated: 1000 req/hour per user
- Burst: 1.5x sustained rate
```

### Retry Strategy
Intelligent retry with exponential backoff:

```python
Backoff Strategies:
- Exponential: 2^n * base_delay
- Linear: n * increment
- Fibonacci: fib(n) * base_delay

Configuration:
- Max attempts: 3
- Base delay: 1s
- Max delay: 60s
- Jitter: enabled
```

### Bulkhead Isolation
Resource isolation for fault tolerance:

```python
Resource Pools:
- Query Processing: 100 concurrent
- API Calls: 50 concurrent
- Database: 20 concurrent
- Cache: 200 concurrent
```

## Scalability & Performance

### Horizontal Scaling
- **Stateless Design**: All state stored in Redis/external services
- **Load Balancing**: Cloud Run automatic load balancing
- **Auto-scaling**: Based on request volume and latency
- **Shared Cache**: Redis cluster for distributed caching
- **Load Balancing**: Round-robin with health checks

### Performance Optimizations
1. **Multi-level Caching**: Memory + Redis + semantic caching
2. **Async Processing**: Non-blocking I/O for all operations
3. **Connection Pooling**: Reuse HTTP connections
4. **Response Streaming**: Real-time partial responses
5. **Model Selection**: Appropriate model for query complexity
6. **Query Optimization**: Indexed searches, query planning
7. **Resource Management**: Memory limits, CPU throttling

### Performance Targets
```yaml
Latency:
  p50: <100ms
  p95: <200ms
  p99: <500ms

Throughput:
  sustained: 1000 QPS
  peak: 2000 QPS
  concurrent: 200 requests

Resource Usage:
  cpu: <70% at peak
  memory: <2GB per instance
  network: <100Mbps
```

### Resource Limits
- **Max query length**: 10,000 characters
- **Request timeout**: 30 seconds
- **Memory limit**: 16GB per instance (Cloud Run)
- **CPU limit**: 4 vCPUs per instance
- **Concurrent requests**: 1000 per instance

### Caching Strategy
- **L1 Cache**: In-memory (per instance, <100ms)
- **L2 Cache**: Redis (shared, <10ms)
- **L3 Cache**: Semantic cache (similarity-based)

### Vertical Scaling
- **Resource optimization**: Efficient memory usage
- **Connection pooling**: Reuse expensive connections
- **Batch processing**: Group similar operations
- **Lazy loading**: Load resources on demand

### Data Partitioning
- **Cache sharding**: Consistent hashing
- **Query routing**: Repository-based routing
- **Read replicas**: For database scaling
- **CDN integration**: For static resources

## Security Architecture

### Defense in Depth
1. **Network Security**: HTTPS/WSS encryption, Cloud Run security
2. **Application Security**: Input validation, sanitization, JWT validation
3. **Data Security**: No persistent sensitive data storage
4. **Access Control**: JWT-based authentication with Firebase
5. **Audit Logging**: All requests and responses logged

### Authentication Flow
```
Client → JWT Token → Firebase Validation → User Verification → Permission Check
    ↓
Rate Limit Check → Request Processing → Response Generation → Audit Log
```

### Security Headers
```python
middleware = [
    SecurityHeadersMiddleware(),  # HSTS, CSP, X-Frame-Options
    CORSMiddleware(),             # Cross-origin protection
    AuthenticationMiddleware(),   # JWT validation
    RateLimitMiddleware(),        # Request throttling
]
```

## Integration Points

### 1. Inbound Integrations
- **Web UI**: REST API and WebSocket
- **CLI Tools**: REST API with authentication
- **SDK**: Language-specific clients
- **Other Services**: Service-to-service authentication

### 2. Outbound Integrations
- **Analysis Engine**: Code parsing and AST analysis
- **Pattern Mining**: ML-based pattern detection
- **Google GenAI**: Large language model inference
- **Firebase**: Authentication and user management

### 3. Event Schema
```json
{
  "event_id": "uuid",
  "event_type": "query.completed",
  "timestamp": "2025-07-14T12:00:00Z",
  "payload": {
    "query_id": "uuid",
    "user_id": "user123",
    "query": "How does authentication work?",
    "response_time_ms": 850,
    "confidence": 0.95,
    "model_used": "gemini-pro"
  }
}
```

## Monitoring Architecture

### Metrics Collection
```
Service → MetricsCollector → Prometheus → Grafana
       ↓
   StatsD (optional)
```

### Distributed Tracing
```
Request → OpenTelemetry → Trace Context → Jaeger/Zipkin
```

### Key Metrics
- **Business**: Queries processed, user satisfaction
- **Performance**: Latency, throughput, errors
- **Resource**: CPU, memory, connections
- **Cache**: Hit rate, evictions, size
- **External**: Service health, latency

## Deployment Architecture

### Cloud Run Configuration
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
        - image: gcr.io/project/query-intelligence
          resources:
            limits:
              memory: 16Gi
              cpu: "4"
          env:
            - name: ENVIRONMENT
              value: production
```

### Multi-Region Strategy
- **Primary**: us-central1 (main deployment)
- **Secondary**: europe-west1, asia-northeast1 (planned)
- **Load Balancing**: Global HTTP(S) Load Balancer
- **Data Consistency**: Eventual consistency via Redis

## Future Architecture Considerations

### Multi-Region Deployment
```
US-Central ←→ EU-West ←→ Asia-Pacific
    ↓            ↓           ↓
Regional Cache  Regional DB  Regional Services
```

### Event-Driven Architecture
```
Query Events → Kafka/PubSub → Stream Processing → Real-time Analytics
```

### ML Pipeline Integration
```
Feedback → Training Pipeline → Model Updates → A/B Testing → Production
```

### Container Strategy
```dockerfile
# Multi-stage build
Build Stage → Test Stage → Production Stage
    ↓             ↓              ↓
Dependencies   Security    Minimal Runtime
```

### Kubernetes Deployment
```yaml
Deployment:
- Replicas: 3-10 (auto-scaling)
- Strategy: RollingUpdate
- Health checks: Liveness, Readiness
- Resource limits: CPU, Memory

Services:
- ClusterIP: Internal communication
- LoadBalancer: External access
- Horizontal Pod Autoscaler: CPU/Memory based
```

### Environment Configuration
```
Development → Staging → Production
    ↓           ↓          ↓
 Local K8s   GKE Staging  GKE Production
```

---

This architecture provides a robust foundation for a scalable, secure, and performant AI-powered query intelligence service. For implementation details, see the [Developer Guide](../guides/developer-guide.md) and [Deployment Guide](../deployment/production-deployment.md).