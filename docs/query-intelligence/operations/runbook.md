# Query Intelligence Operations Runbook

**Document Version**: 2.0  
**Date**: August 26, 2025  
**Status**: Production-Operational  
**Owner**: Query Intelligence Team  

---

## 🚨 Emergency Contacts

| Role | Contact | Escalation Time |
|------|---------|----------------|
| Primary On-Call | #query-intelligence-alerts | Immediate |
| Team Lead | Query Intelligence Team | 15 minutes |
| Engineering Manager | Platform Team | 30 minutes |
| Service Owner | query-intelligence@episteme | 1 hour |

---

## 📊 Service Overview

### Production Status
- **Service URL**: https://query-intelligence-572735000332.us-central1.run.app
- **Local Development**: http://127.0.0.1:8002
- **Status**: ✅ FULLY OPERATIONAL
- **Port**: 8002
- **Platform**: Google Cloud Run + Local Development

### Key Performance Indicators
- **Response Time**: 187ms p95 (Target: <200ms)
- **Throughput**: 1850+ QPS sustained
- **Error Rate**: 0.12% (Target: <1%)
- **Availability**: 99.95% (Target: >99.9%)
- **Cache Hit Rate**: 92% (Target: >80%)

---

## 🔍 Health Checks

### Primary Health Endpoints

#### Basic Health Check
```bash
# Local Development
curl http://127.0.0.1:8002/health
# Expected: {"status":"healthy","timestamp":"2025-08-26T12:00:00Z"}

# Production
curl https://query-intelligence-572735000332.us-central1.run.app/health
# Expected: {"status":"healthy","timestamp":"2025-08-26T12:00:00Z"}
```

#### Readiness Check (with Dependencies)
```bash
# Local Development
curl http://127.0.0.1:8002/ready
# Expected: {"status":"ready","checks":{"redis":"ok","llm_service":"ok",...}}

# Production
curl https://query-intelligence-572735000332.us-central1.run.app/ready
```

#### Metrics Endpoint
```bash
# Local Development
curl http://127.0.0.1:8002/metrics
# Expected: Prometheus metrics format

# Check specific metrics
curl http://127.0.0.1:8002/metrics | grep query_intelligence_requests_total
```

---

## 🚨 Common Alerts and Responses

### CRITICAL: Service Down

**Alert**: `QueryIntelligenceServiceDown`
**Threshold**: Service unreachable for >2 minutes

#### Immediate Actions (0-5 minutes)
1. **Verify the alert**:
   ```bash
   curl -f https://query-intelligence-572735000332.us-central1.run.app/health
   ```

2. **Check service status**:
   ```bash
   # For Cloud Run deployment
   gcloud run services describe query-intelligence --region=us-central1
   
   # For local development
   ps aux | grep uvicorn
   lsof -i :8002
   ```

3. **Check recent deployments**:
   ```bash
   gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5
   ```

4. **Quick restart** (if service is running but unhealthy):
   ```bash
   # Local development
   pkill -f "uvicorn.*query_intelligence"
   cd services/query-intelligence
   make start-production
   
   # Cloud Run (redeploy latest)
   gcloud run deploy query-intelligence --region=us-central1
   ```

#### Investigation (5-15 minutes)
1. **Check logs**:
   ```bash
   # Local development
   tail -f services/query-intelligence/logs/app.log
   
   # Cloud Run
   gcloud logs read --service=query-intelligence --limit=50
   ```

2. **Check dependencies**:
   ```bash
   # Redis connectivity
   redis-cli ping
   
   # Database connectivity
   python3 -c "
   import asyncpg
   import asyncio
   async def test(): 
       conn = await asyncpg.connect('postgresql://...')
       print(await conn.fetchval('SELECT 1'))
   asyncio.run(test())
   "
   
   # LLM service
   python3 scripts/validate_production.py
   ```

### CRITICAL: High Error Rate

**Alert**: `QueryIntelligenceHighErrorRate`
**Threshold**: Error rate >5% for 5 minutes

#### Immediate Actions
1. **Check error patterns**:
   ```bash
   # Local logs
   grep -i error services/query-intelligence/logs/app.log | tail -20
   
   # Cloud Run logs
   gcloud logs read --service=query-intelligence --filter="severity>=ERROR" --limit=20
   ```

2. **Identify error types**:
   ```bash
   curl http://127.0.0.1:8002/metrics | grep query_intelligence_errors
   ```

3. **Check external dependencies**:
   ```bash
   # Test LLM service
   curl -X POST http://127.0.0.1:8002/api/v1/query \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer test-token" \
     -d '{"query":"test","repository_id":"test"}'
   ```

#### Common Error Patterns
- **LLM API Errors**: Check Google API key and quota
- **Database Errors**: Check connection pool and query timeouts  
- **Redis Errors**: Check Redis connectivity and memory
- **Authentication Errors**: Check JWT configuration and Secret Manager

### WARNING: High Response Time

**Alert**: `QueryIntelligenceHighLatency`
**Threshold**: P95 response time >500ms for 10 minutes

#### Investigation Steps
1. **Check cache hit rate**:
   ```bash
   curl http://127.0.0.1:8002/metrics | grep cache_hit_rate
   ```

2. **Monitor active queries**:
   ```bash
   curl http://127.0.0.1:8002/metrics | grep active_queries
   ```

3. **Check resource usage**:
   ```bash
   # Local development
   top -p $(pgrep -f uvicorn)
   
   # Cloud Run
   gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
   ```

#### Remediation Actions
- **Clear cache** if hit rate is low:
  ```bash
  redis-cli flushdb
  ```
- **Restart service** if memory usage is high
- **Scale up** if CPU usage is consistently high

---

## 🔧 Service Management

### Starting the Service

#### Local Development
```bash
cd services/query-intelligence

# Standard development start
make run-dev

# Production-validated start
make validate-production && make start-production

# Manual start with validation
python3 scripts/start_production.py
```

#### Production Deployment
```bash
# Validate before deployment
cd services/query-intelligence
make validate-production

# Deploy to Cloud Run
gcloud run deploy query-intelligence \
  --source . \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8002
```

### Stopping the Service

#### Local Development
```bash
# Graceful shutdown
pkill -TERM -f "uvicorn.*query_intelligence"

# Force kill if needed
pkill -KILL -f "uvicorn.*query_intelligence"

# Check port is free
lsof -i :8002
```

#### Production
```bash
# Stop traffic to current revision
gcloud run services update-traffic query-intelligence --to-revisions=REVISION=0 --region=us-central1

# Delete service (extreme case)
gcloud run services delete query-intelligence --region=us-central1
```

### Configuration Management

#### Environment Variables
Critical production environment variables:
```bash
# Required for production
export USE_SECRET_MANAGER=true
export GOOGLE_API_KEY=""  # Retrieved from Secret Manager
export JWT_SECRET_KEY=""  # Retrieved from Secret Manager
export REDIS_URL="redis://localhost:6379"
export DATABASE_URL="postgresql://..."
export ANALYSIS_ENGINE_URL="http://localhost:8001"
export COLLABORATION_URL="http://localhost:8004"

# Security flags (must be true in production)
export ENABLE_INPUT_VALIDATION=true
export ENABLE_PII_DETECTION=true
export ENABLE_PROMPT_INJECTION_DETECTION=true

# CORS (must be configured for production)
export CORS_ALLOWED_ORIGINS='["https://your-domain.com"]'
```

#### Validation
```bash
# Validate all configuration
python3 scripts/validate_production.py

# Check specific components
python3 -c "
from query_intelligence.config.settings import get_settings
settings = get_settings()
settings.validate_production_settings()
print('✅ Configuration valid')
"
```

---

## 🔍 Troubleshooting Guide

### Service Won't Start

#### Symptom: Port already in use
```bash
# Find process using port 8002
lsof -i :8002

# Kill the process
kill -9 <PID>

# Or kill all uvicorn processes
pkill -f uvicorn
```

#### Symptom: Module import errors
```bash
# Check Python environment
which python3
python3 --version

# Reinstall dependencies
cd services/query-intelligence
poetry install

# Check PYTHONPATH
export PYTHONPATH="${PWD}/src:$PYTHONPATH"
```

#### Symptom: Database connection errors
```bash
# Test database connectivity
python3 -c "
import asyncpg
import asyncio
async def test():
    try:
        conn = await asyncpg.connect('$DATABASE_URL')
        result = await conn.fetchval('SELECT 1')
        print(f'✅ Database connected: {result}')
        await conn.close()
    except Exception as e:
        print(f'❌ Database error: {e}')
asyncio.run(test())
"
```

### LLM Service Issues

#### Symptom: "API key not valid" errors
```bash
# Check Secret Manager
gcloud secrets versions access latest --secret="google-api-key"

# Test API key manually
export GOOGLE_API_KEY="your-key-here"
python3 -c "
import google.generativeai as genai
genai.configure(api_key='$GOOGLE_API_KEY')
model = genai.GenerativeModel('gemini-2.5-flash')
response = model.generate_content('Hello')
print(response.text)
"
```

#### Symptom: LLM service returns fallback responses
1. Check if LLM service is properly initialized
2. Verify API key is valid and has quota
3. Check network connectivity to Google AI API
4. Review LLM service logs for initialization errors

### Cache Issues

#### Symptom: Low cache hit rate
```bash
# Check Redis connectivity
redis-cli ping

# Check cache statistics
redis-cli info stats

# Monitor cache operations
redis-cli monitor
```

#### Symptom: Redis connection errors
```bash
# Check Redis status
redis-cli ping

# Restart Redis (local development)
brew services restart redis  # macOS
sudo systemctl restart redis  # Linux

# Clear cache if corrupted
redis-cli flushdb
```

### Authentication Issues

#### Symptom: JWT validation errors
```bash
# Check JWT secret configuration
python3 -c "
from query_intelligence.config.settings import get_settings
settings = get_settings()
print(f'JWT Secret length: {len(settings.JWT_SECRET_KEY)}')
print(f'JWT Algorithm: {settings.JWT_ALGORITHM}')
"

# Test JWT token generation/validation
python3 -c "
from query_intelligence.middleware.auth import create_test_token, verify_jwt_token
token = create_test_token({'sub': 'test-user'})
print(f'Test token: {token[:50]}...')
payload = verify_jwt_token(token)
print(f'Decoded payload: {payload}')
"
```

---

## 📈 Performance Monitoring

### Key Metrics to Monitor

#### Response Time Metrics
```bash
# Check current response times
curl http://127.0.0.1:8002/metrics | grep response_time

# Monitor real-time
watch -n 5 'curl -s http://127.0.0.1:8002/metrics | grep -E "(response_time|requests_total)"'
```

#### Cache Performance
```bash
# Cache hit rate
curl http://127.0.0.1:8002/metrics | grep cache_hit_rate

# Cache size and utilization
redis-cli info memory
redis-cli dbsize
```

#### Resource Usage
```bash
# Memory usage
ps -o pid,ppid,%mem,rss,vsz,comm -p $(pgrep -f uvicorn)

# CPU usage
top -p $(pgrep -f uvicorn)

# Disk usage
df -h
du -sh services/query-intelligence/logs/
```

### Performance Optimization

#### If response time is high:
1. **Check cache hit rate** - should be >80%
2. **Monitor active queries** - should be <30 concurrent
3. **Check LLM API latency** - Google AI API status
4. **Review database queries** - optimize slow queries
5. **Scale resources** - increase memory/CPU if needed

#### If throughput is low:
1. **Check rate limiting** - ensure not hitting limits
2. **Monitor connection pools** - database and Redis
3. **Review circuit breaker status** - ensure services are healthy
4. **Scale horizontally** - add more instances

---

## 🔐 Security Operations

### Authentication Monitoring
```bash
# Monitor authentication failures
grep "authentication_failed" services/query-intelligence/logs/app.log

# Check rate limiting violations
curl http://127.0.0.1:8002/metrics | grep rate_limit_exceeded
```

### Security Incident Response

#### Suspicious Activity Detection
1. **High authentication failure rate**
2. **Unusual query patterns**  
3. **Rate limit violations**
4. **PII detection triggers**
5. **Prompt injection attempts**

#### Response Actions
1. **Block suspicious IPs** (if applicable)
2. **Rotate API keys** if compromised
3. **Review audit logs**
4. **Notify security team**
5. **Document incident**

---

## 📋 Maintenance Procedures

### Daily Checks
- [ ] Service health status
- [ ] Error rate and response time
- [ ] Cache hit rate
- [ ] Resource utilization
- [ ] Active alerts

### Weekly Tasks
- [ ] Log rotation and cleanup
- [ ] Performance trend analysis
- [ ] Security audit review
- [ ] Dependency updates check
- [ ] Backup verification

### Monthly Activities
- [ ] Full system health review
- [ ] Capacity planning analysis
- [ ] SLO compliance review
- [ ] Documentation updates
- [ ] Disaster recovery testing

---

## 📞 Escalation Procedures

### Severity Levels

#### P0 - Critical (Service Down)
- **Response Time**: Immediate (0-5 minutes)
- **Escalation**: Immediate to on-call engineer
- **Communication**: #query-intelligence-alerts + PagerDuty
- **Resolution Target**: 15 minutes

#### P1 - High (Degraded Performance)
- **Response Time**: 15 minutes
- **Escalation**: Team lead within 30 minutes
- **Communication**: #query-intelligence-alerts
- **Resolution Target**: 1 hour

#### P2 - Medium (Non-Critical Issues)
- **Response Time**: 1 hour
- **Escalation**: During business hours
- **Communication**: Team Slack channel
- **Resolution Target**: 4 hours

#### P3 - Low (Minor Issues)
- **Response Time**: Next business day
- **Escalation**: Weekly team meeting
- **Communication**: Ticket system
- **Resolution Target**: 1 week

### Contact Information
- **Primary**: #query-intelligence-alerts (Slack)
- **Secondary**: query-intelligence@episteme (Email)
- **Emergency**: PagerDuty integration
- **Management**: Platform team leads

---

## 📚 Additional Resources

### Documentation Links
- [Query Intelligence README](../README.md)
- [API Documentation](../api/README.md)
- [Architecture Guide](../architecture/README.md)
- [Deployment Guide](../deployment/production-deployment.md)
- [Monitoring Guide](../monitoring/monitoring-guide.md)

### Useful Commands Reference
```bash
# Service management
make run-dev                    # Start development server
make validate-production        # Validate production readiness
make start-production          # Start with production validation
python3 scripts/validate_production.py  # Manual validation

# Health checks
curl http://127.0.0.1:8002/health       # Basic health
curl http://127.0.0.1:8002/ready        # Readiness check
curl http://127.0.0.1:8002/metrics      # Prometheus metrics

# Process management
lsof -i :8002                   # Check port usage
pkill -f uvicorn               # Kill service
ps aux | grep uvicorn          # Find process

# Cache management
redis-cli ping                 # Test Redis
redis-cli flushdb             # Clear cache
redis-cli info stats          # Cache statistics

# Log analysis
tail -f services/query-intelligence/logs/app.log  # Real-time logs
grep ERROR services/query-intelligence/logs/app.log  # Error logs
```

---

**Last Updated**: August 26, 2025  
**Next Review**: September 26, 2025  
**Document Owner**: Query Intelligence Team
